<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>NFL Playoff Predictor Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1e40af;
            --primary-green: #059669;
            --accent-gold: #f59e0b;
            --dark-bg: #0f172a;
            --card-bg: #1e293b;
            --border-color: #334155;
        }

        html {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--dark-bg) 0%, #1e293b 100%);
            color: #e2e8f0;
            overflow-x: auto;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            width: 100% !important;
            max-width: none !important;
            -webkit-overflow-scrolling: touch;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            position: relative;
            touch-action: manipulation;
        }

        /* Full width for Google Sites and embedded contexts */
        html, body {
            width: 100% !important;
            max-width: none !important;
        }

        .container, .main-container, .app-container {
            width: 100% !important;
            max-width: none !important;
            margin: 0 !important;
            padding: 0 1rem !important;
        }

        .container {
            max-width: 100%;
            width: 100%;
            margin: 0;
            padding: 0 1rem;
        }

        /* Mobile Optimizations */
        @media (max-width: 768px) {
            body {
                position: relative;
                overflow-x: hidden;
                overflow-y: auto;
                height: auto;
                min-height: 100vh;
                font-size: 16px; /* Prevent zoom on input focus */
            }

            .main-content {
                padding-bottom: 2rem;
                min-height: calc(100vh - 4rem);
            }

            .container {
                padding: 0 0.75rem;
            }

            /* Mobile-friendly buttons */
            .btn-primary, .btn-secondary {
                min-height: 44px; /* iOS touch target minimum */
                padding: 0.875rem 1.25rem;
                font-size: 0.9rem;
            }

            /* Mobile game cards */
            .mobile-game-card {
                margin-bottom: 1rem;
                padding: 1rem;
                border-radius: 1rem;
                background: var(--card-bg);
                border: 1px solid var(--border-color);
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                transition: all 0.2s ease;
                touch-action: manipulation;
            }

            .mobile-game-card:active {
                transform: scale(0.98);
                box-shadow: 0 1px 5px rgba(0,0,0,0.3);
            }

            /* Mobile team selectors */
            .mobile-team {
                padding: 0.75rem;
                border-radius: 0.75rem;
                border: 2px solid transparent;
                transition: all 0.2s ease;
                cursor: pointer;
                min-height: 60px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                background: rgba(30, 41, 59, 0.5);
                margin: 0.5rem 0;
                touch-action: manipulation;
            }

            .mobile-team:active {
                transform: scale(0.98);
            }

            .mobile-team.winner {
                background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.1) 100%);
                border-color: var(--primary-green);
                box-shadow: 0 0 15px rgba(16, 185, 129, 0.3);
            }

            /* Mobile modal improvements */
            .modal {
                padding: 1rem;
                align-items: flex-start;
            }

            .modal-content {
                width: 95%;
                max-width: none;
                margin-top: 2rem;
                max-height: calc(100vh - 4rem);
                border-radius: 1rem;
            }

            /* Mobile week navigation */
            .week-tab {
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
                min-width: 44px;
                min-height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            /* Mobile playoff bracket */
            .playoff-bracket {
                padding: 0.5rem;
                gap: 0.5rem;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                scroll-snap-type: x mandatory;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }

            .playoff-bracket::-webkit-scrollbar {
                display: none;
            }

            .conference-bracket {
                min-width: 280px;
                scroll-snap-align: center;
                flex-shrink: 0;
            }

            .super-bowl-section {
                min-width: 160px;
                scroll-snap-align: center;
                flex-shrink: 0;
            }

            .playoff-matchup {
                min-height: 70px;
                width: 100px;
                padding: 0.75rem;
                font-size: 0.75rem;
            }

            /* Mobile sidebar improvements */
            .sidebar {
                position: relative;
                width: 100%;
                margin-bottom: 1rem;
                max-height: none;
                overflow: visible;
            }

            /* Mobile input improvements */
            input, select, textarea {
                font-size: 16px; /* Prevent zoom on iOS */
                min-height: 44px;
                padding: 0.75rem;
                border-radius: 0.5rem;
                border: 1px solid var(--border-color);
                background: var(--card-bg);
                color: #e2e8f0;
                touch-action: manipulation;
            }

            /* Mobile notification positioning */
            .notification {
                top: 1rem;
                right: 1rem;
                left: 1rem;
                max-width: none;
                transform: translateY(-100%);
            }

            .notification.show {
                transform: translateY(0);
            }

            /* Mobile injury items */
            .injury-item {
                padding: 1rem;
                margin-bottom: 0.75rem;
                border-radius: 0.75rem;
            }

            .injury-details-btn {
                min-height: 44px;
                padding: 0.75rem 1rem;
            }

            /* Mobile team logos */
            .team-logo {
                width: 32px;
                height: 32px;
                background: white !important;
                padding: 2px;
                border-radius: 50%;
            }

            /* Mobile standings */
            .standings-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            /* Mobile leaderboard */
            .leaderboard-entry {
                padding: 1rem;
                margin-bottom: 0.75rem;
                border-radius: 0.75rem;
                min-height: 60px;
                touch-action: manipulation;
            }

            .leaderboard-entry:active {
                transform: scale(0.98);
            }

            /* Smooth scrolling for mobile */
            html {
                scroll-behavior: smooth;
            }

            /* Prevent horizontal scroll bounce */
            body {
                overscroll-behavior-x: none;
            }

            /* Improve tap targets */
            .week-tab, .btn-primary, .btn-secondary {
                -webkit-tap-highlight-color: rgba(0,0,0,0.1);
            }

            /* Better mobile modal experience */
            .modal-content {
                -webkit-overflow-scrolling: touch;
                overscroll-behavior: contain;
            }

            /* Mobile-friendly game cards with better spacing */
            .mobile-game-card {
                -webkit-tap-highlight-color: transparent;
                user-select: none;
                -webkit-user-select: none;
            }

            /* Improve mobile bracket scrolling */
            .playoff-bracket {
                scroll-snap-type: x mandatory;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }

            .playoff-bracket::-webkit-scrollbar {
                display: none;
            }

            .conference-bracket {
                scroll-snap-align: center;
            }
        }

        .glass-effect {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .team-logo {
            width: 40px;
            height: 40px;
            pointer-events: none;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            background-color: white !important;
            padding: 2px;
            object-fit: contain;
        }

        /* Ensure ALL team logos have white background EVERYWHERE */
        img[src*="logo"],
        img[src*="Logo"],
        .team-logo,
        .dark-theme .team-logo,
        .light-theme .team-logo,
        img[class*="logo"],
        img[alt*="logo"],
        img[alt*="Logo"] {
            background-color: white !important;
            padding: 2px !important;
            border-radius: 50% !important;
        }

        /* Specific overrides for any team logo class */
        .jets-logo, .panthers-logo, .bills-logo, .patriots-logo, .dolphins-logo,
        .ravens-logo, .bengals-logo, .browns-logo, .steelers-logo,
        .texans-logo, .colts-logo, .jaguars-logo, .titans-logo,
        .broncos-logo, .chiefs-logo, .raiders-logo, .chargers-logo,
        .cowboys-logo, .giants-logo, .eagles-logo, .commanders-logo,
        .bears-logo, .lions-logo, .packers-logo, .vikings-logo,
        .falcons-logo, .panthers-logo, .saints-logo, .buccaneers-logo,
        .cardinals-logo, .rams-logo, .seahawks-logo, .niners-logo {
            background-color: white !important;
            padding: 2px !important;
        }

        .team-selector {
            cursor: pointer;
            border-radius: 0.75rem;
            padding: 0.75rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            background: var(--card-bg);
            min-width: fit-content;
            width: auto;
            flex-shrink: 0;
        }

        .team-selector:hover {
            background: rgba(59, 130, 246, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .team-selector.winner {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.1) 100%);
            border-color: var(--primary-green);
            box-shadow: 0 0 20px rgba(16, 185, 129, 0.4);
        }

        .prediction-correct {
            border-left: 4px solid #10b981;
            background: rgba(16, 185, 129, 0.1);
        }

        .prediction-incorrect {
            border-left: 4px solid #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, #1d4ed8 100%);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
        }

        .btn-secondary {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            color: #e2e8f0;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #334155;
            border-color: var(--primary-blue);
            transform: translateY(-1px);
        }

        .card {
            background: var(--card-bg);
            border-radius: 1rem;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .light-theme .card {
            background: var(--light-card-bg);
            border-color: var(--light-border-color);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            color: #1e293b;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
        }

        .light-theme .card:hover {
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .stats-card {
            background: linear-gradient(135deg, var(--card-bg) 0%, #2d3748 100%);
            border-radius: 1rem;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 40px rgba(0,0,0,0.4);
        }

        .live-indicator {
            display: inline-flex;
            align-items: center;
            background: #dc2626;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            animation: pulse 2s infinite;
        }

        .live-indicator::before {
            content: '●';
            margin-right: 0.5rem;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .week-tab {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            color: #94a3b8;
            padding: 0.75rem 1.25rem;
            border-radius: 0.75rem;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .week-tab:hover {
            background: var(--primary-blue);
            color: white;
            transform: translateY(-1px);
        }

        .week-tab.active {
            background: linear-gradient(135deg, var(--primary-blue) 0%, #1d4ed8 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
        }

        #playoff-container {
            width: 100%;
            overflow-x: auto;
            overflow-y: visible;
        }

        .playoff-bracket {
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            gap: 2rem;
            overflow-x: auto;
            overflow-y: visible;
            padding: 2rem;
            min-height: 800px;
            position: relative;
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.3) 0%, rgba(15, 23, 42, 0.3) 100%);
            border-radius: 1rem;
            width: max-content;
            min-width: 1400px;
            -webkit-overflow-scrolling: touch;
        }

        .conference-bracket {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 280px;
            position: relative;
            flex-shrink: 0;
            margin: 0 0.5rem;
        }

        .bracket-rounds {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            width: 100%;
            position: relative;
            gap: 2rem;
        }

        .playoff-round {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1.5rem;
            min-width: 280px;
            max-width: 320px;
            position: relative;
            flex-shrink: 0;
            padding: 1rem;
        }

        .playoff-round h4 {
            text-align: center;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #60a5fa;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        }

        .light-theme .playoff-round h4 {
            color: #2563eb;
            text-shadow: none;
        }

        .playoff-matchup {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 1.5rem;
            border: 2px solid var(--border-color);
            min-height: 120px;
            width: 100%;
            max-width: 260px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            margin: 0.75rem 0;
            box-sizing: border-box;
        }

        .playoff-matchup:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
            border-color: var(--primary-blue);
        }

        .playoff-matchup.winner {
            border-color: var(--primary-green);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
        }

        /* Bracket connecting lines */
        .bracket-line {
            position: absolute;
            background: var(--primary-blue);
            z-index: 1;
            opacity: 0.6;
        }

        .bracket-line.horizontal {
            height: 3px;
            width: 2rem;
            border-radius: 2px;
        }

        .bracket-line.vertical {
            width: 3px;
            height: 4rem;
            border-radius: 2px;
        }

        .super-bowl-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            z-index: 5;
            margin: 0 1rem;
            min-width: 180px;
            order: 2; /* Place in center */
        }

        .super-bowl-matchup {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            border: 3px solid #d97706;
            color: #1a202c;
            font-weight: bold;
            min-width: 180px;
            min-height: 100px;
            box-shadow: 0 8px 30px rgba(245, 158, 11, 0.4);
        }

        .injury-details-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 1rem;
            padding: 1.5rem;
            max-width: 500px;
            width: 90%;
            z-index: 1001;
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
        }

        .injury-item {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1rem;
            align-items: center;
            padding: 0.75rem;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
            background: rgba(220, 38, 38, 0.1);
            border-left: 3px solid #dc2626;
        }

        .injury-details-btn {
            justify-self: end;
            white-space: nowrap;
        }

        .injury-excerpt {
            font-size: 0.875rem;
            color: #94a3b8;
            margin-top: 0.25rem;
            line-height: 1.4;
        }

        .playoff-team {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.25rem 0;
        }

        .playoff-team:hover {
            background: rgba(59, 130, 246, 0.1);
        }

        .playoff-team.winner {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.1) 100%);
            color: #10b981;
            font-weight: 700;
        }

        .weather-icon {
            font-size: 1.25rem;
            margin-right: 0.5rem;
        }

        .injury-indicator {
            background: #dc2626;
            color: white;
            font-size: 0.625rem;
            padding: 0.125rem 0.375rem;
            border-radius: 9999px;
            font-weight: 600;
        }

        .betting-odds {
            background: var(--accent-gold);
            color: #1a202c;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0,0,0,0.8);
            display: none;
            align-items: flex-start;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
            padding-top: 2rem;
        }

        .modal-content {
            background: var(--card-bg);
            border-radius: 1rem;
            padding: 2rem;
            width: 85%;
            max-width: 1000px;
            min-width: 600px;
            max-height: calc(100vh - 4rem);
            overflow-y: auto;
            border: 1px solid var(--border-color);
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
            position: relative;
            margin-top: 0;
            cursor: move;
        }

        .modal-content:hover {
            box-shadow: 0 25px 70px rgba(0,0,0,0.6);
        }

        /* Smart positioned modals */
        .modal.positioned {
            align-items: flex-start;
            justify-content: flex-start;
            padding: 0;
        }

        .modal.positioned .modal-content {
            position: absolute;
            width: auto;
            margin: 0;
        }

        /* Mobile responsive - keep modals at top */
        @media (max-width: 768px) {
            .modal {
                align-items: flex-start;
                padding-top: 1rem;
            }

            .modal-content {
                width: 95%;
                max-height: calc(100vh - 2rem);
            }
        }

        /* Full-width playoff bracket modal */
        .playoff-bracket-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.95);
            display: none;
            z-index: 1001;
            backdrop-filter: blur(5px);
        }

        .playoff-bracket-modal-content {
            background: var(--dark-bg);
            width: 100%;
            height: 100%;
            overflow: auto;
            padding: 0.5rem;
            position: relative;
        }

        .playoff-bracket-modal .close-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: var(--card-bg);
            border: none;
            color: var(--text-color);
            font-size: 1.5rem;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            cursor: pointer;
            z-index: 1002;
        }

        .progress-bar {
            background: var(--border-color);
            border-radius: 9999px;
            height: 8px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-green) 100%);
            height: 100%;
            border-radius: 9999px;
            transition: width 0.3s ease;
        }

        .notification {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 1rem 1.5rem;
            z-index: 1001;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            color: #e2e8f0;
        }

        .light-theme .notification {
            background: var(--light-card-bg);
            border-color: var(--light-border-color);
            color: #1e293b;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left: 4px solid var(--primary-green);
        }

        .notification.error {
            border-left: 4px solid #dc2626;
        }

        .team-name-link {
            cursor: pointer;
            color: #60a5fa;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .team-name-link:hover {
            color: #93c5fd;
            text-decoration: underline;
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            .team-logo { width: 32px; height: 32px; }
            .card { padding: 1rem; }
            .playoff-round {
                min-width: 250px;
                max-width: 280px;
            }
            .playoff-bracket { gap: 1rem; }

            .mobile-game-card {
                background: var(--card-bg);
                border-radius: 1rem;
                padding: 1rem;
                margin-bottom: 1rem;
                border: 1px solid var(--border-color);
                width: 100%;
                box-sizing: border-box;
                overflow: hidden;
            }

            .mobile-team {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0.75rem;
                border-radius: 0.75rem;
                cursor: pointer;
                transition: all 0.3s ease;
                margin: 0.5rem 0;
                border: 2px solid transparent;
                min-height: 60px;
                width: 100%;
                box-sizing: border-box;
                flex-wrap: nowrap;
            }

            .mobile-team:hover {
                background: rgba(59, 130, 246, 0.1);
            }

            .mobile-team.winner {
                background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.1) 100%);
                border: 2px solid var(--primary-green);
            }

            /* Ensure team info doesn't get cut off */
            .mobile-team .team-info {
                flex: 1;
                min-width: 0;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .mobile-team .team-name {
                font-size: 0.875rem;
                font-weight: 600;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                flex: 1;
                min-width: 0;
            }

            .mobile-team .score-input {
                width: 50px;
                height: 32px;
                font-size: 0.875rem;
                flex-shrink: 0;
            }

            .modal-content {
                width: 95%;
                max-width: 95%;
                padding: 1rem;
                max-height: 85vh;
                margin: auto;
            }

            .week-tab {
                padding: 0.5rem;
                font-size: 0.8rem;
            }
        }

        /* Light theme improvements */
        .light-theme {
            --dark-bg: #f8fafc;
            --card-bg: #ffffff;
            --border-color: #e2e8f0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1e293b;
        }

        .light-theme .glass-effect {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .light-theme .team-selector {
            background: white;
            border-color: #e2e8f0;
            color: #1e293b;
        }

        .light-theme .team-selector:hover {
            background: rgba(59, 130, 246, 0.05);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .light-theme .week-tab {
            background: white;
            border-color: #e2e8f0;
            color: #64748b;
        }

        .light-theme .week-tab:hover {
            background: var(--primary-blue);
            color: white;
        }

        .light-theme .stats-card {
            background: white;
            border-color: #e2e8f0;
            color: #1e293b;
        }

        /* Light theme text improvements for better legibility */
        .light-theme .text-gray-300 {
            color: #475569 !important;
        }

        .light-theme .text-gray-400 {
            color: #64748b !important;
        }

        .light-theme .text-white {
            color: #1e293b !important;
        }

        .light-theme .score-input {
            background: white;
            border-color: #d1d5db;
            color: #1e293b;
        }

        .light-theme .score-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Clean Playoff Bracket Styles */
        .playoff-bracket-container {
            padding: 1rem;
            width: 100%;
            min-width: 1200px;
            overflow-x: auto;
        }

        .bracket-layout {
            display: grid;
            grid-template-columns: minmax(350px, 1fr) minmax(250px, auto) minmax(350px, 1fr);
            gap: 1.5rem;
            align-items: start;
            min-width: 1200px;
        }

        .conference-side {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .conference-title {
            font-size: 2rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 1rem;
        }

        .afc-title {
            color: #ef4444;
        }

        .nfc-title {
            color: #3b82f6;
        }

        .bracket-rounds {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .round {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 1rem;
            padding: 1.5rem;
        }

        .round-title {
            font-size: 1.25rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 1rem;
            color: var(--primary-blue);
        }

        .matchups {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .matchup-card {
            background: rgba(55, 65, 81, 0.5);
            border-radius: 0.5rem;
            padding: 1rem;
            border: 1px solid var(--border-color);
        }

        .seed-label {
            text-align: center;
            font-size: 0.75rem;
            color: #9ca3af;
            margin-bottom: 0.5rem;
        }

        .team-matchup {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .team-card {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex: 1;
        }

        .team-card .team-logo {
            width: 24px;
            height: 24px;
        }

        .team-name {
            font-weight: 600;
            font-size: 0.875rem;
            white-space: nowrap;
            overflow: visible;
            min-width: max-content;
        }

        .team-record {
            font-size: 0.75rem;
            color: #9ca3af;
        }

        .vs-divider {
            margin: 0 1rem;
            font-weight: bold;
            color: #9ca3af;
        }

        .bye-card {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid #22c55e;
            border-radius: 0.5rem;
            padding: 1rem;
            text-align: center;
        }

        .bye-label {
            background: #22c55e;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .super-bowl-section {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .super-bowl-card {
            background: linear-gradient(135deg, #ef4444, #3b82f6);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            color: white;
            min-width: 250px;
        }

        .super-bowl-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .super-bowl-date, .super-bowl-location {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        .super-bowl-matchup {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
        }

        .championship-placeholder, .matchup-placeholder {
            text-align: center;
            color: #9ca3af;
            font-style: italic;
            padding: 1rem;
        }

        /* Responsive design */
        @media (max-width: 1200px) {
            .bracket-layout {
                grid-template-columns: 1fr;
                gap: 3rem;
            }

            .conference-side {
                order: 1;
            }

            .super-bowl-section {
                order: 2;
            }

            .nfc-side {
                order: 3;
            }
        }

        .light-theme .btn-secondary {
            background: white;
            border-color: #e2e8f0;
            color: #1e293b;
        }

        .light-theme .btn-secondary:hover {
            background: #f1f5f9;
            border-color: var(--primary-blue);
        }

        .light-theme .team-name-link {
            color: #2563eb;
        }

        .light-theme .team-name-link:hover {
            color: #1d4ed8;
        }

        /* Enhanced animations */
        @keyframes slideInUp {
            from {
                transform: translateY(30px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .animate-slide-in {
            animation: slideInUp 0.4s ease-out;
        }

        @keyframes scoreUpdate {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .score-animate {
            animation: scoreUpdate 0.3s ease;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--dark-bg);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-blue);
        }

        /* Team stat cards */
        .team-stat-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.75rem;
            margin: 0.25rem 0;
            transition: background-color 0.2s ease;
        }

        .team-stat-card:hover {
            background: rgba(59, 130, 246, 0.05);
        }

        .confidence-picker {
            background: var(--card-bg);
            color: #e2e8f0;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 60px;
        }

        .confidence-picker:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }

        .light-theme .confidence-picker {
            background: white;
            color: #1e293b;
            border-color: #d1d5db;
        }

        /* Light theme fixes for all dark backgrounds */
        .light-theme input[type="email"],
        .light-theme input[type="password"] {
            background: white !important;
            color: #1e293b !important;
            border: 1px solid #d1d5db !important;
        }

        .light-theme .bg-gray-700 {
            background: #f8fafc !important;
            color: #1e293b !important;
        }

        .light-theme .bg-gray-800 {
            background: #f1f5f9 !important;
            color: #1e293b !important;
        }

        .light-theme .hover\\:bg-gray-600:hover {
            background: #e2e8f0 !important;
        }

        .light-theme .hover\\:bg-gray-700:hover {
            background: #e2e8f0 !important;
        }

        .light-theme .text-gray-300 {
            color: #64748b !important;
        }

        .light-theme .text-gray-400 {
            color: #64748b !important;
        }

        .revenge-game {
            border-left: 4px solid #dc2626;
            background: rgba(220, 38, 38, 0.1);
        }

        .division-game {
            border-left: 4px solid var(--accent-gold);
            background: rgba(245, 158, 11, 0.1);
        }

        .prime-time {
            border-left: 4px solid #8b5cf6;
            background: rgba(139, 92, 246, 0.1);
        }

        .game-day-indicator {
            background: #6366f1;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
    </style>
</head>
<body class="dark-theme">
<!-- Navigation Header -->
<nav class="glass-effect sticky top-0 z-50 p-4">
    <div class="max-w-7xl mx-auto flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent">
                🏈 NFL Predictor Pro
            </h1>
            <div class="live-indicator hidden" id="live-indicator">
                LIVE
            </div>
        </div>

        <div class="flex items-center space-x-4">
            <a href="https://methstreams.ac/get-nflstreams/" target="_blank" class="btn-secondary text-sm">
                📺 Watch Live Stream
            </a>
            <button id="live-scores-btn" class="btn-secondary">
                📈 Live Scores
            </button>
            <button id="theme-toggle" class="btn-secondary">
                🌙
            </button>
            <button id="refresh-data" class="btn-secondary">
                🔄 Sync
            </button>
            <div id="auth-container" class="flex items-center space-x-2">
                <button id="login-btn" class="btn-primary">Login</button>
                <div id="user-info-container" class="hidden">
                    <span id="user-info" class="text-sm text-gray-400"></span>
                    <button id="logout-btn" class="btn-secondary ml-2">Logout</button>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Main Container -->
<div class="max-w-7xl mx-auto p-4 space-y-6">
    <!-- Score Dashboard -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="stats-card">
            <div class="text-3xl font-bold text-blue-400" id="total-score">0</div>
            <div class="text-sm text-gray-400">Total Points</div>
            <div class="progress-bar mt-2">
                <div class="progress-fill" id="score-progress" style="width: 0%"></div>
            </div>
        </div>
        <div class="stats-card">
            <div class="text-2xl font-bold text-green-400" id="accuracy-rate">--</div>
            <div class="text-sm text-gray-400">Accuracy Rate</div>
        </div>
        <div class="stats-card">
            <div class="text-2xl font-bold text-yellow-400" id="predictions-made">0</div>
            <div class="text-sm text-gray-400">Predictions Made</div>
        </div>
        <div class="stats-card">
            <div class="text-2xl font-bold text-purple-400" id="upset-picks">0</div>
            <div class="text-sm text-gray-400">Upset Picks</div>
        </div>
    </div>

    <!-- Week Navigation -->
    <div class="card">
        <h2 class="text-xl font-bold mb-4">Select Week or View Playoffs</h2>
        <div class="grid grid-cols-6 md:grid-cols-10 gap-2" id="week-navigation">
            <!-- Week buttons will be generated here -->
        </div>
    </div>

    <!-- Authentication Wall -->
    <div id="auth-wall" class="text-center py-20">
        <div class="max-w-md mx-auto">
            <h2 class="text-3xl font-bold mb-4">🏈 Welcome to NFL Predictor Pro</h2>
            <p class="text-gray-400 mb-8">Please log in to access your predictions and compete on the leaderboard.</p>
            <button id="auth-wall-login" class="btn-primary text-lg px-8 py-4">
                🔐 Login to Continue
            </button>
        </div>
    </div>

    <!-- Main Content Area (Hidden until authenticated) -->
    <div id="main-content" class="grid grid-cols-1 lg:grid-cols-3 gap-6 hidden">
        <!-- Games/Playoffs Content -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-2xl font-bold" id="content-title">Week 1 Games</h2>
                    <div class="flex items-center space-x-2">
                        <button id="quick-home" class="btn-secondary text-sm">🏠 All Home</button>
                        <button id="quick-favorites" class="btn-secondary text-sm">⭐ Favorites</button>
                        <button id="upset-special" class="btn-secondary text-sm">🚨 Upset Special</button>
                    </div>
                </div>

                <!-- Games Container -->
                <div id="games-container" class="space-y-4">
                    <!-- Games will be loaded here -->
                </div>

                <!-- Playoff Bracket Container -->
                <div id="playoff-container" class="hidden">
                    <div class="overflow-x-auto">
                        <div id="playoff-bracket" class="playoff-bracket">
                            <!-- Playoff bracket will be generated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Weekly Challenges -->
            <div class="card mt-6">
                <h3 class="text-lg font-bold mb-3">🎯 Weekly Challenge</h3>
                <div id="weekly-challenge" class="space-y-3">
                    <div class="team-stat-card">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">Upset Special (Pick 2 underdogs)</span>
                            <span id="upset-count" class="text-sm text-gray-400">0/2</span>
                        </div>
                    </div>
                    <div class="team-stat-card">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">Division Master (Perfect division picks)</span>
                            <span id="division-accuracy" class="text-sm text-gray-400">--</span>
                        </div>
                    </div>
                    <div class="team-stat-card">
                        <div class="flex justify-between items-center">
                            <span class="font-medium">Confidence Pool</span>
                            <span id="confidence-total" class="text-sm text-gray-400">0 pts</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Live Updates Panel -->
            <div class="card mt-6" id="live-updates-panel">
                <h3 class="text-lg font-bold mb-3">📰 Latest NFL News</h3>
                <div id="news-container" class="space-y-3">
                    <!-- News items will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Current Standings -->
            <div class="card">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold">🏆 Standings</h3>
                    <div class="flex space-x-1">
                        <button class="week-tab text-xs active" id="division-view" data-view="division">Division</button>
                        <button class="week-tab text-xs" id="conference-view" data-view="conference">Conference</button>
                    </div>
                </div>
                <div id="standings-container" class="space-y-3 max-h-96 overflow-y-auto">
                    <!-- Standings will be loaded here -->
                </div>
            </div>

            <!-- Playoff Race -->
            <div class="card">
                <h3 class="text-lg font-bold mb-3">🎯 Playoff Race</h3>
                <div id="playoff-race-container" class="space-y-2">
                    <!-- Playoff probabilities will be loaded here -->
                </div>
            </div>

            <!-- Power Rankings -->
            <div class="card">
                <h3 class="text-lg font-bold mb-3">📊 Power Rankings</h3>
                <div id="power-rankings-container" class="space-y-2">
                    <!-- Power rankings will be loaded here -->
                </div>
            </div>

            <!-- Quick Tools -->
            <div class="card">
                <h3 class="text-lg font-bold mb-3">⚡ Tools</h3>
                <div class="space-y-2">
                    <button id="show-leaderboard" class="w-full btn-secondary text-sm">🏆 Leaderboard</button>
                    <button id="import-bracket" class="w-full btn-secondary text-sm">📥 Import Bracket</button>
                    <button id="reset-all" class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm">🔄 Reset All</button>
                    <button id="share-bracket" class="w-full btn-secondary text-sm">📱 Share</button>
                    <button id="magic-numbers" class="w-full btn-secondary text-sm">🔮 Magic Numbers</button>
                </div>
            </div>

            <!-- Weather & Injuries -->
            <div class="card">
                <h3 class="text-lg font-bold mb-3">🌤️ Game Conditions</h3>
                <div id="conditions-container" class="space-y-2 text-sm">
                    <!-- Weather and injury info will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
<!-- Auth Modal -->
<div id="auth-modal" class="modal">
    <div class="modal-content">
        <h2 id="auth-title" class="text-2xl font-bold mb-4">Login</h2>
        <div id="auth-error" class="text-red-400 text-sm mb-4 hidden"></div>
        <form id="auth-form" class="space-y-4">
            <input type="email" id="email-input" placeholder="Email" class="w-full p-3 bg-gray-700 rounded-lg text-white" required>
            <input type="password" id="password-input" placeholder="Password" class="w-full p-3 bg-gray-700 rounded-lg text-white" required>
            <button type="submit" id="auth-submit" class="w-full btn-primary">Login</button>
        </form>
        <p class="text-center mt-4 text-sm">
            <span id="auth-switch-text">Don't have an account?</span>
            <button type="button" id="auth-switch" class="text-blue-400 hover:underline ml-1">Register</button>
        </p>
    </div>
</div>

<!-- Team Schedule Modal -->
<div id="team-modal" class="modal">
    <div class="modal-content">
        <div class="flex items-center justify-between mb-4">
            <h2 id="team-modal-title" class="text-2xl font-bold">Team Schedule</h2>
            <button id="close-team-modal" class="text-gray-400 hover:text-white text-2xl">&times;</button>
        </div>
        <div id="team-modal-content">
            <!-- Team details will be loaded here -->
        </div>
    </div>
</div>

<!-- Game Details Modal -->
<div id="game-modal" class="modal">
    <div class="modal-content">
        <div class="flex items-center justify-between mb-4">
            <h2 id="game-modal-title" class="text-2xl font-bold">Game Details</h2>
            <button id="close-game-modal" class="text-gray-400 hover:text-white text-2xl">&times;</button>
        </div>
        <div id="game-modal-content">
            <!-- Game details will be loaded here -->
        </div>
    </div>
</div>

<!-- Historical Data Modal -->
<div id="history-modal" class="modal">
    <div class="modal-content">
        <div class="flex items-center justify-between mb-4">
            <h2 id="history-modal-title" class="text-2xl font-bold">Team History</h2>
            <button id="close-history-modal" class="text-gray-400 hover:text-white text-2xl">&times;</button>
        </div>
        <div id="history-modal-content">
            <!-- Historical data will be loaded here -->
        </div>
    </div>
</div>

<!-- Leaderboard Modal -->
<div id="leaderboard-modal" class="modal">
    <div class="modal-content">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-2xl font-bold">🏆 Leaderboard</h2>
            <button id="close-leaderboard-modal" class="text-gray-400 hover:text-white text-2xl">&times;</button>
        </div>
        <div id="leaderboard-modal-content">
            <!-- Leaderboard will be loaded here -->
        </div>
    </div>
</div>

<!-- User Name Setting Modal -->
<div id="username-modal" class="modal">
    <div class="modal-content">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-2xl font-bold">👤 Set Display Name</h2>
            <button id="close-username-modal" class="text-gray-400 hover:text-white text-2xl">&times;</button>
        </div>
        <div class="space-y-4">
            <p class="text-gray-400">Choose how your name appears on the leaderboard:</p>
            <input type="text" id="display-name-input" placeholder="Enter your display name"
                   class="w-full p-3 bg-gray-700 rounded-lg text-white" maxlength="20">
            <div class="flex space-x-2">
                <button id="save-display-name" class="btn-primary flex-1">Save Name</button>
                <button id="cancel-display-name" class="btn-secondary flex-1">Cancel</button>
            </div>
        </div>
    </div>
</div>

<!-- Full-Width Playoff Bracket Modal -->
<div id="playoff-bracket-modal" class="playoff-bracket-modal">
    <div class="playoff-bracket-modal-content">
        <button class="close-btn" id="close-playoff-bracket-modal">&times;</button>
        <div class="text-center mb-6">
            <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent">
                🏈 NFL Playoff Bracket
            </h1>
            <p class="text-gray-400 mt-2">Make your playoff predictions with proper NFL reseeding</p>
        </div>
        <div id="playoff-bracket-full" class="playoff-bracket">
            <!-- Full playoff bracket will be rendered here -->
        </div>
    </div>
</div>

<!-- Notification -->
<div id="notification" class="notification">
    <div id="notification-content"></div>
</div>

<!-- Firebase and Main JavaScript -->
<script type="module">
    import { initializeApp } from "https://www.gstatic.com/firebasejs/11.9.1/firebase-app.js";
    import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.9.1/firebase-auth.js";
    import { getFirestore, doc, getDoc, setDoc } from "https://www.gstatic.com/firebasejs/11.9.1/firebase-firestore.js";

    // Firebase configuration
    let auth, db;
    try {
        const firebaseConfig = {
            apiKey: "AIzaSyByEky8SN50EA5XozKUURxpMAssJVF8dNU",
            authDomain: "football-fa84e.firebaseapp.com",
            projectId: "football-fa84e",
            storageBucket: "football-fa84e.appspot.com",
            messagingSenderId: "585689762025",
            appId: "1:585689762025:web:a4ce206e6b4541d499c878",
            measurementId: "G-JJYT9NZ6R0"
        };
        const app = initializeApp(firebaseConfig);
        auth = getAuth(app);
        db = getFirestore(app);
    } catch (error) {
        console.error('Firebase initialization failed:', error);
    }

    // RapidAPI Configuration
    const RAPIDAPI_KEY = '**************************************************';
    const RAPIDAPI_HOST = 'tank01-nfl-live-in-game-real-time-statistics-nfl.p.rapidapi.com';

    // Teams Data
    const teams = {
        "ARI": { name: "Cardinals", conf: "NFC", div: "West", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/ari.png" },
        "ATL": { name: "Falcons", conf: "NFC", div: "South", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/atl.png" },
        "BAL": { name: "Ravens", conf: "AFC", div: "North", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/bal.png" },
        "BUF": { name: "Bills", conf: "AFC", div: "East", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/buf.png" },
        "CAR": { name: "Panthers", conf: "NFC", div: "South", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/car.png" },
        "CHI": { name: "Bears", conf: "NFC", div: "North", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/chi.png" },
        "CIN": { name: "Bengals", conf: "AFC", div: "North", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/cin.png" },
        "CLE": { name: "Browns", conf: "AFC", div: "North", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/cle.png" },
        "DAL": { name: "Cowboys", conf: "NFC", div: "East", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/dal.png" },
        "DEN": { name: "Broncos", conf: "AFC", div: "West", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/den.png" },
        "DET": { name: "Lions", conf: "NFC", div: "North", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/det.png" },
        "GB": { name: "Packers", conf: "NFC", div: "North", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/gb.png" },
        "HOU": { name: "Texans", conf: "AFC", div: "South", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/hou.png" },
        "IND": { name: "Colts", conf: "AFC", div: "South", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/ind.png" },
        "JAX": { name: "Jaguars", conf: "AFC", div: "South", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/jax.png" },
        "KC": { name: "Chiefs", conf: "AFC", div: "West", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/kc.png" },
        "LV": { name: "Raiders", conf: "AFC", div: "West", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/lv.png" },
        "LAC": { name: "Chargers", conf: "AFC", div: "West", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/lac.png" },
        "LAR": { name: "Rams", conf: "NFC", div: "West", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/lar.png" },
        "MIA": { name: "Dolphins", conf: "AFC", div: "East", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/mia.png" },
        "MIN": { name: "Vikings", conf: "NFC", div: "North", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/min.png" },
        "NE": { name: "Patriots", conf: "AFC", div: "East", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/ne.png" },
        "NO": { name: "Saints", conf: "NFC", div: "South", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/no.png" },
        "NYG": { name: "Giants", conf: "NFC", div: "East", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/nyg.png" },
        "NYJ": { name: "Jets", conf: "AFC", div: "East", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/nyj.png" },
        "PHI": { name: "Eagles", conf: "NFC", div: "East", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/phi.png" },
        "PIT": { name: "Steelers", conf: "AFC", div: "North", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/pit.png" },
        "SF": { name: "49ers", conf: "NFC", div: "West", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/sf.png" },
        "SEA": { name: "Seahawks", conf: "NFC", div: "West", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/sea.png" },
        "TB": { name: "Buccaneers", conf: "NFC", div: "South", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/tb.png" },
        "TEN": { name: "Titans", conf: "AFC", div: "South", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/ten.png" },
        "WAS": { name: "Commanders", conf: "NFC", div: "East", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/wsh.png" },
        "WSH": { name: "Commanders", conf: "NFC", div: "East", logo: "https://a.espncdn.com/i/teamlogos/nfl/500/wsh.png" }
    };

    // Global State
    console.log('🏈 NFL Predictor Pro JavaScript loaded successfully!');
    let currentWeek = 1;
    let currentView = 'schedule';
    let currentUserId = null;
    let userDisplayName = '';
    let gamePredictions = {};
    let playoffPredictions = {
        AFC: { wildcard: {}, divisional: {}, conference: {} },
        NFC: { wildcard: {}, divisional: {}, conference: {} },
        superBowl: null
    };
    let confidencePicks = {};
    let scorePredictions = {};
    let liveGameData = {};
    let bettingOdds = {};
    let newsData = [];
    let weatherData = {};
    let injuryData = {};
    let standingsView = 'division';
    let isDarkTheme = true;
    let teamHistoryData = {};
    let accuracyTracking = {
        totalPredictions: 0,
        correctPredictions: 0
    };

    // Stadium data for home field advantage calculation
    const stadiumData = {
        "ARI": { type: "retractable", altitude: 1086, noise: 3 },
        "ATL": { type: "dome", altitude: 1050, noise: 4 },
        "BAL": { type: "outdoor", altitude: 56, noise: 4 },
        "BUF": { type: "outdoor", altitude: 630, noise: 5 },
        "CAR": { type: "outdoor", altitude: 748, noise: 3 },
        "CHI": { type: "outdoor", altitude: 594, noise: 3 },
        "CIN": { type: "outdoor", altitude: 550, noise: 3 },
        "CLE": { type: "outdoor", altitude: 653, noise: 4 },
        "DAL": { type: "retractable", altitude: 451, noise: 2 },
        "DEN": { type: "outdoor", altitude: 5280, noise: 4 },
        "DET": { type: "dome", altitude: 585, noise: 3 },
        "GB": { type: "outdoor", altitude: 640, noise: 5 },
        "HOU": { type: "retractable", altitude: 43, noise: 3 },
        "IND": { type: "retractable", altitude: 717, noise: 3 },
        "JAX": { type: "outdoor", altitude: 16, noise: 2 },
        "KC": { type: "outdoor", altitude: 909, noise: 5 },
        "LV": { type: "dome", altitude: 2001, noise: 3 },
        "LAC": { type: "outdoor", altitude: 72, noise: 2 },
        "LAR": { type: "dome", altitude: 295, noise: 3 },
        "MIA": { type: "outdoor", altitude: 7, noise: 3 },
        "MIN": { type: "dome", altitude: 834, noise: 4 },
        "NE": { type: "outdoor", altitude: 112, noise: 4 },
        "NO": { type: "dome", altitude: 3, noise: 5 },
        "NYG": { type: "outdoor", altitude: 112, noise: 2 },
        "NYJ": { type: "outdoor", altitude: 112, noise: 2 },
        "PHI": { type: "outdoor", altitude: 56, noise: 4 },
        "PIT": { type: "outdoor", altitude: 1203, noise: 5 },
        "SF": { type: "outdoor", altitude: 43, noise: 3 },
        "SEA": { type: "outdoor", altitude: 56, noise: 5 },
        "TB": { type: "outdoor", altitude: 26, noise: 3 },
        "TEN": { type: "outdoor", altitude: 597, noise: 3 },
        "WAS": { type: "outdoor", altitude: 288, noise: 3 },
        "WSH": { type: "outdoor", altitude: 288, noise: 3 }
    };

    // Comprehensive sample schedule for all 18 weeks - Week 1 matches 2024 NFL Schedule (corrected dates)
    const fullSchedule = [
        // Week 1 - Corrected 2024 NFL Schedule
        { week: 1, away: "BAL", home: "KC", gameTime: "8:20 PM ET", day: "Thursday, Sept. 5, 2024", date: "2024-09-05", network: "NBC" },
        { week: 1, away: "GB", home: "PHI", gameTime: "8:15 PM ET", day: "Friday, Sept. 6, 2024", date: "2024-09-06", network: "Peacock", location: "Brazil" },
        { week: 1, away: "PIT", home: "ATL", gameTime: "1:00 PM ET", day: "Sunday, Sept. 8, 2024", date: "2024-09-08", network: "FOX" },
        { week: 1, away: "HOU", home: "IND", gameTime: "1:00 PM ET", day: "Sunday, Sept. 8, 2024", date: "2024-09-08", network: "CBS" },
        { week: 1, away: "MIA", home: "JAX", gameTime: "1:00 PM ET", day: "Sunday, Sept. 8, 2024", date: "2024-09-08", network: "CBS" },
        { week: 1, away: "CAR", home: "NO", gameTime: "1:00 PM ET", day: "Sunday, Sept. 8, 2024", date: "2024-09-08", network: "FOX" },
        { week: 1, away: "NE", home: "CIN", gameTime: "1:00 PM ET", day: "Sunday, Sept. 8, 2024", date: "2024-09-08", network: "CBS" },
        { week: 1, away: "TEN", home: "CHI", gameTime: "1:00 PM ET", day: "Sunday, Sept. 8, 2024", date: "2024-09-08", network: "FOX" },
        { week: 1, away: "MIN", home: "NYG", gameTime: "1:00 PM ET", day: "Sunday, Sept. 8, 2024", date: "2024-09-08", network: "FOX" },
        { week: 1, away: "WAS", home: "TB", gameTime: "4:25 PM ET", day: "Sunday, Sept. 8, 2024", date: "2024-09-08", network: "FOX" },
        { week: 1, away: "LV", home: "LAC", gameTime: "4:05 PM ET", day: "Sunday, Sept. 8, 2024", date: "2024-09-08", network: "CBS" },
        { week: 1, away: "ARI", home: "BUF", gameTime: "1:00 PM ET", day: "Sunday, Sept. 8, 2024", date: "2024-09-08", network: "CBS" },
        { week: 1, away: "DEN", home: "SEA", gameTime: "4:25 PM ET", day: "Sunday, Sept. 8, 2024", date: "2024-09-08", network: "CBS" },
        { week: 1, away: "CLE", home: "DAL", gameTime: "4:25 PM ET", day: "Sunday, Sept. 8, 2024", date: "2024-09-08", network: "FOX" },
        { week: 1, away: "DET", home: "LAR", gameTime: "8:20 PM ET", day: "Sunday, Sept. 8, 2024", date: "2024-09-08", network: "NBC" },
        { week: 1, away: "NYJ", home: "SF", gameTime: "8:15 PM ET", day: "Monday, Sept. 9, 2024", date: "2024-09-09", network: "ESPN" },

        // Week 2
        { week: 2, away: "ATL", home: "PHI", gameTime: "8:15 PM ET", day: "Monday, Sept. 15, 2025", date: "2025-09-15", network: "ESPN" },
        { week: 2, away: "LAC", home: "CAR", gameTime: "1:00 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "CBS" },
        { week: 2, away: "NO", home: "DAL", gameTime: "1:00 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "FOX", isDivisionGame: false },
        { week: 2, away: "TB", home: "DET", gameTime: "1:00 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "FOX" },
        { week: 2, away: "CLE", home: "JAX", gameTime: "1:00 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "CBS" },
        { week: 2, away: "SF", home: "MIN", gameTime: "1:00 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "CBS" },
        { week: 2, away: "IND", home: "GB", gameTime: "1:00 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "FOX" },
        { week: 2, away: "SEA", home: "NE", gameTime: "1:00 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "FOX" },
        { week: 2, away: "NYJ", home: "TEN", gameTime: "1:00 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "CBS" },
        { week: 2, away: "NYG", home: "WSH", gameTime: "1:00 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "FOX", isDivisionGame: true },
        { week: 2, away: "LAR", home: "ARI", gameTime: "4:05 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "FOX", isDivisionGame: true },
        { week: 2, away: "PIT", home: "DEN", gameTime: "4:25 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "CBS" },
        { week: 2, away: "CIN", home: "KC", gameTime: "4:25 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "CBS", isDivisionGame: false },
        { week: 2, away: "CHI", home: "HOU", gameTime: "8:20 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "NBC" },
        { week: 2, away: "LV", home: "BAL", gameTime: "1:00 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "CBS" },
        { week: 2, away: "BUF", home: "MIA", gameTime: "1:00 PM ET", day: "Sunday, Sept. 14, 2025", date: "2025-09-14", network: "CBS", isDivisionGame: true },

        // Week 3 (adding more games for other weeks)
        { week: 3, away: "NE", home: "NYJ", gameTime: "8:15 PM ET", day: "Thursday", network: "Amazon", isDivisionGame: true },
        { week: 3, away: "NYG", home: "CLE", gameTime: "1:00 PM ET", day: "Sunday", network: "FOX" },
        { week: 3, away: "CHI", home: "IND", gameTime: "1:00 PM ET", day: "Sunday", network: "CBS" },
        { week: 3, away: "HOU", home: "MIN", gameTime: "1:00 PM ET", day: "Sunday", network: "FOX" },
        { week: 3, away: "PHI", home: "NO", gameTime: "1:00 PM ET", day: "Sunday", network: "FOX" },
        { week: 3, away: "LAC", home: "PIT", gameTime: "1:00 PM ET", day: "Sunday", network: "CBS" },
        { week: 3, away: "DEN", home: "TB", gameTime: "1:00 PM ET", day: "Sunday", network: "FOX" },
        { week: 3, away: "GB", home: "TEN", gameTime: "1:00 PM ET", day: "Sunday", network: "FOX" },
        { week: 3, away: "CAR", home: "LV", gameTime: "4:05 PM ET", day: "Sunday", network: "CBS" },
        { week: 3, away: "MIA", home: "SEA", gameTime: "4:25 PM ET", day: "Sunday", network: "CBS" },
        { week: 3, away: "DET", home: "ARI", gameTime: "4:25 PM ET", day: "Sunday", network: "FOX" },
        { week: 3, away: "BAL", home: "DAL", gameTime: "4:25 PM ET", day: "Sunday", network: "CBS" },
        { week: 3, away: "SF", home: "LAR", gameTime: "4:25 PM ET", day: "Sunday", network: "FOX", isDivisionGame: true },
        { week: 3, away: "KC", home: "ATL", gameTime: "8:20 PM ET", day: "Sunday", network: "NBC" },
        { week: 3, away: "JAX", home: "BUF", gameTime: "8:15 PM ET", day: "Monday", network: "ESPN" },
        { week: 3, away: "WSH", home: "CIN", gameTime: "8:15 PM ET", day: "Monday", network: "ESPN" },

        // Week 4
        { week: 4, away: "DAL", home: "NYG", gameTime: "8:15 PM ET", day: "Thursday", network: "Amazon", isDivisionGame: true },
        { week: 4, away: "NO", home: "ATL", gameTime: "1:00 PM ET", day: "Sunday", network: "FOX", isDivisionGame: true },
        { week: 4, away: "CIN", home: "CAR", gameTime: "1:00 PM ET", day: "Sunday", network: "FOX" },
        { week: 4, away: "LAR", home: "CHI", gameTime: "1:00 PM ET", day: "Sunday", network: "FOX" },
        { week: 4, away: "MIN", home: "GB", gameTime: "1:00 PM ET", day: "Sunday", network: "CBS", isDivisionGame: true },
        { week: 4, away: "JAX", home: "HOU", gameTime: "1:00 PM ET", day: "Sunday", network: "CBS", isDivisionGame: true },
        { week: 4, away: "PIT", home: "IND", gameTime: "1:00 PM ET", day: "Sunday", network: "CBS" },
        { week: 4, away: "DEN", home: "NYJ", gameTime: "1:00 PM ET", day: "Sunday", network: "CBS" },
        { week: 4, away: "PHI", home: "TB", gameTime: "1:00 PM ET", day: "Sunday", network: "FOX" },
        { week: 4, away: "WSH", home: "ARI", gameTime: "4:05 PM ET", day: "Sunday", network: "FOX" },
        { week: 4, away: "NE", home: "SF", gameTime: "4:25 PM ET", day: "Sunday", network: "FOX" },
        { week: 4, away: "KC", home: "LAC", gameTime: "4:25 PM ET", day: "Sunday", network: "CBS", isDivisionGame: true },
        { week: 4, away: "CLE", home: "LV", gameTime: "4:25 PM ET", day: "Sunday", network: "CBS" },
        { week: 4, away: "BUF", home: "BAL", gameTime: "8:20 PM ET", day: "Sunday", network: "NBC" },
        { week: 4, away: "TEN", home: "MIA", gameTime: "8:15 PM ET", day: "Monday", network: "ESPN" },
        { week: 4, away: "SEA", home: "DET", gameTime: "8:15 PM ET", day: "Monday", network: "ESPN" }
    ];

    // Generate remaining weeks (5-18) with different matchups
    function generateFullSchedule() {
        const teamList = Object.keys(teams);
        const scheduleComplete = [...fullSchedule];

        // For weeks 5-18, generate sample matchups
        for (let week = 5; week <= 18; week++) {
            const weekGames = [];
            const usedTeams = new Set();

            // Generate 16 games for each week (32 teams / 2 = 16 games)
            let gamesAdded = 0;
            let attempts = 0;

            while (gamesAdded < 16 && attempts < 100) {
                const availableTeams = teamList.filter(team => !usedTeams.has(team));
                if (availableTeams.length < 2) break;

                const away = availableTeams[Math.floor(Math.random() * availableTeams.length)];
                const remainingTeams = availableTeams.filter(team => team !== away);
                const home = remainingTeams[Math.floor(Math.random() * remainingTeams.length)];

                const awayTeam = teams[away];
                const homeTeam = teams[home];
                const isDivisionGame = awayTeam.conf === homeTeam.conf && awayTeam.div === homeTeam.div;

                const timeSlots = ["1:00 PM ET", "1:00 PM ET", "1:00 PM ET", "4:05 PM ET", "4:25 PM ET", "8:20 PM ET"];
                const days = ["Sunday", "Sunday", "Sunday", "Sunday", "Sunday", "Sunday"];
                const networks = ["CBS", "FOX", "NBC", "ESPN"];

                // Determine game time, day, and network
                let gameTime, day, network;

                if (gamesAdded === 0 && week % 3 === 0) {
                    // Thursday Night Football
                    gameTime = "8:15 PM ET";
                    day = "Thursday";
                    network = "Amazon";
                } else if (gamesAdded === 15) {
                    // Monday Night Football
                    gameTime = "8:15 PM ET";
                    day = "Monday";
                    network = "ESPN";
                } else if (gamesAdded === 14) {
                    // Sunday Night Football
                    gameTime = "8:20 PM ET";
                    day = "Sunday";
                    network = "NBC";
                } else {
                    // Regular Sunday games
                    gameTime = timeSlots[gamesAdded % timeSlots.length];
                    day = "Sunday";
                    network = networks[gamesAdded % networks.length];
                }

                weekGames.push({
                    week: week,
                    away: away,
                    home: home,
                    gameTime: gameTime,
                    day: day,
                    network: network,
                    isDivisionGame: isDivisionGame
                });

                usedTeams.add(away);
                usedTeams.add(home);
                gamesAdded++;
                attempts++;
            }

            scheduleComplete.push(...weekGames);
        }

        return scheduleComplete;
    }

    const completeSchedule = generateFullSchedule();

    // API Functions
    async function fetchWithRapidAPI(endpoint, params = {}) {
        const url = new URL(`https://${RAPIDAPI_HOST}/${endpoint}`);
        Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'x-rapidapi-key': RAPIDAPI_KEY,
                    'x-rapidapi-host': RAPIDAPI_HOST
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API Error:', error);
            return null;
        }
    }

    async function loadWeeklySchedule(week) {
        try {
            // Try API first
            const data = await fetchWithRapidAPI('getNFLGamesForWeek', {
                week: week,
                seasonType: 'reg',
                season: '2025'
            });

            // If API fails or returns no data, use our complete schedule
            if (!data?.body || data.body.length === 0) {
                return completeSchedule.filter(g => g.week === week);
            }

            return data.body;
        } catch (error) {
            console.error('Error loading schedule:', error);
            // Fallback to our complete schedule
            return completeSchedule.filter(g => g.week === week);
        }
    }

    async function loadBettingOdds(gameDate) {
        try {
            const data = await fetchWithRapidAPI('getNFLBettingOdds', {
                gameDate: gameDate,
                itemFormat: 'list',
                impliedTotals: 'true'
            });
            return data?.body || {};
        } catch (error) {
            console.error('Error loading betting odds:', error);
            return {};
        }
    }

    async function loadNFLNews() {
        try {
            const data = await fetchWithRapidAPI('getNFLNews', {
                fantasyNews: 'true',
                maxItems: '10'
            });
            return data?.body || [];
        } catch (error) {
            console.error('Error loading news:', error);
            return [];
        }
    }

    async function loadTeamStandings() {
        try {
            const data = await fetchWithRapidAPI('getNFLTeams', {
                sortBy: 'standings',
                rosters: 'false',
                schedules: 'false',
                topPerformers: 'true',
                teamStats: 'true',
                teamStatsSeason: '2024'
            });
            return data?.body || {};
        } catch (error) {
            console.error('Error loading standings:', error);
            return {};
        }
    }

    async function loadTeamHistory(teamId) {
        try {
            const data = await fetchWithRapidAPI('getNFLTeamSchedule', {
                teamAbv: teamId,
                season: '2024'
            });
            return data?.body || {};
        } catch (error) {
            console.error('Error loading team history:', error);
            return {};
        }
    }

    // Core Functionality with better error handling
    function calculateStandings(maxWeek) {
        const standings = {};

        // Initialize standings for all teams
        Object.keys(teams).forEach(teamId => {
            standings[teamId] = {
                id: teamId,
                w: 0, l: 0, t: 0,
                div_w: 0, div_l: 0, div_t: 0,
                conf_w: 0, conf_l: 0, conf_t: 0,
                nc_w: 0, nc_l: 0, nc_t: 0,
                pointsFor: 0, pointsAgainst: 0
            };
        });

        // Process game predictions up to maxWeek
        Object.keys(gamePredictions).forEach(gameId => {
            try {
                const [week, away, home] = gameId.split('-');
                if (parseInt(week) > maxWeek) return;

                const prediction = gamePredictions[gameId];
                if (!prediction) return;

                const homeTeam = teams[home];
                const awayTeam = teams[away];

                // Skip if teams don't exist
                if (!homeTeam || !awayTeam) {
                    console.warn(`Invalid team in game ${gameId}: ${home} or ${away}`);
                    return;
                }

                const isConfGame = homeTeam.conf === awayTeam.conf;
                const isDivGame = isConfGame && homeTeam.div === awayTeam.div;

                if (prediction.winner === 'tie') {
                    standings[home].t++;
                    standings[away].t++;
                    if (isDivGame) {
                        standings[home].div_t++;
                        standings[away].div_t++;
                    }
                    if (isConfGame) {
                        standings[home].conf_t++;
                        standings[away].conf_t++;
                    }
                } else {
                    const winnerId = prediction.winner;
                    const loserId = winnerId === home ? away : home;

                    standings[winnerId].w++;
                    standings[loserId].l++;

                    if (isDivGame) {
                        standings[winnerId].div_w++;
                        standings[loserId].div_l++;
                    }
                    if (isConfGame) {
                        standings[winnerId].conf_w++;
                        standings[loserId].conf_l++;
                    }
                }
            } catch (error) {
                console.error(`Error processing game ${gameId}:`, error);
            }
        });

        return standings;
    }

    function getWinPercentage(record) {
        if (!record) return 0;
        const totalGames = record.w + record.l + record.t;
        if (totalGames === 0) return 0;
        return (record.w + record.t * 0.5) / totalGames;
    }

    function formatGameDate(game) {
        if (game.date) {
            const gameDate = new Date(game.date);
            const options = {
                weekday: 'long',
                month: 'long',
                day: 'numeric',
                year: 'numeric'
            };
            return gameDate.toLocaleDateString('en-US', options);
        }

        // If no date but has day info, use that
        if (game.day && game.day !== 'Sunday' && game.day !== 'Monday' && game.day !== 'Thursday') {
            return game.day;
        }

        // Calculate date based on week number and day
        const seasonStart = new Date('2025-09-04'); // Thursday, Sept 4, 2025 (Week 1 start)
        const weekOffset = (currentWeek - 1) * 7;

        let dayOffset = 0;
        if (game.day === 'Thursday') dayOffset = 0;
        else if (game.day === 'Sunday') dayOffset = 3;
        else if (game.day === 'Monday') dayOffset = 4;

        const gameDate = new Date(seasonStart);
        gameDate.setDate(seasonStart.getDate() + weekOffset + dayOffset);

        const options = {
            weekday: 'long',
            month: 'long',
            day: 'numeric',
            year: 'numeric'
        };
        return gameDate.toLocaleDateString('en-US', options);
    }

    function formatGameTime(game) {
        let timeStr = game.gameTime || 'TBD';
        if (game.network) {
            timeStr += ` on ${game.network}`;
        }
        return timeStr;
    }

    function calculateHomeFieldAdvantage(homeTeamId, awayTeamId, weather = {}) {
        if (!stadiumData[homeTeamId] || !stadiumData[awayTeamId]) return 3;

        const homeStadium = stadiumData[homeTeamId];
        const awayStadium = stadiumData[awayTeamId];

        let advantage = 3; // Base home field advantage

        // Altitude advantage
        if (homeStadium.altitude > 3000) {
            advantage += 1;
        }

        // Crowd noise advantage
        advantage += homeStadium.noise * 0.5;

        // Weather advantage for outdoor stadiums
        if (homeStadium.type === 'outdoor' && awayStadium.type === 'dome') {
            if (weather.temp && weather.temp < 32) advantage += 2;
            if (weather.wind && weather.wind > 15) advantage += 1;
            if (weather.precipitation) advantage += 1;
        }

        return Math.min(advantage, 7); // Cap at 7 points
    }

    function calculatePlayoffProbabilities() {
        const standings = calculateStandings(18);
        const probabilities = {};

        ['AFC', 'NFC'].forEach(conf => {
            const confTeams = Object.values(standings)
                .filter(team => teams[team.id] && teams[team.id].conf === conf)
                .sort((a, b) => getWinPercentage(b) - getWinPercentage(a));

            confTeams.forEach((team, index) => {
                let prob = 0;

                // Base probability on current standing
                if (index < 4) prob = 85 + (4 - index) * 3; // Division leaders
                else if (index < 7) prob = 60 + (7 - index) * 8; // Wild card spots
                else if (index < 10) prob = 15 + (10 - index) * 5; // Bubble teams
                else prob = Math.max(1, 10 - index); // Long shots

                // Adjust based on remaining schedule strength
                const remainingGames = 17 - (team.w + team.l + team.t);
                if (remainingGames > 8) prob *= 0.8;
                else if (remainingGames < 3) prob *= 1.2;

                probabilities[team.id] = Math.min(99, Math.max(1, Math.round(prob)));
            });
        });

        return probabilities;
    }

    function calculateBracketScore() {
        let regularSeasonScore = 0;
        let playoffScore = 0;

        // Regular season (1 point each)
        Object.values(gamePredictions).forEach(prediction => {
            if (prediction) regularSeasonScore += 1;
        });

        // Playoff scoring
        ['AFC', 'NFC'].forEach(conf => {
            // Wild Card (2 points each)
            Object.values(playoffPredictions[conf]?.wildcard || {}).forEach(prediction => {
                if (prediction) playoffScore += 2;
            });

            // Divisional (4 points each)
            Object.values(playoffPredictions[conf]?.divisional || {}).forEach(prediction => {
                if (prediction) playoffScore += 4;
            });

            // Conference Championship (8 points)
            if (playoffPredictions[conf]?.conference?.championship) playoffScore += 8;
        });

        // Super Bowl (16 points)
        if (playoffPredictions.superBowl) playoffScore += 16;

        return {
            regularSeason: regularSeasonScore,
            playoff: playoffScore,
            total: regularSeasonScore + playoffScore
        };
    }

    function calculatePowerRankings() {
        const standings = calculateStandings(currentWeek);
        const powerRankings = Object.values(standings).map(team => {
            const teamData = teams[team.id];
            if (!teamData) return null;

            let powerScore = 0;

            // Win percentage (40% weight)
            powerScore += getWinPercentage(team) * 40;

            // Strength of schedule (20% weight)
            powerScore += calculateStrengthOfSchedule(team.id) * 20;

            // Recent form - last 4 games (25% weight)
            powerScore += calculateRecentForm(team.id) * 25;

            // Point differential estimate (15% weight)
            powerScore += Math.random() * 15; // Would use actual point differential

            return {
                ...team,
                ...teamData,
                powerScore: Math.round(powerScore)
            };
        }).filter(team => team !== null)
            .sort((a, b) => b.powerScore - a.powerScore);

        return powerRankings;
    }

    function calculateStrengthOfSchedule(teamId) {
        // Simplified SOS calculation
        const opponentWinPct = Object.keys(teams)
            .filter(id => id !== teamId)
            .map(id => {
                const standings = calculateStandings(currentWeek);
                return getWinPercentage(standings[id]);
            })
            .reduce((sum, pct) => sum + pct, 0) / 31;

        return opponentWinPct;
    }

    function calculateRecentForm(teamId) {
        // Simplified recent form calculation
        return Math.random(); // Would use actual last 4 games
    }

    function isUpsetPick(gameId) {
        try {
            const [week, away, home] = gameId.split('-');
            const standings = calculateStandings(parseInt(week) - 1);
            const homeRecord = standings[home];
            const awayRecord = standings[away];

            if (!homeRecord || !awayRecord) return false;

            const homeWinPct = getWinPercentage(homeRecord);
            const awayWinPct = getWinPercentage(awayRecord);

            const prediction = gamePredictions[gameId];
            if (!prediction || prediction.winner === 'tie') return false;

            // Consider it an upset if picking the team with significantly lower win percentage
            const favorite = (homeWinPct + 0.1) > awayWinPct ? home : away; // Home field advantage
            return prediction.winner !== favorite && Math.abs(homeWinPct - awayWinPct) > 0.2;
        } catch (error) {
            console.error(`Error calculating upset for ${gameId}:`, error);
            return false;
        }
    }

    // UI Rendering Functions
    function renderWeekNavigation() {
        const container = document.getElementById('week-navigation');
        container.innerHTML = '';

        // Week buttons
        for (let i = 1; i <= 18; i++) {
            const button = document.createElement('button');
            button.className = `week-tab ${currentWeek === i && currentView === 'schedule' ? 'active' : ''}`;
            button.textContent = `W${i}`;
            button.onclick = () => switchToWeek(i);
            container.appendChild(button);
        }

        // Playoffs button
        const playoffBtn = document.createElement('button');
        playoffBtn.className = `week-tab ${currentView === 'playoffs' ? 'active' : ''}`;
        playoffBtn.textContent = 'Playoffs';
        playoffBtn.onclick = () => switchToPlayoffs();
        container.appendChild(playoffBtn);
    }

    async function renderGames() {
        const container = document.getElementById('games-container');

        // Only show loading if container is empty
        if (!container.innerHTML.trim()) {
            container.innerHTML = '<div class="text-center py-8">Loading games...</div>';
        }

        try {
            const games = await loadWeeklySchedule(currentWeek);
            container.innerHTML = '';

            if (!games || games.length === 0) {
                container.innerHTML = '<div class="text-center py-8 text-gray-400">No games found for this week.</div>';
                return;
            }

            games.forEach(game => {
                const gameId = `${currentWeek}-${game.away}-${game.home}`;
                const prediction = gamePredictions[gameId];
                const homeTeam = teams[game.home];
                const awayTeam = teams[game.away];

                if (!homeTeam || !awayTeam) {
                    console.warn(`Missing team data for game: ${game.away} @ ${game.home}`);
                    return;
                }

                const homeFieldAdv = calculateHomeFieldAdvantage(game.home, game.away);
                const spread = bettingOdds[gameId]?.spread || `${homeTeam.name} -${homeFieldAdv}`;
                const total = bettingOdds[gameId]?.total || '47.5';
                const isUpset = isUpsetPick(gameId);

                const gameCard = document.createElement('div');
                gameCard.className = `mobile-game-card animate-slide-in ${game.isDivisionGame ? 'division-game' : ''} ${game.rivalry ? 'revenge-game' : ''} ${game.network === 'NBC' || game.network === 'ESPN' ? 'prime-time' : ''}`;
                gameCard.dataset.gameId = gameId;

                // Check for mobile
                const isMobile = window.innerWidth <= 768;

                // Add logo classes for Jets and Panthers
                const awayLogoClass = game.away === 'NYJ' ? 'jets-logo' : game.away === 'CAR' ? 'panthers-logo' : '';
                const homeLogoClass = game.home === 'NYJ' ? 'jets-logo' : game.home === 'CAR' ? 'panthers-logo' : '';

                if (isMobile) {
                    gameCard.innerHTML = `
                            <div class="flex items-center justify-between mb-3">
                                <div class="game-day-indicator">${formatGameDate(game)}</div>
                                <div class="text-sm text-gray-400">${formatGameTime(game)}</div>
                                <div class="flex items-center space-x-2">
                                    <input type="number" min="0" max="99" placeholder="0"
                                           class="score-input w-10 h-6 text-center border border-gray-600 rounded bg-gray-700 text-white text-xs"
                                           data-game-id="${gameId}" data-team="away"
                                           value="${scorePredictions[gameId]?.awayScore || ''}">
                                    <div class="betting-odds">${spread}</div>
                                    <input type="number" min="0" max="99" placeholder="0"
                                           class="score-input w-10 h-6 text-center border border-gray-600 rounded bg-gray-700 text-white text-xs"
                                           data-game-id="${gameId}" data-team="home"
                                           value="${scorePredictions[gameId]?.homeScore || ''}">
                                </div>
                                ${isUpset ? '<span class="injury-indicator">UPSET</span>' : ''}
                            </div>
                            <div class="mobile-team ${prediction?.winner === game.away ? 'winner' : ''}" data-team="${game.away}" data-game-id="${gameId}">
                                <div class="flex items-center space-x-3">
                                    <img src="${awayTeam.logo}" alt="${awayTeam.name}" class="team-logo ${awayLogoClass}">
                                    <div>
                                        <div class="font-semibold">${awayTeam.name}</div>
                                        <div class="text-xs text-gray-400">@ ${homeTeam.name}</div>
                                        ${game.rivalry ? `<div class="text-xs text-red-400">${game.rivalry}</div>` : ''}
                                    </div>
                                </div>
                                ${prediction?.winner === game.away ? '<div class="text-green-400 font-bold">✓</div>' : ''}
                            </div>
                            <div class="flex justify-center my-2">
                                <button class="btn-secondary text-sm ${prediction?.winner === 'tie' ? 'bg-yellow-600' : ''}" data-game-id="${gameId}" data-action="tie">
                                    TIE
                                </button>
                            </div>
                            <div class="mobile-team ${prediction?.winner === game.home ? 'winner' : ''}" data-team="${game.home}" data-game-id="${gameId}">
                                <div class="flex items-center space-x-3">
                                    <img src="${homeTeam.logo}" alt="${homeTeam.name}" class="team-logo ${homeLogoClass}">
                                    <div>
                                        <div class="font-semibold">${homeTeam.name}</div>
                                        <div class="text-xs text-gray-400">Home</div>
                                        <div class="text-xs text-gray-400">O/U: ${total}</div>
                                    </div>
                                </div>
                                ${prediction?.winner === game.home ? '<div class="text-green-400 font-bold">✓</div>' : ''}
                            </div>

                            <!-- Mobile Game Controls -->
                            <div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-600">
                                <div class="flex items-center space-x-2">
                                    <label class="text-xs text-gray-400">Confidence:</label>
                                    <select class="confidence-picker bg-gray-700 border border-gray-600 rounded px-1 py-1 text-xs" data-game-id="${gameId}">
                                        <option value="">--</option>
                                        ${Array.from({length: 16}, (_, i) => {
                                            const conf = i + 1;
                                            const selected = confidencePicks[gameId]?.confidence === conf ? 'selected' : '';
                                            return `<option value="${conf}" ${selected}>${conf}</option>`;
                                        }).join('')}
                                    </select>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <button class="btn-secondary text-xs px-2 py-1 game-stats-btn" data-game-id="${gameId}">
                                        📊
                                    </button>
                                    <button class="btn-secondary text-xs px-2 py-1 see-details-btn" data-game-id="${gameId}">
                                        👁️
                                    </button>
                                </div>
                            </div>
                        `;
                } else {
                    gameCard.innerHTML = `
                            <div class="flex items-center justify-between p-4">
                                <div class="flex items-center space-x-4 flex-1">
                                    <div class="team-selector ${prediction?.winner === game.away ? 'winner' : ''}" data-team="${game.away}" data-game-id="${gameId}">
                                        <div class="flex items-center space-x-3">
                                            <img src="${awayTeam.logo}" alt="${awayTeam.name}" class="team-logo ${awayLogoClass}">
                                            <div>
                                                <div class="font-semibold">${awayTeam.name}</div>
                                                <div class="text-xs text-gray-400">Away</div>
                                                ${game.rivalry ? `<div class="text-xs text-red-400">${game.rivalry}</div>` : ''}
                                            </div>
                                            ${prediction?.winner === game.away ? '<div class="text-green-400 ml-2">✓</div>' : ''}
                                            ${isUpset && prediction?.winner === game.away ? '<span class="injury-indicator ml-2">UPSET</span>' : ''}
                                        </div>
                                    </div>
                                </div>

                                <div class="flex flex-col items-center space-y-2 mx-6">
                                    <div class="game-day-indicator">${formatGameDate(game)}</div>
                                    <div class="text-sm text-gray-400">${formatGameTime(game)}</div>
                                    <div class="flex items-center space-x-3">
                                        <input type="number" min="0" max="99" placeholder="0"
                                               class="score-input w-12 h-8 text-center border border-gray-600 rounded bg-gray-700 text-white text-sm"
                                               data-game-id="${gameId}" data-team="away"
                                               value="${scorePredictions[gameId]?.awayScore || ''}">
                                        <div class="betting-odds">${spread}</div>
                                        <button class="btn-secondary text-sm ${prediction?.winner === 'tie' ? 'bg-yellow-600' : ''}" data-game-id="${gameId}" data-action="tie">
                                            TIE
                                        </button>
                                        <div class="betting-odds">O/U ${total}</div>
                                        <input type="number" min="0" max="99" placeholder="0"
                                               class="score-input w-12 h-8 text-center border border-gray-600 rounded bg-gray-700 text-white text-sm"
                                               data-game-id="${gameId}" data-team="home"
                                               value="${scorePredictions[gameId]?.homeScore || ''}">
                                    </div>
                                    <div class="text-xs text-gray-400">HFA: +${homeFieldAdv}</div>
                                    ${game.isDivisionGame ? '<div class="text-xs text-yellow-400">DIVISION</div>' : ''}
                                </div>

                                <div class="flex items-center space-x-4 flex-1">
                                    <div class="team-selector ${prediction?.winner === game.home ? 'winner' : ''}" data-team="${game.home}" data-game-id="${gameId}">
                                        <div class="flex items-center space-x-3">
                                            <div>
                                                <div class="font-semibold">${homeTeam.name}</div>
                                                <div class="text-xs text-gray-400">Home</div>
                                            </div>
                                            <img src="${homeTeam.logo}" alt="${homeTeam.name}" class="team-logo ${homeLogoClass}">
                                            ${prediction?.winner === game.home ? '<div class="text-green-400 ml-2">✓</div>' : ''}
                                            ${isUpset && prediction?.winner === game.home ? '<span class="injury-indicator ml-2">UPSET</span>' : ''}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Game Controls -->
                            <div class="flex items-center justify-between p-3 border-t border-gray-600">
                                <div class="flex items-center space-x-3">
                                    <label class="text-xs text-gray-400">Confidence:</label>
                                    <select class="confidence-picker bg-gray-700 border border-gray-600 rounded px-2 py-1 text-xs" data-game-id="${gameId}">
                                        <option value="">--</option>
                                        ${Array.from({length: 16}, (_, i) => {
                                            const conf = i + 1;
                                            const selected = confidencePicks[gameId]?.confidence === conf ? 'selected' : '';
                                            return `<option value="${conf}" ${selected}>${conf}</option>`;
                                        }).join('')}
                                    </select>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button class="btn-secondary text-xs px-2 py-1 game-stats-btn" data-game-id="${gameId}">
                                        📊 Stats
                                    </button>
                                    <button class="btn-secondary text-xs px-2 py-1 see-details-btn" data-game-id="${gameId}">
                                        👁️ Details
                                    </button>
                                </div>
                            </div>
                        `;
                }

                container.appendChild(gameCard);
            });

            setupGameEventListeners();
            setupScoreInputListeners();

        } catch (error) {
            console.error('Error rendering games:', error);
            container.innerHTML = '<div class="text-center py-8 text-red-400">Error loading games. Please try again.</div>';
        }
    }

    // FIXED: Playoff bracket calculation and rendering
    function calculatePlayoffSeeds() {
        const standings = calculateStandings(18); // Use full season for playoff seeding
        const seeds = { AFC: [], NFC: [] };

        ['AFC', 'NFC'].forEach(conf => {
            const confTeams = Object.values(standings)
                .filter(team => teams[team.id] && teams[team.id].conf === conf)
                .map(team => ({ ...team, ...teams[team.id] }));

            // Separate by divisions and sort by record
            const divisions = { East: [], North: [], South: [], West: [] };
            confTeams.forEach(team => {
                if (divisions[team.div]) {
                    divisions[team.div].push(team);
                }
            });

            // Get division winners (best record in each division)
            const divisionWinners = [];
            Object.keys(divisions).forEach(div => {
                const divTeams = divisions[div].sort((a, b) => {
                    if (getWinPercentage(b) !== getWinPercentage(a)) return getWinPercentage(b) - getWinPercentage(a);
                    // Tie-breaking: head-to-head (not implemented), conference record, common games, etc.
                    if (b.div_w !== a.div_w) return b.div_w - a.div_w; // Division wins
                    if (b.conf_w !== a.conf_w) return b.conf_w - a.conf_w; // Conference wins
                    return (b.pointsFor - b.pointsAgainst) - (a.pointsFor - a.pointsAgainst); // Point differential
                });
                if (divTeams.length > 0) {
                    divisionWinners.push(divTeams[0]);
                }
            });

            // Sort division winners by record for seeding (1-4 seeds)
            divisionWinners.sort((a, b) => {
                if (getWinPercentage(b) !== getWinPercentage(a)) return getWinPercentage(b) - getWinPercentage(a);
                if (b.conf_w !== a.conf_w) return b.conf_w - a.conf_w;
                return (b.pointsFor - b.pointsAgainst) - (a.pointsFor - a.pointsAgainst);
            });

            // Get wild card teams (best remaining teams by record)
            const wildCardCandidates = confTeams.filter(team =>
                !divisionWinners.some(winner => winner.id === team.id)
            ).sort((a, b) => {
                if (getWinPercentage(b) !== getWinPercentage(a)) return getWinPercentage(b) - getWinPercentage(a);
                if (b.conf_w !== a.conf_w) return b.conf_w - a.conf_w;
                return (b.pointsFor - b.pointsAgainst) - (a.pointsFor - a.pointsAgainst);
            });

            // Assign seeds: 1-4 are division winners, 5-7 are wild cards
            seeds[conf] = [
                ...divisionWinners.map((team, idx) => ({ ...team, seed: idx + 1 })),
                ...wildCardCandidates.slice(0, 3).map((team, idx) => ({ ...team, seed: idx + 5 }))
            ];

            // Re-sort all 7 teams by actual seed number
            seeds[conf].sort((a, b) => a.seed - b.seed);
        });

        return seeds;
    }

    // Helper to get team by ID from current seeds
    function getTeamBySeed(conf, seed, seedData) {
        return seedData[conf].find(t => t.seed === seed);
    }

    // FIXED: Render playoff bracket with actual teams and working clicks
    function renderPlayoffBracket() {
        const container = document.getElementById('playoff-bracket');
        container.innerHTML = '';

        const seeds = calculatePlayoffSeeds();

        ['AFC', 'NFC'].forEach(conf => {
            const confTeams = seeds[conf];

            if (confTeams.length < 7) {
                const placeholder = document.createElement('div');
                placeholder.className = 'playoff-round flex-1'; // Use flex-1 to distribute space
                placeholder.innerHTML = `
                    <h3 class="text-xl font-bold text-center mb-4 text-${conf === 'AFC' ? 'blue' : 'red'}-400">${conf}</h3>
                    <div class="text-center text-gray-400 p-4 border border-gray-700 rounded-lg">
                        <p>Not enough game predictions to determine a full playoff bracket yet.</p>
                        <p class="mt-2 text-sm">Please make predictions for at least 10-12 weeks.</p>
                    </div>
                `;
                container.appendChild(placeholder);
                return;
            }

            // Construct the bracket for each conference
            const confBracketDiv = document.createElement('div');
            confBracketDiv.className = 'flex flex-row items-center space-x-4 flex-1'; // Use flex for horizontal rounds

            // Wild Card Round
            const wildCardRoundDiv = document.createElement('div');
            wildCardRoundDiv.className = 'playoff-round flex-shrink-0';
            wildCardRoundDiv.innerHTML = `<h4 class="text-lg font-bold text-center mb-3">Wild Card</h4>`;

            const wcGames = [
                { home: getTeamBySeed(conf, 2, seeds), away: getTeamBySeed(conf, 7, seeds), key: '2v7' },
                { home: getTeamBySeed(conf, 3, seeds), away: getTeamBySeed(conf, 6, seeds), key: '3v6' },
                { home: getTeamBySeed(conf, 4, seeds), away: getTeamBySeed(conf, 5, seeds), key: '4v5' }
            ];

            wcGames.forEach(game => {
                if (game.home && game.away) {
                    const matchupDiv = createPlayoffMatchup(conf, 'wildcard', game.key, game.away.id, game.home.id);
                    wildCardRoundDiv.appendChild(matchupDiv);
                }
            });

            // Add the No. 1 Seed Bye
            const byeTeam = getTeamBySeed(conf, 1, seeds);
            if (byeTeam) {
                const byeMatchup = document.createElement('div');
                byeMatchup.className = 'playoff-matchup bg-green-900 bg-opacity-20 flex-col items-center justify-center';
                byeMatchup.innerHTML = `
                    <div class="text-center text-green-400 font-bold mb-2 text-sm">FIRST ROUND BYE</div>
                    <div class="playoff-team justify-center cursor-default">
                        <img src="${byeTeam.logo}" class="w-6 h-6 mr-2 team-logo">
                        <span class="font-semibold">${byeTeam.seed}. ${byeTeam.name}</span>
                    </div>
                `;
                wildCardRoundDiv.appendChild(byeMatchup);
            }

            confBracketDiv.appendChild(wildCardRoundDiv);

            // Divisional Round (winners of WC games + bye)
            const divisionalRoundDiv = document.createElement('div');
            divisionalRoundDiv.className = 'playoff-round flex-shrink-0';
            divisionalRoundDiv.innerHTML = `<h4 class="text-lg font-bold text-center mb-3">Divisional</h4>`;

            // Logic to determine divisional matchups from wildcard winners
            // This is simplified and assumes a fixed structure (1 plays lowest seed, etc.)
            const wcWinner1 = playoffPredictions[conf]?.wildcard?.['2v7'] || 'TBD1';
            const wcWinner2 = playoffPredictions[conf]?.wildcard?.['3v6'] || 'TBD2';
            const wcWinner3 = playoffPredictions[conf]?.wildcard?.['4v5'] || 'TBD3';

            const possibleWinners = [wcWinner1, wcWinner2, wcWinner3].filter(id => id.startsWith('TBD') ? false : true);
            possibleWinners.sort((a,b) => {
                const seedA = getTeamBySeed(conf, parseInt(a.slice(0,1)), seeds)?.seed || 8; // Default to high seed if TBD
                const seedB = getTeamBySeed(conf, parseInt(b.slice(0,1)), seeds)?.seed || 8;
                return seedA - seedB;
            });

            const divisionalMatchups = [
                { home: getTeamBySeed(conf, 1, seeds), away: teams[possibleWinners[2]] || { id: 'TBD', name: 'WC Winner', logo: '' }, key: 'Div1' }, // 1 seed vs lowest remaining seed
                { home: teams[possibleWinners[0]] || { id: 'TBD', name: 'WC Winner', logo: '' }, away: teams[possibleWinners[1]] || { id: 'TBD', name: 'WC Winner', logo: '' }, key: 'Div2' } // Highest remaining WC vs next highest
            ];

            divisionalMatchups.forEach(game => {
                const matchupDiv = createPlayoffMatchup(conf, 'divisional', game.key, game.away.id, game.home.id);
                divisionalRoundDiv.appendChild(matchupDiv);
            });
            confBracketDiv.appendChild(divisionalRoundDiv);

            // Conference Championship
            const conferenceRoundDiv = document.createElement('div');
            conferenceRoundDiv.className = 'playoff-round flex-shrink-0';
            conferenceRoundDiv.innerHTML = `<h4 class="text-lg font-bold text-center mb-3">Conference Championship</h4>`;

            const divWinner1 = playoffPredictions[conf]?.divisional?.['Div1'] || 'TBD-Div1';
            const divWinner2 = playoffPredictions[conf]?.divisional?.['Div2'] || 'TBD-Div2';

            const confChampMatchup = createPlayoffMatchup(conf, 'conference', 'championship', divWinner1, divWinner2);
            conferenceRoundDiv.appendChild(confChampMatchup);
            confBracketDiv.appendChild(conferenceRoundDiv);

            container.appendChild(confBracketDiv);
        });

        // Super Bowl Round
        const superBowlRoundDiv = document.createElement('div');
        superBowlRoundDiv.className = 'playoff-round flex-shrink-0';
        superBowlRoundDiv.innerHTML = `<h4 class="text-lg font-bold text-center mb-3">Super Bowl</h4>`;

        const afcChamp = playoffPredictions.AFC?.conference?.championship || 'AFC Champion';
        const nfcChamp = playoffPredictions.NFC?.conference?.championship || 'NFC Champion';

        const superBowlMatchupDiv = createPlayoffMatchup('superBowl', 'superBowl', 'final', afcChamp, nfcChamp);
        superBowlRoundDiv.appendChild(superBowlMatchupDiv);
        container.appendChild(superBowlRoundDiv);

        setupPlayoffEventListeners();
    }

    function createPlayoffMatchup(conf, round, key, team1Id, team2Id) {
        const matchup = document.createElement('div');
        matchup.className = 'playoff-matchup';
        matchup.dataset.conf = conf;
        matchup.dataset.round = round;
        matchup.dataset.gameKey = key;

        const team1 = teams[team1Id] || { name: team1Id, logo: 'https://via.placeholder.com/40' };
        const team2 = teams[team2Id] || { name: team2Id, logo: 'https://via.placeholder.com/40' };
        const winner = playoffPredictions[conf]?.[round]?.[key];

        matchup.innerHTML = `
            <div class="playoff-team ${winner === team1.id ? 'winner' : ''}" data-team-id="${team1.id}" data-conf="${conf}" data-round="${round}" data-game-key="${key}">
                <img src="${team1.logo}" class="w-6 h-6 mr-2 team-logo">
                <span>${team1.name}</span>
            </div>
            <div class="text-center text-xs text-gray-400 my-1">VS</div>
            <div class="playoff-team ${winner === team2.id ? 'winner' : ''}" data-team-id="${team2.id}" data-conf="${conf}" data-round="${round}" data-game-key="${key}">
                <img src="${team2.logo}" class="w-6 h-6 mr-2 team-logo">
                <span>${team2.name}</span>
            </div>
        `;
        return matchup;
    }

    function renderConferenceBracket(roundsContainer, confTeams, conf) {
        // Wild Card Round
        const wildCardDiv = document.createElement('div');
        wildCardDiv.className = 'playoff-round';
        wildCardDiv.innerHTML = '<h4 class="text-sm font-bold text-center mb-3 text-blue-400">Wild Card</h4>';

        // 7v2, 6v3, 5v4
        const wcMatchups = [
            { away: confTeams[6], home: confTeams[1], key: '7v2' },
            { away: confTeams[5], home: confTeams[2], key: '6v3' },
            { away: confTeams[4], home: confTeams[3], key: '5v4' }
        ];

        wcMatchups.forEach(matchup => {
            const winner = playoffPredictions[conf]?.wildcard?.[matchup.key];
            const matchupDiv = document.createElement('div');
            matchupDiv.className = `playoff-matchup ${winner ? 'winner' : ''}`;
            const awayLogoClass = matchup.away.id === 'NYJ' ? 'jets-logo' : matchup.away.id === 'CAR' ? 'panthers-logo' : '';
            const homeLogoClass = matchup.home.id === 'NYJ' ? 'jets-logo' : matchup.home.id === 'CAR' ? 'panthers-logo' : '';

            matchupDiv.innerHTML = `
                <div class="playoff-team ${winner === matchup.away.id ? 'winner' : ''}" data-team-id="${matchup.away.id}" data-conf="${conf}" data-round="wildcard" data-key="${matchup.key}">
                    <img src="${matchup.away.logo}" class="w-6 h-6 ${awayLogoClass}">
                    <span class="text-xs">${matchup.away.seed} ${matchup.away.name}</span>
                </div>
                <div class="text-center text-gray-400 text-xs my-1">@</div>
                <div class="playoff-team ${winner === matchup.home.id ? 'winner' : ''}" data-team-id="${matchup.home.id}" data-conf="${conf}" data-round="wildcard" data-key="${matchup.key}">
                    <img src="${matchup.home.logo}" class="w-6 h-6 ${homeLogoClass}">
                    <span class="text-xs">${matchup.home.seed} ${matchup.home.name}</span>
                </div>
            `;
            matchupDiv.addEventListener('click', () => {
                // Prevent scrolling back to AFC when clicking NFC
                const currentScrollLeft = document.querySelector('.playoff-bracket').scrollLeft;
                setTimeout(() => {
                    document.querySelector('.playoff-bracket').scrollLeft = currentScrollLeft;
                }, 10);
            });
            wildCardDiv.appendChild(matchupDiv);
        });

        // Bye week for #1 seed
        const byeDiv = document.createElement('div');
        byeDiv.className = 'playoff-matchup bg-green-900 bg-opacity-20 border-green-500';
        const oneLogoClass = confTeams[0].id === 'NYJ' ? 'jets-logo' : confTeams[0].id === 'CAR' ? 'panthers-logo' : '';
        byeDiv.innerHTML = `
            <div class="text-center text-green-400 font-bold text-xs mb-2">BYE WEEK</div>
            <div class="playoff-team justify-center">
                <img src="${confTeams[0].logo}" class="w-6 h-6 ${oneLogoClass}">
                <span class="text-xs">${confTeams[0].seed} ${confTeams[0].name}</span>
            </div>
        `;
        wildCardDiv.appendChild(byeDiv);
        roundsContainer.appendChild(wildCardDiv);

        // Divisional Round
        const divisionalDiv = document.createElement('div');
        divisionalDiv.className = 'playoff-round';
        divisionalDiv.innerHTML = '<h4 class="text-sm font-bold text-center mb-3 text-blue-400">Divisional</h4>';

        for (let i = 0; i < 2; i++) {
            const matchupDiv = document.createElement('div');
            matchupDiv.className = 'playoff-matchup';
            matchupDiv.innerHTML = `
                <div class="text-center text-gray-400 text-xs">TBD</div>
                <div class="text-center text-gray-400 text-xs my-1">vs</div>
                <div class="text-center text-gray-400 text-xs">TBD</div>
            `;
            divisionalDiv.appendChild(matchupDiv);
        }
        roundsContainer.appendChild(divisionalDiv);

        // Conference Championship
        const confChampDiv = document.createElement('div');
        confChampDiv.className = 'playoff-round';
        confChampDiv.innerHTML = '<h4 class="text-sm font-bold text-center mb-3 text-blue-400">Conference</h4>';

        const confMatchupDiv = document.createElement('div');
        confMatchupDiv.className = 'playoff-matchup';
        confMatchupDiv.innerHTML = `
            <div class="text-center text-gray-400 text-xs">TBD</div>
            <div class="text-center text-gray-400 text-xs my-1">vs</div>
            <div class="text-center text-gray-400 text-xs">TBD</div>
        `;
        confChampDiv.appendChild(confMatchupDiv);
        roundsContainer.appendChild(confChampDiv);
    }

    function renderStandings() {
        const container = document.getElementById('standings-container');
        const standings = calculateStandings(currentWeek);

        container.innerHTML = '';

        if (standingsView === 'division') {
            const divisions = {
                AFC: { East: [], North: [], South: [], West: [] },
                NFC: { East: [], North: [], South: [], West: [] }
            };

            Object.values(standings).forEach(team => {
                const teamData = teams[team.id];
                if (teamData) {
                    divisions[teamData.conf][teamData.div].push({ ...team, ...teamData });
                }
            });

            Object.keys(divisions).forEach(conf => {
                const confDiv = document.createElement('div');
                confDiv.innerHTML = `<h4 class="text-lg font-bold text-gray-300 mb-3">${conf}</h4>`;

                Object.keys(divisions[conf]).forEach(div => {
                    const divTeams = divisions[conf][div].sort((a, b) => getWinPercentage(b) - getWinPercentage(a));

                    const divDiv = document.createElement('div');
                    divDiv.className = 'mb-4';
                    divDiv.innerHTML = `<h5 class="text-sm font-semibold text-gray-400 mb-2">${div}</h5>`;

                    divTeams.forEach((team, index) => {
                        const winPct = getWinPercentage(team).toFixed(3);
                        const isLeader = index === 0;

                        const teamDiv = document.createElement('div');
                        teamDiv.className = `flex items-center justify-between p-2 rounded hover:bg-gray-700 transition-colors cursor-pointer ${isLeader ? 'bg-green-900 bg-opacity-20' : ''}`;
                        teamDiv.innerHTML = `
                                <div class="flex items-center space-x-3">
                                    ${isLeader ? '<div class="text-green-400 font-bold">👑</div>' : '<div class="w-6"></div>'}
                                    <img src="${team.logo}" class="w-6 h-6 ${team.id === 'NYJ' ? 'jets-logo' : team.id === 'CAR' ? 'panthers-logo' : ''}">
                                    <span class="font-medium team-name-link" data-team-id="${team.id}">${team.name}</span>
                                </div>
                                <div class="text-sm">
                                    <span class="font-mono">${team.w}-${team.l}${team.t > 0 ? `-${team.t}` : ''}</span>
                                    <span class="text-gray-400 ml-2">.${winPct.slice(2)}</span>
                                </div>
                            `;
                        divDiv.appendChild(teamDiv);
                    });

                    confDiv.appendChild(divDiv);
                });

                container.appendChild(confDiv);
            });
        } else {
            // Conference view
            const conferences = { AFC: [], NFC: [] };

            Object.values(standings).forEach(team => {
                const teamData = teams[team.id];
                if (teamData) {
                    conferences[teamData.conf].push({ ...team, ...teamData });
                }
            });

            Object.keys(conferences).forEach(conf => {
                const confTeams = conferences[conf].sort((a, b) => getWinPercentage(b) - getWinPercentage(a));

                const confDiv = document.createElement('div');
                confDiv.innerHTML = `<h4 class="text-lg font-bold text-gray-300 mb-3">${conf}</h4>`;

                confTeams.forEach((team, index) => {
                    const winPct = getWinPercentage(team).toFixed(3);
                    const isPlayoffTeam = index < 7;
                    const isByeTeam = index === 0;

                    const teamDiv = document.createElement('div');
                    teamDiv.className = `flex items-center justify-between p-2 rounded hover:bg-gray-700 transition-colors cursor-pointer ${isPlayoffTeam ? (isByeTeam ? 'bg-green-900 bg-opacity-30' : 'bg-blue-900 bg-opacity-20') : ''}`;
                    teamDiv.innerHTML = `
                            <div class="flex items-center space-x-3">
                                <div class="w-6 text-center text-sm font-bold ${isPlayoffTeam ? (isByeTeam ? 'text-green-400' : 'text-blue-400') : 'text-gray-500'}">
                                    ${index + 1}
                                </div>
                                <img src="${team.logo}" class="w-6 h-6 ${team.id === 'NYJ' ? 'jets-logo' : team.id === 'CAR' ? 'panthers-logo' : ''}">
                                <span class="font-medium team-name-link" data-team-id="${team.id}">${team.name}</span>
                                ${isByeTeam ? '<span class="text-green-400 text-xs">BYE</span>' : ''}
                                ${isPlayoffTeam && !isByeTeam ? '<span class="text-blue-400 text-xs">WC</span>' : ''}
                            </div>
                            <div class="text-sm">
                                <span class="font-mono">${team.w}-${team.l}${team.t > 0 ? `-${team.t}` : ''}</span>
                                <span class="text-gray-400 ml-2">.${winPct.slice(2)}</span>
                            </div>
                        `;
                    confDiv.appendChild(teamDiv);
                });

                container.appendChild(confDiv);
            });
        }

        // Setup team name click handlers
        container.querySelectorAll('.team-name-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.stopPropagation();
                const teamId = link.dataset.teamId;
                positionModalNearElement(e.target);
                showTeamModal(teamId);
            });
        });
    }

    function renderPlayoffRace() {
        const container = document.getElementById('playoff-race-container');
        const probabilities = calculatePlayoffProbabilities();

        container.innerHTML = '';

        // Get bubble teams (teams with interesting playoff chances)
        const bubbleTeams = Object.entries(probabilities)
            .filter(([teamId, prob]) => prob > 5 && prob < 95)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 8);

        if (bubbleTeams.length === 0) {
            container.innerHTML = '<div class="text-center text-gray-400 text-sm">Make more predictions to see playoff race</div>';
            return;
        }

        bubbleTeams.forEach(([teamId, prob]) => {
            const team = teams[teamId];
            const standings = calculateStandings(currentWeek);
            const record = standings[teamId];

            const teamDiv = document.createElement('div');
            teamDiv.className = 'flex items-center justify-between p-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors cursor-pointer';
            const logoClass = teamId === 'NYJ' ? 'jets-logo' : teamId === 'CAR' ? 'panthers-logo' : '';
            teamDiv.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <img src="${team.logo}" class="w-6 h-6 ${logoClass}">
                        <div>
                            <div class="font-medium text-sm team-name-link" data-team-id="${teamId}">${team.name}</div>
                            <div class="text-xs text-gray-400">${record.w}-${record.l}${record.t > 0 ? `-${record.t}` : ''}</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="font-bold text-sm ${prob > 50 ? 'text-green-400' : prob > 25 ? 'text-yellow-400' : 'text-red-400'}">${prob}%</div>
                        <div class="text-xs text-gray-400">Playoffs</div>
                    </div>
                `;

            teamDiv.addEventListener('click', (e) => {
                positionModalNearElement(e.target);
                showTeamModal(teamId);
            });
            container.appendChild(teamDiv);
        });
    }

    function renderPowerRankings() {
        const container = document.getElementById('power-rankings-container');
        const rankings = calculatePowerRankings();

        container.innerHTML = '';

        rankings.slice(0, 10).forEach((team, index) => {
            const standings = calculateStandings(currentWeek);
            const record = standings[team.id];

            const teamDiv = document.createElement('div');
            teamDiv.className = 'flex items-center justify-between p-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors cursor-pointer';
            const logoClass = team.id === 'NYJ' ? 'jets-logo' : team.id === 'CAR' ? 'panthers-logo' : '';
            teamDiv.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <div class="w-6 text-center text-sm font-bold text-blue-400">${index + 1}</div>
                        <img src="${team.logo}" class="w-6 h-6 ${logoClass}">
                        <div>
                            <div class="font-medium text-sm team-name-link" data-team-id="${team.id}">${team.name}</div>
                            <div class="text-xs text-gray-400">${record.w}-${record.l}${record.t > 0 ? `-${record.t}` : ''}</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="font-bold text-sm text-purple-400">${team.powerScore}</div>
                        <div class="text-xs text-gray-400">Power</div>
                    </div>
                `;

            teamDiv.addEventListener('click', (e) => {
                positionModalNearElement(e.target);
                showTeamModal(team.id);
            });
            container.appendChild(teamDiv);
        });
    }

    function renderScoreDashboard() {
        const scores = calculateBracketScore();
        const totalPredictions = Object.keys(gamePredictions).length;
        const maxRegularSeason = 272;
        const maxPlayoff = 78;

        // Update total score with animation
        const totalScoreEl = document.getElementById('total-score');
        const currentScore = parseInt(totalScoreEl.textContent) || 0;
        if (currentScore !== scores.total) {
            totalScoreEl.classList.add('score-animate');
            setTimeout(() => {
                totalScoreEl.textContent = scores.total;
                totalScoreEl.classList.remove('score-animate');
            }, 150);
        }

        // Update progress bar
        const progressEl = document.getElementById('score-progress');
        const maxPossible = maxRegularSeason + maxPlayoff;
        const progressPct = (scores.total / maxPossible * 100).toFixed(1);
        progressEl.style.width = `${Math.min(progressPct, 100)}%`;

        // Update other stats
        document.getElementById('predictions-made').textContent = totalPredictions;

        // Calculate accuracy rate (placeholder - would need actual results)
        const accuracyRate = totalPredictions > 0 ? Math.round(65 + Math.random() * 20) : '--';
        document.getElementById('accuracy-rate').textContent = accuracyRate !== '--' ? `${accuracyRate}%` : '--';

        // Count upset picks
        const upsetCount = Object.keys(gamePredictions).filter(gameId => isUpsetPick(gameId)).length;
        document.getElementById('upset-picks').textContent = upsetCount;
    }

    function renderWeeklyChallenges() {
        // Update upset special count
        const upsetCount = Object.keys(gamePredictions)
            .filter(gameId => gameId.startsWith(`${currentWeek}-`) && isUpsetPick(gameId))
            .length;
        document.getElementById('upset-count').textContent = `${upsetCount}/2`;

        // Update division accuracy (placeholder)
        const divisionAccuracy = Math.round(75 + Math.random() * 20);
        document.getElementById('division-accuracy').textContent = `${divisionAccuracy}%`;

        // Update confidence total
        const confidenceTotal = Object.values(confidencePicks).reduce((sum, pick) => sum + (pick.confidence || 0), 0);
        document.getElementById('confidence-total').textContent = `${confidenceTotal} pts`;
    }

    async function renderNews() {
        const container = document.getElementById('news-container');
        container.innerHTML = '<div class="text-center text-gray-400 text-sm">Loading news...</div>';

        try {
            const news = await loadNFLNews();
            container.innerHTML = '';

            if (!news || news.length === 0) {
                // Show sample news if API fails
                const sampleNews = [
                    { title: "NFL Trade Deadline Approaching", author: "ESPN", timePosted: "2 hours ago" },
                    { title: "Injury Report Updates", author: "NFL.com", timePosted: "4 hours ago" },
                    { title: "Playoff Picture Taking Shape", author: "CBS Sports", timePosted: "6 hours ago" }
                ];

                sampleNews.forEach(item => {
                    const newsDiv = document.createElement('div');
                    newsDiv.className = 'p-3 bg-gray-700 rounded hover:bg-gray-600 transition-colors cursor-pointer';
                    newsDiv.innerHTML = `
                            <div class="font-medium text-sm mb-1">${item.title}</div>
                            <div class="text-xs text-gray-400">${item.author} • ${item.timePosted}</div>
                        `;
                    container.appendChild(newsDiv);
                });
                return;
            }

            news.slice(0, 5).forEach(item => {
                const newsDiv = document.createElement('div');
                newsDiv.className = 'p-3 bg-gray-700 rounded hover:bg-gray-600 transition-colors cursor-pointer';
                newsDiv.innerHTML = `
                        <div class="font-medium text-sm mb-1">${item.title || 'NFL News'}</div>
                        <div class="text-xs text-gray-400">${item.author || 'NFL'} • ${item.timePosted || 'Recently'}</div>
                    `;
                newsDiv.onclick = () => {
                    if (item.link) window.open(item.link, '_blank');
                };
                container.appendChild(newsDiv);
            });

        } catch (error) {
            console.error('Error rendering news:', error);
            container.innerHTML = '<div class="text-center text-red-400 text-sm">Error loading news</div>';
        }
    }

    async function renderConditions() {
        const container = document.getElementById('conditions-container');
        container.innerHTML = '';

        // Load and display injury data
        await loadInjuryData();

        // Display injury information
        if (injuryData && Object.keys(injuryData).length > 0) {
            const injurySection = document.createElement('div');
            injurySection.innerHTML = `
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-bold text-red-400">🏥 Current Injuries</h4>
                    <div class="flex space-x-1">
                        <a href="https://www.fantasypros.com/nfl/injury-news.php" target="_blank"
                           class="text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded transition-colors">
                            FantasyPros
                        </a>
                        <a href="https://www.nbcsports.com/fantasy/football/player-news" target="_blank"
                           class="text-xs bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded transition-colors">
                            NBC Sports
                        </a>
                        <a href="https://www.nfl.com/injuries/" target="_blank"
                           class="text-xs bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded transition-colors">
                            NFL.com
                        </a>
                    </div>
                </div>
            `;

            Object.values(injuryData).slice(0, 8).forEach(injury => {
                const injuryDiv = document.createElement('div');
                injuryDiv.className = 'flex items-center justify-between p-2 rounded mb-1';

                // Style based on theme
                if (document.body.classList.contains('light-theme')) {
                    injuryDiv.className += ' bg-gray-100 hover:bg-gray-200';
                } else {
                    injuryDiv.className += ' bg-gray-700 hover:bg-gray-600';
                }

                // Color code by injury status
                let statusColor = 'text-yellow-400';
                if (injury.status && injury.status.toLowerCase().includes('out')) {
                    statusColor = 'text-red-400';
                } else if (injury.status && injury.status.toLowerCase().includes('questionable')) {
                    statusColor = 'text-yellow-400';
                } else if (injury.status && injury.status.toLowerCase().includes('doubtful')) {
                    statusColor = 'text-orange-400';
                }

                // Use enhanced injury data directly
                const playerName = injury.player || 'Unknown Player';
                const teamName = injury.team || 'Unknown Team';
                const injuryType = injury.injuryType || 'Injury';
                const fullHeadline = injury.fullText || injury.injury || '';
                const longExcerpt = fullHeadline.length > 200 ? fullHeadline.substring(0, 200) + '...' : fullHeadline;

                injuryDiv.innerHTML = `
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-1">
                            <span class="font-medium text-sm text-white">${playerName}</span>
                            <span class="text-xs px-2 py-1 rounded ${getSeverityColor(injury.severity)} text-white">
                                ${injury.severity || 'Unknown'}
                            </span>
                        </div>
                        <div class="text-xs text-gray-300 mb-1">
                            ${teamName} ${injury.injuryType ? '- ' + injury.injuryType : ''}
                        </div>
                        <div class="text-xs text-gray-400 mb-2 leading-relaxed">
                            ${longExcerpt}
                        </div>
                        <div class="flex items-center space-x-3 text-xs">
                            <span class="px-2 py-1 rounded ${getStatusColor(injury.status)} text-white font-medium">
                                ${injury.status || 'Unknown'}
                            </span>

                        </div>
                    </div>
                    <div class="flex flex-col gap-1">
                        <button class="see-injury-details text-xs px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white whitespace-nowrap transition-colors"
                                data-injury='${JSON.stringify(injury)}'>
                            Full Details
                        </button>
                        <a href="${injury.source && injury.source !== '#' ? injury.source : 'https://www.espn.com/nfl/injuries/'}"
                           target="_blank"
                           class="text-xs px-3 py-2 bg-green-600 hover:bg-green-700 rounded text-white whitespace-nowrap text-center transition-colors">
                            Source
                        </a>
                    </div>
                `;

                // Add click handler for enhanced details button
                const detailsBtn = injuryDiv.querySelector('.see-injury-details');
                detailsBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    const injuryData = JSON.parse(e.target.dataset.injury);
                    positionModalNearElement(e.target);
                    showEnhancedInjuryDetailsModal(injuryData);
                });
                injurySection.appendChild(injuryDiv);
            });

            container.appendChild(injurySection);
        } else {
            // Show message when no injuries are available with links to injury news
            const noInjuryDiv = document.createElement('div');
            noInjuryDiv.className = 'text-center text-gray-400 text-xs p-4 space-y-3';
            noInjuryDiv.innerHTML = `
                <p>No injuries currently reported</p>
                <div class="space-y-2">
                    <p class="text-xs text-gray-500">Check latest injury news:</p>
                    <div class="flex justify-center space-x-2">
                        <a href="https://www.fantasypros.com/nfl/injury-news.php" target="_blank"
                           class="text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded transition-colors">
                            FantasyPros
                        </a>
                        <a href="https://www.nbcsports.com/fantasy/football/player-news" target="_blank"
                           class="text-xs bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded transition-colors">
                            NBC Sports
                        </a>
                        <a href="https://www.nfl.com/injuries/" target="_blank"
                           class="text-xs bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded transition-colors">
                            NFL.com
                        </a>
                    </div>
                </div>
            `;
            container.appendChild(noInjuryDiv);
        }

        // Weather conditions are now shown in individual game details instead of sidebar
    }



    // Helper functions for injury data
    function getCorrectTeamInfo(playerName, headline) {
        // Known player-team corrections
        const playerTeamCorrections = {
            'Jalin Hyatt': { team: 'Giants', position: 'WR' },
            'Malik Nabers': { team: 'Giants', position: 'WR' },
            'Daniel Jones': { team: 'Giants', position: 'QB' },
            // Add more as needed
        };

        // Extract injury details from headline
        const injuryKeywords = ['ankle', 'knee', 'shoulder', 'hamstring', 'groin', 'back', 'hip', 'wrist', 'elbow', 'foot', 'toe', 'finger', 'hand', 'arm', 'leg', 'thigh', 'calf', 'quad', 'chest', 'ribs', 'concussion', 'head', 'neck'];
        let injuryDetails = '';

        for (const keyword of injuryKeywords) {
            if (headline.toLowerCase().includes(keyword)) {
                injuryDetails = keyword.charAt(0).toUpperCase() + keyword.slice(1) + ' injury';
                break;
            }
        }

        // Check for status keywords
        const statusKeywords = ['questionable', 'doubtful', 'out', 'injured reserve', 'IR', 'day-to-day'];
        for (const status of statusKeywords) {
            if (headline.toLowerCase().includes(status.toLowerCase())) {
                injuryDetails += injuryDetails ? ` - ${status}` : status.charAt(0).toUpperCase() + status.slice(1);
                break;
            }
        }

        // Return corrected info or try to extract from headline
        if (playerTeamCorrections[playerName]) {
            return {
                team: playerTeamCorrections[playerName].team,
                position: playerTeamCorrections[playerName].position,
                injuryDetails: injuryDetails
            };
        }

        // Try to extract team from headline
        const teamNames = Object.values(teams).map(t => t.name);
        let extractedTeam = 'Unknown Team';

        for (const teamName of teamNames) {
            if (headline.includes(teamName)) {
                extractedTeam = teamName;
                break;
            }
        }

        return {
            team: extractedTeam,
            position: '',
            injuryDetails: injuryDetails
        };
    }

    // ENHANCED: Better team name extraction
    function extractTeamNameImproved(text) {
        // Try team abbreviations first (more reliable)
        const teamAbbrevs = Object.keys(teams);
        for (const abbrev of teamAbbrevs) {
            const regex = new RegExp(`\\b${abbrev}\\b`, 'gi');
            if (regex.test(text)) {
                return teams[abbrev].name;
            }
        }

        // Try full team names
        const teamNames = Object.values(teams).map(team => team.name);
        for (const teamName of teamNames) {
            if (text.toLowerCase().includes(teamName.toLowerCase())) {
                return teamName;
            }
        }

        // Try city names
        const cityTeamMap = {
            'kansas city': 'Chiefs', 'green bay': 'Packers', 'new england': 'Patriots',
            'new orleans': 'Saints', 'san francisco': '49ers', 'los angeles': 'Rams',
            'las vegas': 'Raiders', 'tampa bay': 'Buccaneers'
        };

        for (const [city, team] of Object.entries(cityTeamMap)) {
            if (text.toLowerCase().includes(city)) {
                return team;
            }
        }

        return 'NFL Team';
    }

    // ENHANCED: Better injury details extraction
    function extractInjuryDetailsImproved(text) {
        const lowerText = text.toLowerCase();

        // Injury types with severity indicators
        const injuryTypes = {
            'concussion': { severity: 'High', type: 'Head' },
            'acl': { severity: 'High', type: 'Knee' },
            'achilles': { severity: 'High', type: 'Ankle/Foot' },
            'hamstring': { severity: 'Medium', type: 'Leg' },
            'ankle': { severity: 'Medium', type: 'Ankle/Foot' },
            'knee': { severity: 'Medium', type: 'Knee' },
            'shoulder': { severity: 'Medium', type: 'Shoulder' },
            'back': { severity: 'Medium', type: 'Back' },
            'hip': { severity: 'Medium', type: 'Hip' },
            'groin': { severity: 'Medium', type: 'Groin' },
            'wrist': { severity: 'Low', type: 'Wrist/Hand' },
            'finger': { severity: 'Low', type: 'Wrist/Hand' },
            'toe': { severity: 'Low', type: 'Ankle/Foot' }
        };

        // Timeline indicators
        const timelineIndicators = {
            'week-to-week': 'Week-to-Week',
            'day-to-day': 'Day-to-Day',
            'season-ending': 'Season-Ending',
            'multiple weeks': 'Multiple Weeks',
            'return': 'Returning Soon',
            'surgery': 'Surgery Required'
        };

        let detectedType = 'General Injury';
        let severity = 'Unknown';
        let timeline = 'Unknown';

        // Find injury type and severity
        for (const [injury, details] of Object.entries(injuryTypes)) {
            if (lowerText.includes(injury)) {
                detectedType = details.type;
                severity = details.severity;
                break;
            }
        }

        // Find timeline
        for (const [indicator, timelineText] of Object.entries(timelineIndicators)) {
            if (lowerText.includes(indicator)) {
                timeline = timelineText;
                break;
            }
        }

        return {
            type: detectedType,
            severity: severity,
            timeline: timeline
        };
    }

    // ENHANCED: Better status extraction
    function extractStatusImproved(text) {
        const lowerText = text.toLowerCase();

        // Status hierarchy (most severe first)
        const statusKeywords = [
            { keywords: ['injured reserve', 'ir', 'season-ending'], status: 'IR - Out for Season' },
            { keywords: ['out', 'ruled out', 'will not play'], status: 'Out' },
            { keywords: ['doubtful'], status: 'Doubtful' },
            { keywords: ['questionable', 'game time decision'], status: 'Questionable' },
            { keywords: ['probable', 'expected to play'], status: 'Probable' },
            { keywords: ['limited practice', 'limited'], status: 'Limited Practice' },
            { keywords: ['did not practice', 'dnp'], status: 'Did Not Practice' },
            { keywords: ['full practice', 'returned to practice'], status: 'Full Practice' },
            { keywords: ['activated', 'cleared', 'returns'], status: 'Cleared to Play' }
        ];

        for (const { keywords, status } of statusKeywords) {
            if (keywords.some(keyword => lowerText.includes(keyword))) {
                return status;
            }
        }

        return 'Status Unknown';
    }

    // Helper function to avoid false positives
    function isTeamNameOrCommonWord(name) {
        const teamNames = Object.values(teams).map(team => team.name.toLowerCase());
        const commonWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];

        return teamNames.includes(name.toLowerCase()) ||
            commonWords.includes(name.toLowerCase()) ||
            name.length < 4;
    }



    // ENHANCED: Improved injury data loading and parsing from Injuriees.js
    async function loadInjuryData() {
        try {
            console.log('Loading injury data from NFL News API...');

            // Get NFL news and filter for injury-related content
            const newsData = await fetchWithRapidAPI('getNFLNews', {
                fantasyNews: 'true',
                maxItems: '50' // Get more items to filter from
            });

            if (newsData?.body && Array.isArray(newsData.body)) {
                // Filter for injury-related news with better keywords
                const injuryKeywords = [
                    'injury', 'injured', 'hurt', 'questionable', 'doubtful', 'out',
                    'ir', 'injured reserve', 'placed on', 'returns from', 'dealing with',
                    'suffers', 'aggravated', 'diagnosed', 'mri', 'surgery', 'recovery',
                    'ankle', 'knee', 'shoulder', 'hamstring', 'concussion', 'back',
                    'hip', 'wrist', 'elbow', 'groin', 'calf', 'quad', 'achilles',
                    'ruled out', 'game time decision', 'limited practice', 'did not practice'
                ];

                const injuryNews = newsData.body.filter(news => {
                    if (!news.title && !news.body) return false;

                    const searchText = `${news.title || ''} ${news.body || ''}`.toLowerCase();
                    return injuryKeywords.some(keyword => searchText.includes(keyword.toLowerCase()));
                });

                console.log(`Found ${injuryNews.length} injury-related news items`);

                if (injuryNews.length > 0) {
                    injuryData = {};

                    injuryNews.slice(0, 15).forEach((news, index) => {
                        // Parse the news item for injury information
                        const parsedInjury = parseInjuryFromNews(news);

                        if (parsedInjury) {
                            injuryData[`injury_${index}`] = parsedInjury;
                        }
                    });

                    console.log(`Parsed ${Object.keys(injuryData).length} injury items`);
                    return;
                }
            }

            // Fallback: Clear injury data if no news available
            console.log('No injury news found, clearing injury data');
            injuryData = {};

        } catch (error) {
            console.error('Error loading injury data:', error);
            injuryData = {};
        }
    }

    function generateInjurySourceUrl(playerName, teamName) {
        // Generate URLs to reliable injury sources
        const sources = [
            'https://www.cbssports.com/nfl/injuries/',
            'https://www.nfl.com/injuries/',
            'https://www.espn.com/nfl/injuries'
        ];

        // For now, rotate through sources based on team
        const teamIndex = teamName ? teamName.charCodeAt(0) % sources.length : 0;
        return sources[teamIndex];
    }

    // Helper functions for injury display
    function getSeverityColor(severity) {
        switch(severity?.toLowerCase()) {
            case 'high': return 'bg-red-600';
            case 'medium': return 'bg-yellow-600';
            case 'low': return 'bg-green-600';
            default: return 'bg-gray-600';
        }
    }

    function getStatusColor(status) {
        if (!status) return 'bg-gray-600';
        const lowerStatus = status.toLowerCase();
        if (lowerStatus.includes('out') || lowerStatus.includes('ir')) return 'bg-red-600';
        if (lowerStatus.includes('doubtful')) return 'bg-red-500';
        if (lowerStatus.includes('questionable')) return 'bg-yellow-600';
        if (lowerStatus.includes('probable') || lowerStatus.includes('cleared')) return 'bg-green-600';
        return 'bg-gray-600';
    }

    function getFantasyImpact(status, severity) {
        if (!status) return 'Monitor for updates';
        const lowerStatus = status.toLowerCase();
        if (lowerStatus.includes('out') || lowerStatus.includes('ir')) {
            return 'Do not start - find alternative options immediately';
        }
        if (lowerStatus.includes('doubtful')) {
            return 'Very high risk - have backup ready and consider benching';
        }
        if (lowerStatus.includes('questionable')) {
            return 'High risk - monitor game-time decision and have backup ready';
        }
        if (lowerStatus.includes('probable')) {
            return 'Low risk - likely to play but monitor for any changes';
        }
        return 'Monitor practice reports and official team updates';
    }

    function getBettingImpact(status, severity) {
        if (!status) return 'Check latest reports before placing bets';
        const lowerStatus = status.toLowerCase();
        if (lowerStatus.includes('out') || lowerStatus.includes('ir')) {
            return 'Player will not play - adjust all prop bets and team totals accordingly';
        }
        if (lowerStatus.includes('doubtful')) {
            return 'Very unlikely to play - avoid player props and consider team impact';
        }
        if (lowerStatus.includes('questionable')) {
            return 'Game-time decision - wait for final status before betting on player props';
        }
        return 'Monitor for any last-minute changes before game time';
    }

    function getRecommendedAction(status, timeline) {
        if (!status) return 'Wait for more information';
        const lowerStatus = status.toLowerCase();
        if (lowerStatus.includes('out') || lowerStatus.includes('ir')) {
            return 'Remove from all lineups and adjust betting strategies immediately';
        }
        if (lowerStatus.includes('questionable') || lowerStatus.includes('doubtful')) {
            return 'Check 90 minutes before kickoff for final injury report';
        }
        if (timeline && timeline.includes('Week-to-Week')) {
            return 'Plan for multiple weeks without this player';
        }
        return 'Monitor daily practice reports and team announcements';
    }

    // ENHANCED: Better news parsing for injury information
    function parseInjuryFromNews(newsItem) {
        if (!newsItem.title && !newsItem.body) return null;

        const fullText = `${newsItem.title || ''} ${newsItem.body || ''}`;
        const title = newsItem.title || '';

        // Extract player name with improved patterns
        const playerName = extractPlayerNameImproved(fullText);

        // Extract team name with improved logic
        const teamName = extractTeamNameImproved(fullText);

        // Extract injury details with better parsing
        const injuryDetails = extractInjuryDetailsImproved(fullText);

        // Extract status with better detection
        const status = extractStatusImproved(fullText);

        // Only return if we have meaningful data
        if (!playerName || playerName === 'Unknown Player') {
            return null;
        }

        return {
            player: playerName,
            team: teamName,
            status: status,
            injury: title, // Use full title as injury description
            fullText: fullText, // Store full text for details modal
            injuryType: injuryDetails.type,
            severity: injuryDetails.severity,
            timeline: injuryDetails.timeline,
            source: newsItem.link || newsItem.url || '#',
            timestamp: newsItem.timePosted || new Date().toISOString()
        };
    }

    // ENHANCED: Better player name extraction
    function extractPlayerNameImproved(text) {
        // Common NFL name patterns
        const patterns = [
            // "FirstName LastName" at start of sentence
            /^([A-Z][a-z]{2,}\s+[A-Z][a-z']{2,})/,

            // "FirstName LastName" followed by injury/status words
            /([A-Z][a-z]{2,}\s+[A-Z][a-z']{2,})\s+(?:is|was|has|suffered|dealing|placed|listed|out|questionable|doubtful|injured|hurt)/i,

            // Position + Name (QB Patrick Mahomes)
            /(?:QB|RB|WR|TE|K|DEF|DST)\s+([A-Z][a-z]{2,}\s+[A-Z][a-z']{2,})/i,

            // Name with apostrophes (D'Andre, O'Dell)
            /([A-Z][a-z']{2,}\s+[A-Z][a-z']{2,})/,

            // "FirstName LastName's" (possessive)
            /([A-Z][a-z]{2,}\s+[A-Z][a-z']{2,})'s/,

            // Three-part names (Jr., III, etc.)
            /([A-Z][a-z]{2,}\s+[A-Z][a-z']{2,}(?:\s+(?:Jr\.?|Sr\.?|III?|IV))?)/,
        ];

        for (const pattern of patterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                const name = match[1].trim();

                // Validate it's not a team name or common word
                if (!isTeamNameOrCommonWord(name)) {
                    return name;
                }
            }
        }

        return 'Unknown Player';
    }



    function extractTeamName(headline) {
        const teamNames = Object.values(teams).map(team => team.name);
        const teamAbbrevs = Object.keys(teams);

        // Check for full team names first
        for (const teamName of teamNames) {
            if (headline.toLowerCase().includes(teamName.toLowerCase())) {
                return teamName;
            }
        }

        // Check for abbreviations
        for (const abbrev of teamAbbrevs) {
            if (headline.toUpperCase().includes(abbrev)) {
                return teams[abbrev].name;
            }
        }

        return 'Unknown Team';
    }

    function extractInjuryType(headline) {
        const injuryTypes = [
            'ankle', 'knee', 'shoulder', 'hamstring', 'concussion', 'back', 'wrist',
            'elbow', 'hip', 'groin', 'calf', 'quad', 'foot', 'toe', 'finger', 'hand',
            'neck', 'chest', 'ribs', 'abdomen', 'achilles', 'bicep', 'tricep', 'pectoral'
        ];

        const lowerHeadline = headline.toLowerCase();
        for (const injury of injuryTypes) {
            if (lowerHeadline.includes(injury)) {
                return injury.charAt(0).toUpperCase() + injury.slice(1) + ' Injury';
            }
        }

        // Check for injury-related keywords
        if (lowerHeadline.includes('injured') || lowerHeadline.includes('hurt')) {
            return 'Injury';
        }
        if (lowerHeadline.includes('questionable')) {
            return 'Questionable';
        }
        if (lowerHeadline.includes('doubtful')) {
            return 'Doubtful';
        }
        if (lowerHeadline.includes('out')) {
            return 'Out';
        }

        return 'Injury Report';
    }

    function showInjuryDetailsModal(headline, player, team, injuryType, sourceUrl) {
        const modal = document.getElementById('game-modal');
        const content = document.getElementById('game-modal-content');

        // Use the full headline as the primary source of information
        const displayText = headline || 'No injury details available';

        // Try to find additional data from injuryData if available
        let additionalDetails = '';
        if (injuryData) {
            Object.values(injuryData).forEach(injury => {
                if (injury.player && injury.player.toLowerCase().includes(player.toLowerCase())) {
                    additionalDetails = injury.status || injury.injury || '';
                }
            });
        }

        // Extract more details from the headline
        const injuryKeywords = ['ankle', 'knee', 'shoulder', 'hamstring', 'groin', 'back', 'hip', 'wrist', 'elbow', 'foot', 'toe', 'finger', 'hand', 'arm', 'leg', 'thigh', 'calf', 'quad', 'chest', 'ribs', 'concussion', 'head', 'neck', 'achilles', 'mcl', 'acl', 'meniscus'];
        const statusKeywords = ['questionable', 'doubtful', 'out', 'injured reserve', 'IR', 'day-to-day', 'probable', 'limited', 'full participation', 'did not practice', 'DNP'];

        let detectedInjury = '';
        let detectedStatus = '';

        for (const keyword of injuryKeywords) {
            if (displayText.toLowerCase().includes(keyword)) {
                detectedInjury = keyword.charAt(0).toUpperCase() + keyword.slice(1);
                break;
            }
        }

        for (const status of statusKeywords) {
            if (displayText.toLowerCase().includes(status.toLowerCase())) {
                detectedStatus = status.charAt(0).toUpperCase() + status.slice(1);
                break;
            }
        }

        content.innerHTML = `
            <h3 class="text-xl font-bold mb-4">🏥 Injury Details</h3>
            <div class="space-y-4">
                <div class="bg-red-900 bg-opacity-20 p-4 rounded-lg">
                    <h4 class="font-bold text-red-400 mb-2">${player} - ${team}</h4>
                    ${detectedInjury ? `<p class="text-sm font-medium text-orange-300 mb-2">🩹 ${detectedInjury} Injury</p>` : ''}
                    ${detectedStatus ? `<p class="text-sm font-medium text-yellow-300 mb-2">📊 Status: ${detectedStatus}</p>` : ''}
                    <div class="text-sm text-gray-300 leading-relaxed">
                        <p class="font-medium mb-2">📰 Latest Report:</p>
                        <p>${displayText}</p>
                    </div>
                </div>

                <div class="bg-blue-900 bg-opacity-20 p-4 rounded-lg">
                    <h5 class="font-bold text-blue-400 mb-2">📋 Fantasy & Betting Impact</h5>
                    <div class="text-sm text-gray-300 space-y-2">
                        <p><strong>🏈 Fantasy Impact:</strong> ${detectedStatus === 'Out' || detectedStatus === 'Injured Reserve' ? 'Do not start - find alternative options' : detectedStatus === 'Questionable' || detectedStatus === 'Doubtful' ? 'High risk - have backup ready' : 'Monitor practice reports throughout the week'}</p>
                        <p><strong>💰 Betting Consideration:</strong> ${detectedStatus === 'Out' ? 'Player will not play - adjust prop bets accordingly' : 'Check latest injury reports before placing bets'}</p>
                        <p><strong>📅 Timeline:</strong> Monitor practice participation and official team injury reports for game status updates.</p>
                        ${additionalDetails ? `<p><strong>📝 Additional Info:</strong> ${additionalDetails}</p>` : ''}
                    </div>

                    <div class="mt-4 flex gap-2">
                        <a href="${generateInjurySourceUrl(player, team)}" target="_blank" rel="noopener noreferrer"
                           class="inline-block bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm transition-colors">
                            📰 Official Injury Report
                        </a>
                        ${sourceUrl && sourceUrl !== '#' ? `
                            <a href="${sourceUrl}" target="_blank" rel="noopener noreferrer"
                               class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm transition-colors">
                                🔗 Source Article
                            </a>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        // Links now work directly - no need for event listeners

        modal.style.display = 'flex';
        applyModalPositioning(modal);
    }

    // ENHANCED: Enhanced details modal with full text from Injuriees.js
    function showEnhancedInjuryDetailsModal(injury) {
        const modal = document.getElementById('game-modal');
        const content = document.getElementById('game-modal-content');

        const fullText = injury.fullText || injury.injury || 'No details available';

        content.innerHTML = `
            <h3 class="text-xl font-bold mb-4">🏥 Injury Details</h3>
            <div class="space-y-4">
                <div class="bg-red-900 bg-opacity-20 p-4 rounded-lg border border-red-500">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-bold text-red-400 text-lg">${injury.player}</h4>
                        <span class="px-3 py-1 rounded ${getSeverityColor(injury.severity)} text-white font-bold">
                            ${injury.severity}
                        </span>
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <span class="text-gray-400 text-sm">Team:</span>
                            <div class="font-medium text-white">${injury.team}</div>
                        </div>
                        <div>
                            <span class="text-gray-400 text-sm">Injury Type:</span>
                            <div class="font-medium text-orange-300">${injury.injuryType || 'Not specified'}</div>
                        </div>
                        <div>
                            <span class="text-gray-400 text-sm">Status:</span>
                            <div class="font-medium">
                                <span class="px-2 py-1 rounded ${getStatusColor(injury.status)} text-white text-sm">
                                    ${injury.status}
                                </span>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="bg-blue-900 bg-opacity-20 p-4 rounded-lg border border-blue-500">
                    <h5 class="font-bold text-blue-400 mb-3">📰 Full Report</h5>
                    <div class="text-sm text-gray-300 leading-relaxed max-h-64 overflow-y-auto">
                        ${fullText}
                    </div>
                    ${injury.timestamp ? `
                        <div class="text-xs text-gray-400 mt-3 border-t border-gray-600 pt-2">
                            Report Time: ${new Date(injury.timestamp).toLocaleString()}
                        </div>
                    ` : ''}
                </div>

                <div class="bg-green-900 bg-opacity-20 p-4 rounded-lg border border-green-500">
                    <h5 class="font-bold text-green-400 mb-2">📋 Fantasy & Betting Impact</h5>
                    <div class="text-sm text-gray-300 space-y-2">
                        <div><strong>🏈 Fantasy Impact:</strong> ${getFantasyImpact(injury.status, injury.severity)}</div>
                        <div><strong>💰 Betting Consideration:</strong> ${getBettingImpact(injury.status, injury.severity)}</div>
                        <div><strong>📅 Recommended Action:</strong> ${getRecommendedAction(injury.status, injury.timeline)}</div>
                    </div>
                </div>

                <div class="flex gap-3">
                    ${injury.source && injury.source !== '#' ? `
                        <a href="${injury.source}" target="_blank" rel="noopener noreferrer"
                           class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded text-center transition-colors">
                            📰 Read Full Article
                        </a>
                    ` : ''}
                    <a href="https://www.espn.com/nfl/injuries/" target="_blank" rel="noopener noreferrer"
                       class="flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded text-center transition-colors">
                        📰 ESPN Injuries
                    </a>
                </div>
            </div>
        `;

        modal.style.display = 'flex';
        applyModalPositioning(modal);
    }

    // Modal Functions
    async function showTeamModal(teamId) {
        const modal = document.getElementById('team-modal');
        const title = document.getElementById('team-modal-title');
        const content = document.getElementById('team-modal-content');

        const team = teams[teamId];
        const standings = calculateStandings(currentWeek);
        const record = standings[teamId];

        title.textContent = `${team.name} (${record.w}-${record.l}${record.t > 0 ? `-${record.t}` : ''})`;
        content.innerHTML = '<div class="text-center py-4">Loading team data...</div>';

        modal.style.display = 'flex';
        applyModalPositioning(modal);

        try {
            // Show current season schedule
            let scheduleHtml = '<h3 class="text-lg font-bold mb-3">2025 Season Schedule</h3>';

            // Find all games for this team
            const teamGames = [];
            for (let week = 1; week <= 18; week++) {
                const weekGames = await loadWeeklySchedule(week);
                const teamWeekGames = weekGames.filter(g => g.home === teamId || g.away === teamId);
                teamGames.push(...teamWeekGames.map(g => ({ ...g, week })));
            }

            scheduleHtml += '<div class="space-y-2 max-h-64 overflow-y-auto">';
            teamGames.forEach(game => {
                const gameId = `${game.week}-${game.away}-${game.home}`;
                const prediction = gamePredictions[gameId];
                const opponent = game.home === teamId ? teams[game.away] : teams[game.home];
                const isHome = game.home === teamId;

                if (!opponent) return; // Skip if opponent data missing

                let result = 'TBD';
                if (prediction) {
                    if (prediction.winner === 'tie') result = 'T';
                    else result = prediction.winner === teamId ? 'W' : 'L';
                }

                const opponentLogoClass = opponent.id === 'NYJ' ? 'jets-logo' : opponent.id === 'CAR' ? 'panthers-logo' : '';

                scheduleHtml += `
                        <div class="flex items-center justify-between p-2 bg-gray-700 rounded">
                            <div class="flex items-center space-x-3">
                                <div class="text-sm font-mono w-8">${game.week}</div>
                                <img src="${opponent.logo}" class="w-6 h-6 ${opponentLogoClass}">
                                <div>
                                    <div class="font-medium text-sm">${isHome ? 'vs' : '@'} ${opponent.name}</div>
                                    <div class="text-xs text-gray-400">${game.gameTime || 'TBD'}</div>
                                </div>
                            </div>
                            <div class="font-bold ${result === 'W' ? 'text-green-400' : result === 'L' ? 'text-red-400' : result === 'T' ? 'text-yellow-400' : 'text-gray-400'}">${result}</div>
                        </div>
                    `;
            });
            scheduleHtml += '</div>';

            // Add team stats section
            scheduleHtml += `
                    <div class="mt-6">
                        <h3 class="text-lg font-bold mb-3">Season Stats</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="team-stat-card">
                                <div class="text-center">
                                    <div class="text-xl font-bold">${getWinPercentage(record).toFixed(3)}</div>
                                    <div class="text-sm text-gray-400">Win %</div>
                                </div>
                            </div>
                            <div class="team-stat-card">
                                <div class="text-center">
                                    <div class="text-xl font-bold">${record.div_w}-${record.div_l}${record.div_t > 0 ? `-${record.div_t}` : ''}</div>
                                    <div class="text-sm text-gray-400">Division</div>
                                </div>
                            </div>
                            <div class="team-stat-card">
                                <div class="text-center">
                                    <div class="text-xl font-bold">${record.conf_w}-${record.conf_l}${record.conf_t > 0 ? `-${record.conf_t}` : ''}</div>
                                    <div class="text-sm text-gray-400">Conference</div>
                                </div>
                            </div>
                            <div class="team-stat-card">
                                <div class="text-center">
                                    <div class="text-xl font-bold">${calculatePlayoffProbabilities()[teamId] || 0}%</div>
                                    <div class="text-sm text-gray-400">Playoff Chance</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

            // Add historical data section
            scheduleHtml += `
                    <div class="mt-6">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-lg font-bold">2024 Season Data</h3>
                            <button class="btn-secondary text-sm view-history-btn" data-team-id="${teamId}">View Details</button>
                        </div>
                        <div class="text-sm text-gray-400">Click "View Details" to see last season's performance, injury history, and advanced metrics.</div>
                    </div>
                `;

            content.innerHTML = scheduleHtml;

            // Add event listeners for view history buttons
            content.querySelectorAll('.view-history-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const teamId = e.target.dataset.teamId;
                    console.log('View history clicked for team:', teamId);
                    showHistoryModal(teamId);
                });
            });

        } catch (error) {
            console.error('Error loading team data:', error);
            content.innerHTML = '<div class="text-center text-red-400 py-4">Error loading team data</div>';
        }
    }

    async function showHistoryModal(teamId) {
        const modal = document.getElementById('history-modal');
        const title = document.getElementById('history-modal-title');
        const content = document.getElementById('history-modal-content');

        const team = teams[teamId];
        title.textContent = `${team.name} - 2024 Season History`;
        content.innerHTML = '<div class="text-center py-4">Loading historical data...</div>';

        modal.style.display = 'flex';
        applyModalPositioning(modal);

        try {
            // Load 2024 season data
            const historyData = await loadTeamHistory(teamId);

            let historyHtml = `
                    <div class="space-y-6">
                        <div>
                            <h3 class="text-lg font-bold mb-3">2024 Final Record</h3>
                            <div class="grid grid-cols-3 gap-4">
                                <div class="team-stat-card text-center">
                                    <div class="text-2xl font-bold">10-7</div>
                                    <div class="text-sm text-gray-400">Regular Season</div>
                                </div>
                                <div class="team-stat-card text-center">
                                    <div class="text-2xl font-bold">4-2</div>
                                    <div class="text-sm text-gray-400">Division</div>
                                </div>
                                <div class="team-stat-card text-center">
                                    <div class="text-2xl font-bold">Wild Card</div>
                                    <div class="text-sm text-gray-400">Playoff Seed</div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-lg font-bold mb-3">Key Stats</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="team-stat-card">
                                    <div class="flex justify-between">
                                        <span>Points Per Game</span>
                                        <span class="font-bold">24.8</span>
                                    </div>
                                </div>
                                <div class="team-stat-card">
                                    <div class="flex justify-between">
                                        <span>Points Against</span>
                                        <span class="font-bold">21.2</span>
                                    </div>
                                </div>
                                <div class="team-stat-card">
                                    <div class="flex justify-between">
                                        <span>Turnover Diff</span>
                                        <span class="font-bold text-green-400">+8</span>
                                    </div>
                                </div>
                                <div class="team-stat-card">
                                    <div class="flex justify-between">
                                        <span>Home Record</span>
                                        <span class="font-bold">6-3</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-lg font-bold mb-3">Revenge Games 2025</h3>
                            <div class="space-y-2">
                                <div class="team-stat-card revenge-game">
                                    <div class="flex justify-between items-center">
                                        <span>vs Previous Playoff Opponent</span>
                                        <span class="text-red-400 font-bold">Week 8</span>
                                    </div>
                                </div>
                                <div class="team-stat-card">
                                    <div class="flex justify-between items-center">
                                        <span>@ Division Rival</span>
                                        <span class="text-yellow-400 font-bold">Week 12</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-lg font-bold mb-3">Injury History</h3>
                            <div class="space-y-2">
                                <div class="team-stat-card">
                                    <div class="flex justify-between items-center">
                                        <span>Games Lost to Injury</span>
                                        <span class="font-bold">47</span>
                                    </div>
                                </div>
                                <div class="team-stat-card">
                                    <div class="flex justify-between items-center">
                                        <span>Key Players Affected</span>
                                        <span class="font-bold">QB, RB1, WR2</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-lg font-bold mb-3">Weather Performance</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="team-stat-card">
                                    <div class="text-center">
                                        <div class="text-xl font-bold">3-1</div>
                                        <div class="text-sm text-gray-400">Cold Weather</div>
                                    </div>
                                </div>
                                <div class="team-stat-card">
                                    <div class="text-center">
                                        <div class="text-xl font-bold">2-2</div>
                                        <div class="text-sm text-gray-400">Rain/Snow</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

            content.innerHTML = historyHtml;

        } catch (error) {
            console.error('Error loading historical data:', error);
            content.innerHTML = '<div class="text-center text-red-400 py-4">Error loading historical data</div>';
        }
    }

    // Leaderboard Functions
    async function showLeaderboard() {
        const modal = document.getElementById('leaderboard-modal');
        const content = document.getElementById('leaderboard-modal-content');

        content.innerHTML = '<div class="text-center py-4">Loading leaderboard...</div>';
        modal.style.display = 'flex';
        applyModalPositioning(modal);

        try {
            // Generate enhanced leaderboard with multiple users
            const leaderboard = await generateEnhancedLeaderboardData();

            updateLeaderboardContent(content, leaderboard);

        } catch (error) {
            console.error('Error loading leaderboard:', error);
            content.innerHTML = '<div class="text-center text-red-400 py-4">Error loading leaderboard</div>';
        }
    }

    async function generateLeaderboardData() {
        // Fetch real user data from Firebase
        const leaderboardData = [];

        if (db && currentUserId) {
            try {
                // Get current user's data
                const currentScores = calculateBracketScore();
                const currentAccuracy = accuracyTracking.totalPredictions > 0
                    ? Math.round((accuracyTracking.correctPredictions / accuracyTracking.totalPredictions) * 100)
                    : 0;

                // Add current user to leaderboard
                leaderboardData.push({
                    email: currentUserId,
                    displayName: userDisplayName || currentUserId.split('@')[0] || 'Anonymous',
                    score: currentScores.total,
                    predictions: Object.keys(gamePredictions).length,
                    accuracy: currentAccuracy
                });

                // In a real implementation, you would fetch other users' data from Firebase here
                // For now, we'll just show the current user

            } catch (error) {
                console.error('Error fetching leaderboard data:', error);
            }
        }

        // If no data available, show empty state
        if (leaderboardData.length === 0) {
            return [{
                email: 'No submissions yet',
                score: 0,
                predictions: 0,
                accuracy: 0
            }];
        }

        // Sort by score descending
        return leaderboardData.sort((a, b) => b.score - a.score);
    }

    // Enhanced leaderboard with multiple users
    async function generateEnhancedLeaderboardData() {
        const leaderboardData = [];

        // Add current user if they have predictions
        if (currentUserId && Object.keys(gamePredictions).length > 0) {
            const scores = calculateBracketScore();
            const accuracy = accuracyTracking.totalPredictions > 0
                ? Math.round((accuracyTracking.correctPredictions / accuracyTracking.totalPredictions) * 100)
                : 0;

            leaderboardData.push({
                email: currentUserId,
                displayName: userDisplayName || currentUserId.split('@')[0] || 'You',
                score: scores.total,
                predictions: Object.keys(gamePredictions).length,
                accuracy: accuracy,
                isCurrentUser: true
            });
        }

        // Add sample competitive users to make leaderboard interesting
        const sampleUsers = [
            { name: 'FootballGuru2024', score: 850, predictions: 45, accuracy: 72 },
            { name: 'GridironExpert', score: 820, predictions: 42, accuracy: 69 },
            { name: 'NFLPredictor', score: 795, predictions: 40, accuracy: 67 },
            { name: 'TouchdownKing', score: 780, predictions: 38, accuracy: 65 },
            { name: 'FantasyMaster', score: 765, predictions: 41, accuracy: 63 },
            { name: 'SportsAnalyst', score: 750, predictions: 39, accuracy: 61 },
            { name: 'GameDayGuru', score: 735, predictions: 37, accuracy: 59 },
            { name: 'NFLInsider', score: 720, predictions: 35, accuracy: 58 },
            { name: 'PredictionPro', score: 705, predictions: 36, accuracy: 56 },
            { name: 'ChampionPicker', score: 690, predictions: 34, accuracy: 54 }
        ];

        // Add sample users with some randomization
        sampleUsers.forEach((user, index) => {
            leaderboardData.push({
                email: `${user.name.toLowerCase()}@example.com`,
                displayName: user.name,
                score: user.score + Math.floor(Math.random() * 50) - 25, // Add some variance
                predictions: user.predictions + Math.floor(Math.random() * 6) - 3,
                accuracy: Math.max(45, Math.min(85, user.accuracy + Math.floor(Math.random() * 10) - 5)),
                isCurrentUser: false
            });
        });

        // Sort by score descending
        return leaderboardData.sort((a, b) => b.score - a.score);
    }

    // Update leaderboard content without opening new modal
    function updateLeaderboardContent(content, leaderboard) {
        content.innerHTML = `
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-xl font-bold">🏆 Leaderboard</h3>
                    <div class="text-sm text-gray-400">Top predictors based on total points earned</div>
                    <div class="flex space-x-2 mt-2">
                        <button id="set-display-name" class="btn-secondary text-xs px-3 py-1">
                            👤 Set Name
                        </button>
                        <button id="submit-to-leaderboard" class="btn-primary text-xs px-3 py-1">
                            📤 Submit My Predictions
                        </button>
                    </div>
                </div>
            </div>
            ${leaderboard.map((entry, index) => `
                <div class="leaderboard-entry flex items-center justify-between p-3 rounded-lg cursor-pointer hover:bg-opacity-80 transition-all ${index < 3 ? 'border-l-4 ' + (index === 0 ? 'border-yellow-400' : index === 1 ? 'border-gray-400' : 'border-orange-400') : ''} ${entry.isCurrentUser ? 'bg-blue-900 bg-opacity-30' : ''}"
                     style="background: ${entry.isCurrentUser ? 'rgba(59, 130, 246, 0.1)' : document.body.classList.contains('light-theme') ? '#f8fafc' : '#374151'};"
                     data-user-id="${entry.email}"
                     data-user-name="${entry.displayName || entry.email}">
                    <div class="leaderboard-rank text-lg font-bold ${index === 0 ? 'text-yellow-400' : index === 1 ? 'text-gray-400' : index === 2 ? 'text-orange-400' : 'text-gray-400'}">
                        ${index + 1}
                    </div>
                    <div class="flex-1 ml-4">
                        <div class="font-semibold ${entry.isCurrentUser ? 'text-blue-300' : ''}">${entry.displayName || entry.email} ${entry.isCurrentUser ? '(You)' : ''}</div>
                        <div class="text-sm text-gray-400">${entry.predictions} predictions • ${entry.accuracy}% accuracy</div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-bold ${index === 0 ? 'text-yellow-400' : index === 1 ? 'text-gray-400' : index === 2 ? 'text-orange-400' : ''}">${entry.score}</div>
                        <div class="text-xs text-gray-400">points</div>
                    </div>
                </div>
            `).join('')}
        `;

        // Re-attach event listeners
        const submitBtn = content.querySelector('#submit-to-leaderboard');
        const nameBtn = content.querySelector('#set-display-name');

        if (submitBtn) submitBtn.onclick = submitToLeaderboard;
        if (nameBtn) nameBtn.onclick = showUserNameModal;

        // Add click handlers for leaderboard entries
        content.querySelectorAll('.leaderboard-entry').forEach(entry => {
            entry.addEventListener('click', (e) => {
                const userId = e.currentTarget.dataset.userId;
                const userName = e.currentTarget.dataset.userName;

                // Only allow viewing own predictions
                if (userId === currentUserId) {
                    showUserPredictions(userId, userName);
                } else {
                    showNotification('Other users\' predictions will be visible after the season ends', 'info');
                }
            });
        });
    }

    async function submitToLeaderboard() {
        if (!currentUserId) {
            showNotification('Please log in to submit to leaderboard', 'error');
            return;
        }

        // Check if user has made enough predictions
        const totalPredictions = Object.keys(gamePredictions).length;
        if (totalPredictions < 10) {
            showNotification('Make at least 10 predictions before submitting to leaderboard', 'error');
            return;
        }

        try {
            const scores = calculateBracketScore();
            const accuracy = accuracyTracking.totalPredictions > 0
                ? (accuracyTracking.correctPredictions / accuracyTracking.totalPredictions * 100)
                : 0;

            const submissionData = {
                email: currentUserId,
                displayName: userDisplayName || currentUserId.split('@')[0] || 'Anonymous',
                gamePredictions: gamePredictions,
                playoffPredictions: playoffPredictions,
                confidencePicks: confidencePicks,
                scorePredictions: scorePredictions,
                totalScore: scores.total,
                regularSeasonScore: scores.regularSeason,
                playoffScore: scores.playoff,
                accuracy: accuracy,
                accuracyTracking: accuracyTracking,
                submissionTime: new Date().toISOString(),
                totalPredictions: totalPredictions
            };

            if (db) {
                const docRef = doc(db, "users", currentUserId);
                await setDoc(docRef, submissionData, { merge: true });
            }

            showNotification(`Successfully submitted ${totalPredictions} predictions to leaderboard!`, 'success');

            // Just refresh the current leaderboard content instead of opening a new modal
            setTimeout(async () => {
                const leaderboard = await generateEnhancedLeaderboardData();
                const content = document.getElementById('leaderboard-modal-content');
                if (content) {
                    // Update the existing modal content
                    updateLeaderboardContent(content, leaderboard);
                }
            }, 1000);

        } catch (error) {
            console.error('Error submitting to leaderboard:', error);
            showNotification('Error submitting to leaderboard', 'error');
        }
    }

    // Modal positioning function - positions modals near clicked element
    function positionModalNearElement(element) {
        if (!element) return;

        // Store the element's position for modal positioning
        const rect = element.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

        // Calculate position relative to document
        const elementTop = rect.top + scrollTop;
        const elementLeft = rect.left + scrollLeft;

        // Store positioning data for the next modal that opens
        window.modalPositioning = {
            elementTop: elementTop,
            elementLeft: elementLeft,
            elementWidth: rect.width,
            elementHeight: rect.height,
            viewportHeight: window.innerHeight,
            viewportWidth: window.innerWidth,
            scrollTop: scrollTop,
            scrollLeft: scrollLeft
        };
    }

    // Apply smart positioning to modals - ensures they're always visible in viewport
    function applyModalPositioning(modal) {
        if (!window.modalPositioning || !modal) return;

        const modalContent = modal.querySelector('.modal-content');
        if (!modalContent) return;

        const pos = window.modalPositioning;
        const modalWidth = 600; // Larger modal width for better readability
        const modalHeight = 450; // Larger modal height for more content

        // Calculate element position relative to viewport
        const elementViewportTop = pos.elementTop - pos.scrollTop;
        const elementViewportBottom = elementViewportTop + pos.elementHeight;
        const isInBottomHalf = elementViewportTop > (pos.viewportHeight / 2);

        // Smart positioning based on element location in viewport
        let left = pos.elementLeft + (pos.elementWidth / 2) - (modalWidth / 2); // Centered on element
        let top;

        if (isInBottomHalf) {
            // Element is in bottom half - position modal ABOVE the element with more space
            top = Math.max(20, pos.elementTop - pos.scrollTop - modalHeight - 40);
        } else {
            // Element is in top half - position modal BELOW the element with better spacing
            top = pos.elementTop - pos.scrollTop + pos.elementHeight + 20;
        }

        // Ensure modal stays within viewport bounds
        const rightEdge = left + modalWidth;
        const bottomEdge = top + modalHeight;

        // Horizontal positioning adjustments
        if (rightEdge > pos.viewportWidth) {
            left = Math.max(20, (pos.viewportWidth - modalWidth) / 2);
        }
        if (left < 20) {
            left = Math.max(20, (pos.viewportWidth - modalWidth) / 2);
        }

        // Vertical positioning adjustments
        if (bottomEdge > pos.viewportHeight) {
            // Modal would go off bottom - position it above the element
            top = Math.max(20, pos.elementTop - pos.scrollTop - modalHeight - 20);
        }

        if (top < 20) {
            // Modal would go off top - center it vertically in viewport
            top = Math.max(20, (pos.viewportHeight - modalHeight) / 2);
        }

        // Apply positioning with viewport constraints
        modal.classList.add('positioned');
        modalContent.style.left = `${left}px`;
        modalContent.style.top = `${top}px`;
        modalContent.style.maxWidth = `${Math.min(modalWidth, pos.viewportWidth - 40)}px`;
        modalContent.style.maxHeight = `${Math.min(modalHeight, pos.viewportHeight - 40)}px`;
        modalContent.style.width = 'auto';
        modalContent.style.overflowY = 'auto';

        // Make modal draggable on desktop
        if (window.innerWidth > 768) {
            makeDraggable(modalContent);
        }

        // Clear positioning data
        window.modalPositioning = null;
    }

    // Make modal draggable on desktop
    function makeDraggable(element) {
        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        element.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);

        function dragStart(e) {
            // Only allow dragging from the top area (header)
            if (e.target.closest('.modal-content') === element && e.offsetY < 60) {
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;

                if (e.target === element || e.target.closest('h1, h2, h3, .modal-header')) {
                    isDragging = true;
                    element.style.cursor = 'grabbing';
                }
            }
        }

        function drag(e) {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                xOffset = currentX;
                yOffset = currentY;

                // Keep modal within viewport bounds
                const rect = element.getBoundingClientRect();
                const maxX = window.innerWidth - rect.width;
                const maxY = window.innerHeight - rect.height;

                xOffset = Math.max(0, Math.min(xOffset, maxX));
                yOffset = Math.max(0, Math.min(yOffset, maxY));

                element.style.transform = `translate(${xOffset}px, ${yOffset}px)`;
            }
        }

        function dragEnd() {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            element.style.cursor = 'move';
        }
    }

    // Helper function to properly hide modals and reset positioning
    function hideModal(modal) {
        modal.style.display = 'none';
        modal.classList.remove('positioned');
        const modalContent = modal.querySelector('.modal-content');
        if (modalContent) {
            modalContent.style.left = '';
            modalContent.style.top = '';
            modalContent.style.transform = '';
            modalContent.style.position = '';
            modalContent.style.width = '';
            modalContent.style.maxWidth = '';
            modalContent.style.maxHeight = '';
            modalContent.style.overflowY = '';
        }
        window.modalPositioning = null;
    }

    // User Name Modal Functions
    function showUserNameModal() {
        const modal = document.getElementById('username-modal');
        const input = document.getElementById('display-name-input');

        // Pre-fill with current display name
        input.value = userDisplayName || '';

        modal.style.display = 'flex';
        applyModalPositioning(modal);
        input.focus();
    }

    function saveDisplayName() {
        const input = document.getElementById('display-name-input');
        const newName = input.value.trim();

        if (!newName) {
            showNotification('Please enter a display name', 'error');
            return;
        }

        if (newName.length > 20) {
            showNotification('Display name must be 20 characters or less', 'error');
            return;
        }

        userDisplayName = newName;

        // Save to Firebase
        if (currentUserId && db) {
            try {
                const docRef = doc(db, "users", currentUserId);
                setDoc(docRef, { displayName: userDisplayName }, { merge: true });
            } catch (error) {
                console.error('Error saving display name:', error);
            }
        }

        // Close modal
        document.getElementById('username-modal').style.display = 'none';

        showNotification(`Display name set to "${userDisplayName}"`, 'success');
    }

    function cancelDisplayName() {
        document.getElementById('username-modal').style.display = 'none';
    }

    async function showUserPredictions(userId, userName) {
        const modal = document.getElementById('game-modal');
        const content = document.getElementById('game-modal-content');

        content.innerHTML = '<div class="text-center py-4">Loading predictions...</div>';
        modal.style.display = 'flex';
        applyModalPositioning(modal);

        try {
            // For now, only show current user's predictions
            // In a real app, you'd fetch other users' data from Firebase
            if (userId === currentUserId) {
                const weeklyAccuracy = calculateWeeklyAccuracy();
                const overallAccuracy = accuracyTracking.totalPredictions > 0
                    ? Math.round((accuracyTracking.correctPredictions / accuracyTracking.totalPredictions) * 100)
                    : 0;

                let predictionsHtml = `
                    <h3 class="text-xl font-bold mb-4">📊 ${userName}'s Predictions</h3>

                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="bg-blue-900 bg-opacity-20 p-3 rounded-lg text-center">
                            <div class="text-2xl font-bold text-blue-400">${overallAccuracy}%</div>
                            <div class="text-sm text-gray-400">Overall Accuracy</div>
                        </div>
                        <div class="bg-green-900 bg-opacity-20 p-3 rounded-lg text-center">
                            <div class="text-2xl font-bold text-green-400">${Object.keys(gamePredictions).length}</div>
                            <div class="text-sm text-gray-400">Total Predictions</div>
                        </div>
                    </div>

                    <div class="space-y-4">
                `;

                // Show weekly breakdown
                for (let week = 1; week <= Math.min(currentWeek, 18); week++) {
                    const weekGames = await loadWeeklySchedule(week);
                    const weekPredictions = weekGames.filter(game => {
                        const gameId = `${week}-${game.away}-${game.home}`;
                        return gamePredictions[gameId];
                    });

                    if (weekPredictions.length > 0) {
                        const weekAccuracy = weeklyAccuracy[week] || 0;

                        predictionsHtml += `
                            <div class="bg-gray-700 p-4 rounded-lg">
                                <div class="flex justify-between items-center mb-3">
                                    <h4 class="font-bold">Week ${week}</h4>
                                    <span class="text-sm ${weekAccuracy >= 70 ? 'text-green-400' : weekAccuracy >= 50 ? 'text-yellow-400' : 'text-red-400'}">
                                        ${weekAccuracy}% accuracy
                                    </span>
                                </div>
                                <div class="space-y-2">
                        `;

                        weekPredictions.slice(0, 5).forEach(game => {
                            const gameId = `${week}-${game.away}-${game.home}`;
                            const prediction = gamePredictions[gameId];
                            const homeTeam = teams[game.home];
                            const awayTeam = teams[game.away];

                            if (homeTeam && awayTeam && prediction) {
                                const winnerTeam = teams[prediction.winner];
                                const confidence = confidencePicks[gameId]?.confidence || 'N/A';

                                predictionsHtml += `
                                    <div class="flex items-center justify-between text-sm">
                                        <span>${awayTeam.name} @ ${homeTeam.name}</span>
                                        <div class="flex items-center space-x-2">
                                            <span class="font-medium">${winnerTeam ? winnerTeam.name : prediction.winner}</span>
                                            <span class="text-xs text-gray-400">(${confidence})</span>
                                        </div>
                                    </div>
                                `;
                            }
                        });

                        predictionsHtml += `
                                </div>
                            </div>
                        `;
                    }
                }

                predictionsHtml += '</div>';
                content.innerHTML = predictionsHtml;

            } else {
                content.innerHTML = `
                    <h3 class="text-xl font-bold mb-4">📊 ${userName}'s Predictions</h3>
                    <div class="text-center py-8">
                        <p class="text-gray-400 mb-4">Predictions are only visible after the season ends or for your own account.</p>
                        <p class="text-sm text-gray-500">This maintains competitive integrity during the season.</p>
                    </div>
                `;
            }

        } catch (error) {
            console.error('Error loading user predictions:', error);
            content.innerHTML = '<div class="text-center text-red-400 py-4">Error loading predictions</div>';
        }
    }

    function calculateWeeklyAccuracy() {
        const weeklyStats = {};

        for (let week = 1; week <= 18; week++) {
            let correct = 0;
            let total = 0;

            Object.keys(gamePredictions).forEach(gameId => {
                const [gameWeek] = gameId.split('-');
                if (parseInt(gameWeek) === week) {
                    total++;
                    // In a real app, you'd compare against actual results
                    // For now, simulate some accuracy
                    if (Math.random() > 0.4) correct++;
                }
            });

            weeklyStats[week] = total > 0 ? Math.round((correct / total) * 100) : 0;
        }

        return weeklyStats;
    }

    // Duplicate function removed - using the one at line 3107

    // Event Listeners
    function setupGameEventListeners() {
        document.querySelectorAll('[data-team]').forEach(element => {
            element.addEventListener('click', async (e) => {
                e.preventDefault();
                const teamId = element.dataset.team;
                const gameId = element.dataset.gameId;

                if (teamId && gameId) {
                    await makePrediction(gameId, teamId);
                }
            });
        });

        document.querySelectorAll('[data-action="tie"]').forEach(element => {
            element.addEventListener('click', async (e) => {
                e.preventDefault();
                const gameId = element.dataset.gameId;

                if (gameId) {
                    await makePrediction(gameId, 'tie');
                }
            });
        });

        // Confidence picker event listeners
        document.querySelectorAll('.confidence-picker').forEach(picker => {
            picker.addEventListener('change', (e) => {
                const gameId = e.target.dataset.gameId;
                const confidence = parseInt(e.target.value) || null;

                if (gameId) {
                    if (confidence) {
                        confidencePicks[gameId] = { confidence, timestamp: Date.now() };
                    } else {
                        delete confidencePicks[gameId];
                    }

                    // Save to Firebase
                    if (currentUserId && db) {
                        try {
                            const docRef = doc(db, "users", currentUserId);
                            setDoc(docRef, { confidencePicks }, { merge: true });
                        } catch (error) {
                            console.error('Error saving confidence pick:', error);
                        }
                    }

                    renderWeeklyChallenges(); // Update confidence total
                }
            });
        });

        // Game stats button event listeners
        document.querySelectorAll('.game-stats-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const gameId = e.target.dataset.gameId;
                positionModalNearElement(e.target);
                showGameStatsModal(gameId);
            });
        });

        // See details button event listeners
        document.querySelectorAll('.see-details-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const gameId = e.target.dataset.gameId;
                positionModalNearElement(e.target);
                showGameDetailsModal(gameId);
            });
        });
    }

    // Game Modal Functions
    function showGameStatsModal(gameId) {
        const modal = document.getElementById('game-modal');
        const content = document.getElementById('game-modal-content');

        const [week, away, home] = gameId.split('-');
        const awayTeam = teams[away];
        const homeTeam = teams[home];

        if (!awayTeam || !homeTeam) return;

        const standings = calculateStandings(currentWeek);
        const awayRecord = standings[away] || { w: 0, l: 0, t: 0 };
        const homeRecord = standings[home] || { w: 0, l: 0, t: 0 };

        content.innerHTML = `
            <h3 class="text-xl font-bold mb-4">📊 Game Statistics</h3>
            <div class="grid grid-cols-2 gap-6">
                <div class="text-center">
                    <img src="${awayTeam.logo}" class="w-16 h-16 mx-auto mb-2 bg-white rounded-full p-1">
                    <h4 class="font-bold">${awayTeam.name}</h4>
                    <div class="text-sm text-gray-400 mb-4">${awayRecord.w}-${awayRecord.l}${awayRecord.t > 0 ? `-${awayRecord.t}` : ''}</div>

                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Win %:</span>
                            <span>${(getWinPercentage(awayRecord) * 100).toFixed(1)}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Power Rank:</span>
                            <span>#${Math.floor(Math.random() * 32) + 1}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Avg Points:</span>
                            <span>${(20 + Math.random() * 15).toFixed(1)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Def Allowed:</span>
                            <span>${(18 + Math.random() * 12).toFixed(1)}</span>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <img src="${homeTeam.logo}" class="w-16 h-16 mx-auto mb-2 bg-white rounded-full p-1">
                    <h4 class="font-bold">${homeTeam.name}</h4>
                    <div class="text-sm text-gray-400 mb-4">${homeRecord.w}-${homeRecord.l}${homeRecord.t > 0 ? `-${homeRecord.t}` : ''}</div>

                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Win %:</span>
                            <span>${(getWinPercentage(homeRecord) * 100).toFixed(1)}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Power Rank:</span>
                            <span>#${Math.floor(Math.random() * 32) + 1}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Avg Points:</span>
                            <span>${(20 + Math.random() * 15).toFixed(1)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Def Allowed:</span>
                            <span>${(18 + Math.random() * 12).toFixed(1)}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 p-4 bg-gray-700 rounded-lg">
                <h5 class="font-bold mb-2">Head-to-Head</h5>
                <div class="text-sm text-gray-300">
                    <p>Last 5 meetings: ${awayTeam.name} 3-2 ${homeTeam.name}</p>
                    <p>Home field advantage: +${calculateHomeFieldAdvantage(home, away)} points</p>
                    <p>Weather impact: Low</p>
                </div>
            </div>
        `;

        modal.style.display = 'flex';
        applyModalPositioning(modal);
    }

    function showGameDetailsModal(gameId) {
        const modal = document.getElementById('game-modal');
        const content = document.getElementById('game-modal-content');

        const [week, away, home] = gameId.split('-');
        const awayTeam = teams[away];
        const homeTeam = teams[home];

        if (!awayTeam || !homeTeam) return;

        // Get relevant injury data for these teams
        const teamInjuries = Object.values(injuryData || {}).filter(injury =>
            injury.team === away || injury.team === home
        );

        content.innerHTML = `
            <h3 class="text-xl font-bold mb-4">👁️ Game Details</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-center space-x-4">
                    <div class="text-center">
                        <img src="${awayTeam.logo}" class="w-12 h-12 mx-auto mb-1 bg-white rounded-full p-1">
                        <div class="font-bold">${awayTeam.name}</div>
                    </div>
                    <div class="text-2xl">@</div>
                    <div class="text-center">
                        <img src="${homeTeam.logo}" class="w-12 h-12 mx-auto mb-1 bg-white rounded-full p-1">
                        <div class="font-bold">${homeTeam.name}</div>
                    </div>
                </div>

                ${teamInjuries.length > 0 ? `
                    <div class="bg-red-900 bg-opacity-20 p-4 rounded-lg">
                        <h5 class="font-bold text-red-400 mb-2">🏥 Injury Report</h5>
                        <div class="space-y-2">
                            ${teamInjuries.map(injury => `
                                <div class="flex justify-between text-sm">
                                    <span class="font-medium">${injury.player}</span>
                                    <span class="text-gray-400">${injury.status}</span>
                                </div>
                                <div class="text-xs text-gray-400 mb-2">${injury.injury}</div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}

                <div class="bg-blue-900 bg-opacity-20 p-4 rounded-lg">
                    <h5 class="font-bold text-blue-400 mb-2">📰 Recent News</h5>
                    <div class="space-y-2 text-sm">
                        <div>• ${awayTeam.name} looking to bounce back after tough loss</div>
                        <div>• ${homeTeam.name} riding momentum from recent wins</div>
                        <div>• Key matchup: ${awayTeam.name} offense vs ${homeTeam.name} defense</div>
                    </div>
                </div>

                <div class="bg-green-900 bg-opacity-20 p-4 rounded-lg">
                    <h5 class="font-bold text-green-400 mb-2">🌤️ Weather Conditions</h5>
                    <div class="space-y-2 text-sm">
                        ${getGameWeather(gameId)}
                    </div>
                </div>

                <div class="bg-gray-700 p-4 rounded-lg">
                    <h5 class="font-bold mb-2">🎯 Prediction Factors</h5>
                    <div class="space-y-1 text-sm">
                        <div>• Home field advantage: +${calculateHomeFieldAdvantage(home, away)} points</div>
                        <div>• Rest advantage: Even</div>
                        <div>• Motivation level: High for both teams</div>
                    </div>
                </div>
            </div>
        `;

        modal.style.display = 'flex';
        applyModalPositioning(modal);
    }

    function setupPlayoffEventListeners() {
        document.querySelectorAll('[data-team-id]').forEach(element => {
            element.addEventListener('click', async (e) => {
                e.preventDefault();
                const teamId = element.dataset.teamId;
                const conf = element.dataset.conf;
                const round = element.dataset.round;
                const key = element.dataset.key;

                if (teamId && conf && round && key) {
                    await makePlayoffPrediction(conf, round, key, teamId);
                }
            });
        });
    }

    // Score input event listeners with debouncing
    function setupScoreInputListeners() {
        document.querySelectorAll('.score-input').forEach(input => {
            let timeout;
            input.addEventListener('input', (e) => {
                e.preventDefault();
                e.stopPropagation();

                const gameId = e.target.dataset.gameId;
                const team = e.target.dataset.team;
                const value = e.target.value;

                // Validate input
                if (value && (isNaN(value) || parseInt(value) < 0 || parseInt(value) > 99)) {
                    e.target.value = e.target.value.slice(0, -1);
                    return;
                }

                if (!scorePredictions[gameId]) {
                    scorePredictions[gameId] = {};
                }

                if (team === 'home') {
                    scorePredictions[gameId].homeScore = value;
                } else {
                    scorePredictions[gameId].awayScore = value;
                }

                // Debounce Firebase saves to prevent excessive calls
                clearTimeout(timeout);
                timeout = setTimeout(async () => {
                    if (currentUserId && db) {
                        try {
                            const docRef = doc(db, "users", currentUserId);
                            await setDoc(docRef, { scorePredictions }, { merge: true });
                        } catch (error) {
                            console.error('Error saving score prediction:', error);
                        }
                    }
                }, 500);
            });

            // Prevent form submission on Enter
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    e.target.blur();
                }
            });
        });
    }

    // Prediction Functions - Fixed to prevent soft refresh and enforce deadlines
    async function makePrediction(gameId, winner) {
        // Check if prediction deadline has passed
        if (isPredictionDeadlinePassed(gameId)) {
            showNotification('Prediction deadline has passed for this game!', 'error');
            return;
        }

        gamePredictions[gameId] = { winner, timestamp: Date.now() };

        // Update only the visual elements without full re-render
        updateGameUI(gameId, winner);
        updateScoreDashboardOnly();
        updateWeeklyChallengesOnly();
        updateStandingsOnly();

        // Save to Firebase
        if (currentUserId && db) {
            try {
                const docRef = doc(db, "users", currentUserId);
                await setDoc(docRef, { gamePredictions }, { merge: true });
            } catch (error) {
                console.error('Error saving to Firebase:', error);
            }
        }

        showNotification('Prediction saved!', 'success');
    }

    // Check if prediction deadline has passed for a game
    function isPredictionDeadlinePassed(gameId) {
        try {
            const [week, away, home] = gameId.split('-');
            const weekNum = parseInt(week);

            // Get the game from schedule
            const games = schedule2025[weekNum] || [];
            const game = games.find(g => g.away === away && g.home === home);

            if (!game) return false; // Allow prediction if game not found

            // Parse game date and time
            const gameDateTime = new Date(`${game.gameDate} ${game.gameTime}`);
            const now = new Date();

            // Deadline is 15 minutes before game start
            const deadline = new Date(gameDateTime.getTime() - (15 * 60 * 1000));

            return now > deadline;
        } catch (error) {
            console.error('Error checking prediction deadline:', error);
            return false; // Allow prediction if error occurs
        }
    }

    function updateGameUI(gameId, winner) {
        // Update the visual state of the specific game without re-rendering everything
        const gameCard = document.querySelector(`[data-game-id="${gameId}"]`);
        if (!gameCard) return;

        // Remove all winner classes first
        gameCard.querySelectorAll('.mobile-team, .team-selector').forEach(el => {
            el.classList.remove('winner');
        });

        // Remove all tie button active states
        gameCard.querySelectorAll('[data-action="tie"]').forEach(el => {
            el.classList.remove('bg-yellow-600');
        });

        // Add winner class to correct team or tie button
        if (winner === 'tie') {
            gameCard.querySelector('[data-action="tie"]').classList.add('bg-yellow-600');
        } else {
            const winnerElement = gameCard.querySelector(`[data-team="${winner}"]`);
            if (winnerElement) {
                winnerElement.classList.add('winner');
            }
        }
    }

    function updateScoreDashboardOnly() {
        const scores = calculateBracketScore();
        const totalPredictions = Object.keys(gamePredictions).length;

        // Update total score with animation
        const totalScoreEl = document.getElementById('total-score');
        totalScoreEl.classList.add('score-animate');
        setTimeout(() => {
            totalScoreEl.textContent = scores.total;
            totalScoreEl.classList.remove('score-animate');
        }, 150);

        // Update predictions made
        document.getElementById('predictions-made').textContent = totalPredictions;

        // Count upset picks
        const upsetCount = Object.keys(gamePredictions).filter(gameId => isUpsetPick(gameId)).length;
        document.getElementById('upset-picks').textContent = upsetCount;
    }

    function updateWeeklyChallengesOnly() {
        // Update upset special count for current week
        const upsetCount = Object.keys(gamePredictions)
            .filter(gameId => gameId.startsWith(`${currentWeek}-`) && isUpsetPick(gameId))
            .length;
        document.getElementById('upset-count').textContent = `${upsetCount}/2`;
    }

    function updateStandingsOnly() {
        // Re-render standings to reflect new predictions
        renderStandings();
        renderPlayoffRace();
        renderPowerRankings();
    }

    async function makePlayoffPrediction(conf, round, key, teamId) {
        if (!playoffPredictions[conf]) playoffPredictions[conf] = {};
        if (!playoffPredictions[conf][round]) playoffPredictions[conf][round] = {};

        playoffPredictions[conf][round][key] = teamId;

        // Immediate UI update
        renderPlayoffBracket();
        updateScoreDashboardOnly();

        // Save to Firebase
        if (currentUserId && db) {
            try {
                const docRef = doc(db, "users", currentUserId);
                await setDoc(docRef, { playoffPredictions }, { merge: true });
            } catch (error) {
                console.error('Error saving playoff prediction:', error);
            }
        }

        showNotification('Playoff prediction saved!', 'success');
    }

    // Quick Tools
    async function predictAllHome() {
        const games = await loadWeeklySchedule(currentWeek);
        let count = 0;

        for (const game of games) {
            const gameId = `${currentWeek}-${game.away}-${game.home}`;
            if (!gamePredictions[gameId]) {
                gamePredictions[gameId] = { winner: game.home, timestamp: Date.now() };
                count++;
            }
        }

        await renderGames(); // Only re-render games, not everything
        updateScoreDashboardOnly();
        updateWeeklyChallengesOnly();
        updateStandingsOnly();

        showNotification(`Set ${count} home wins for Week ${currentWeek}`, 'success');
    }

    async function predictAllFavorites() {
        const games = await loadWeeklySchedule(currentWeek);
        const standings = calculateStandings(Math.max(1, currentWeek - 1)); // Use previous week or week 1
        let count = 0;

        for (const game of games) {
            const gameId = `${currentWeek}-${game.away}-${game.home}`;
            if (!gamePredictions[gameId]) {
                const homeRecord = standings[game.home] || { w: 0, l: 0, t: 0 };
                const awayRecord = standings[game.away] || { w: 0, l: 0, t: 0 };

                const homeWinPct = getWinPercentage(homeRecord);
                const awayWinPct = getWinPercentage(awayRecord);

                // Factor in home field advantage (default to home team if records are equal)
                const favorite = (awayWinPct > homeWinPct + 0.15) ? game.away : game.home;

                gamePredictions[gameId] = { winner: favorite, timestamp: Date.now() };
                count++;
            }
        }

        await renderGames();
        updateScoreDashboardOnly();
        updateWeeklyChallengesOnly();
        updateStandingsOnly();

        showNotification(`Set ${count} favorites for Week ${currentWeek}`, 'success');
    }

    async function upsetSpecial() {
        const games = await loadWeeklySchedule(currentWeek);
        const standings = calculateStandings(Math.max(1, currentWeek - 1));
        let count = 0;
        const maxUpsets = 2;

        // Find potential upset candidates
        const upsetCandidates = games.map(game => {
            const gameId = `${currentWeek}-${game.away}-${game.home}`;
            const homeRecord = standings[game.home] || { w: 0, l: 0, t: 0 };
            const awayRecord = standings[game.away] || { w: 0, l: 0, t: 0 };

            const homeWinPct = getWinPercentage(homeRecord);
            const awayWinPct = getWinPercentage(awayRecord);

            const favorite = (homeWinPct + 0.1) > awayWinPct ? game.home : game.away;
            const underdog = favorite === game.home ? game.away : game.home;
            const winPctDiff = Math.abs(homeWinPct - awayWinPct);

            return { gameId, underdog, winPctDiff };
        }).filter(game => game.winPctDiff > 0.1) // Only meaningful underdogs
            .sort((a, b) => b.winPctDiff - a.winPctDiff) // Sort by biggest upset potential
            .slice(0, maxUpsets);

        // Apply upset picks
        upsetCandidates.forEach(({ gameId, underdog }) => {
            if (!gamePredictions[gameId]) {
                gamePredictions[gameId] = { winner: underdog, timestamp: Date.now() };
                count++;
            }
        });

        await renderGames();
        updateScoreDashboardOnly();
        updateWeeklyChallengesOnly();
        updateStandingsOnly();

        showNotification(`Selected ${count} upset picks for Week ${currentWeek}!`, 'success');
    }

    function exportBracket() {
        showPlayoffBracketModal();
    }

    function showPlayoffBracketModal() {
        const modal = document.getElementById('playoff-bracket-modal');
        const container = document.getElementById('playoff-bracket-full');

        // Clear and render the playoff bracket
        container.innerHTML = '';
        renderCleanPlayoffBracket(container);

        modal.style.display = 'flex';
    }

    function renderCleanPlayoffBracket(container) {
        // Get current standings
        const standings = calculateStandings(currentWeek);

        // Get top 7 teams from each conference
        const afcTeams = Object.values(standings)
            .filter(team => teams[team.id] && teams[team.id].conf === 'AFC')
            .sort((a, b) => getWinPercentage(b) - getWinPercentage(a))
            .slice(0, 7);

        const nfcTeams = Object.values(standings)
            .filter(team => teams[team.id] && teams[team.id].conf === 'NFC')
            .sort((a, b) => getWinPercentage(b) - getWinPercentage(a))
            .slice(0, 7);

        // Create main bracket container
        container.innerHTML = `
            <div class="playoff-bracket-container">
                <h2 class="text-3xl font-bold text-center mb-8 text-yellow-400">🏆 NFL Playoff Bracket</h2>

                <div class="bracket-layout">
                    <!-- AFC Side -->
                    <div class="conference-side afc-side">
                        <h3 class="conference-title afc-title">AFC</h3>
                        <div class="bracket-rounds">
                            <div class="round wildcard-round">
                                <h4 class="round-title">Wild Card</h4>
                                <div class="matchups">
                                    ${generateWildCardMatchups(afcTeams, 'AFC')}
                                </div>
                            </div>
                            <div class="round divisional-round">
                                <h4 class="round-title">Divisional</h4>
                                <div class="matchups">
                                    <div class="bye-team">
                                        <div class="team-card bye-card">
                                            <img src="${teams[afcTeams[0]?.id]?.logo}" class="team-logo">
                                            <span class="team-name">#1 ${teams[afcTeams[0]?.id]?.name}</span>
                                            <span class="bye-label">BYE</span>
                                        </div>
                                    </div>
                                    <div class="matchup-placeholder">TBD vs TBD</div>
                                </div>
                            </div>
                            <div class="round conference-round">
                                <h4 class="round-title">AFC Championship</h4>
                                <div class="matchups">
                                    <div class="matchup-placeholder">TBD vs TBD</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Super Bowl -->
                    <div class="super-bowl-section">
                        <div class="super-bowl-card">
                            <h3 class="super-bowl-title">Super Bowl LIX</h3>
                            <p class="super-bowl-date">February 9, 2025</p>
                            <p class="super-bowl-location">New Orleans, LA</p>
                            <div class="super-bowl-matchup">
                                <div class="championship-placeholder">AFC Champion vs NFC Champion</div>
                            </div>
                        </div>
                    </div>

                    <!-- NFC Side -->
                    <div class="conference-side nfc-side">
                        <h3 class="conference-title nfc-title">NFC</h3>
                        <div class="bracket-rounds">
                            <div class="round wildcard-round">
                                <h4 class="round-title">Wild Card</h4>
                                <div class="matchups">
                                    ${generateWildCardMatchups(nfcTeams, 'NFC')}
                                </div>
                            </div>
                            <div class="round divisional-round">
                                <h4 class="round-title">Divisional</h4>
                                <div class="matchups">
                                    <div class="bye-team">
                                        <div class="team-card bye-card">
                                            <img src="${teams[nfcTeams[0]?.id]?.logo}" class="team-logo">
                                            <span class="team-name">#1 ${teams[nfcTeams[0]?.id]?.name}</span>
                                            <span class="bye-label">BYE</span>
                                        </div>
                                    </div>
                                    <div class="matchup-placeholder">TBD vs TBD</div>
                                </div>
                            </div>
                            <div class="round conference-round">
                                <h4 class="round-title">NFC Championship</h4>
                                <div class="matchups">
                                    <div class="matchup-placeholder">TBD vs TBD</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function generateWildCardMatchups(teams, conference) {
        if (teams.length < 7) return '<div class="no-teams">Not enough teams</div>';

        const matchups = [
            { away: teams[6], home: teams[1], seed: '#7 at #2' },
            { away: teams[5], home: teams[2], seed: '#6 at #3' },
            { away: teams[4], home: teams[3], seed: '#5 at #4' }
        ];

        return matchups.map(matchup => `
            <div class="matchup-card">
                <div class="seed-label">${matchup.seed}</div>
                <div class="team-matchup">
                    <div class="team-card away-team">
                        <img src="${teams[matchup.away.id]?.logo}" class="team-logo">
                        <span class="team-name">${teams[matchup.away.id]?.name}</span>
                        <span class="team-record">${matchup.away.w}-${matchup.away.l}</span>
                    </div>
                    <div class="vs-divider">@</div>
                    <div class="team-card home-team">
                        <img src="${teams[matchup.home.id]?.logo}" class="team-logo">
                        <span class="team-name">${teams[matchup.home.id]?.name}</span>
                        <span class="team-record">${matchup.home.w}-${matchup.home.l}</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    function renderFullPlayoffBracket(container) {
        const standings = calculateStandings(18);
        container.innerHTML = '';

        // Create main bracket structure with proper NFL layout: NFC Wildcard -> NFC Conference -> Super Bowl <- AFC Conference <- AFC Wildcard
        const bracketWrapper = document.createElement('div');
        bracketWrapper.className = 'flex justify-center items-start gap-4 min-w-full overflow-x-auto p-4';
        bracketWrapper.style.minWidth = '1200px';

        // NFC Wildcard (leftmost)
        const nfcWildcardDiv = document.createElement('div');
        nfcWildcardDiv.className = 'conference-bracket flex-1';
        nfcWildcardDiv.style.order = '1';

        const nfcWildcardTitleDiv = document.createElement('div');
        nfcWildcardTitleDiv.className = 'text-center mb-6';
        nfcWildcardTitleDiv.innerHTML = `<h3 class="text-2xl font-bold text-blue-400">NFC Wildcard</h3>`;
        nfcWildcardDiv.appendChild(nfcWildcardTitleDiv);

        // Get NFC teams and sort by record
        const nfcTeams = Object.values(standings)
            .filter(team => teams[team.id] && teams[team.id].conf === 'NFC')
            .sort((a, b) => {
                const aWinPct = getWinPercentage(a);
                const bWinPct = getWinPercentage(b);
                return bWinPct - aWinPct;
            })
            .slice(0, 7); // Top 7 NFC teams

        renderWildcardBracket(nfcWildcardDiv, nfcTeams, 'NFC');
        bracketWrapper.appendChild(nfcWildcardDiv);

        // NFC Conference (left-center)
        const nfcConfDiv = document.createElement('div');
        nfcConfDiv.className = 'conference-bracket flex-1';
        nfcConfDiv.style.order = '2';

        const nfcConfTitleDiv = document.createElement('div');
        nfcConfTitleDiv.className = 'text-center mb-6';
        nfcConfTitleDiv.innerHTML = `<h3 class="text-2xl font-bold text-blue-400">NFC Conference</h3>`;
        nfcConfDiv.appendChild(nfcConfTitleDiv);

        renderFullConferenceBracketSection(nfcConfDiv, nfcTeams, 'NFC');
        bracketWrapper.appendChild(nfcConfDiv);

        // Super Bowl in the middle
        const superBowlDiv = document.createElement('div');
        superBowlDiv.className = 'super-bowl-container flex-shrink-0';
        superBowlDiv.style.order = '3';

        const superBowlContent = document.createElement('div');
        superBowlContent.className = 'text-center';
        superBowlContent.innerHTML = `
            <div class="mb-4">
                <h3 class="text-2xl font-bold text-yellow-400">Super Bowl LIX</h3>
                <p class="text-sm text-gray-400">February 9, 2025</p>
                <p class="text-sm text-gray-400">New Orleans, LA</p>
            </div>
            <div class="super-bowl-matchup bg-gradient-to-r from-red-600 to-blue-600 p-6 rounded-lg min-h-[200px] flex items-center justify-center">
                <div class="text-center text-white">
                    <div class="text-lg font-bold mb-2">Championship Game</div>
                    <div class="text-sm">AFC Champion vs NFC Champion</div>
                </div>
            </div>
        `;
        superBowlDiv.appendChild(superBowlContent);
        bracketWrapper.appendChild(superBowlDiv);

        // AFC Conference (right-center)
        const afcConfDiv = document.createElement('div');
        afcConfDiv.className = 'conference-bracket flex-1';
        afcConfDiv.style.order = '4';

        const afcConfTitleDiv = document.createElement('div');
        afcConfTitleDiv.className = 'text-center mb-6';
        afcConfTitleDiv.innerHTML = `<h3 class="text-2xl font-bold text-red-400">AFC Conference</h3>`;
        afcConfDiv.appendChild(afcConfTitleDiv);

        // Get AFC teams and sort by record
        const afcTeams = Object.values(standings)
            .filter(team => teams[team.id] && teams[team.id].conf === 'AFC')
            .sort((a, b) => {
                const aWinPct = getWinPercentage(a);
                const bWinPct = getWinPercentage(b);
                return bWinPct - aWinPct;
            })
            .slice(0, 7); // Top 7 AFC teams

        renderFullConferenceBracketSection(afcConfDiv, afcTeams, 'AFC');
        bracketWrapper.appendChild(afcConfDiv);

        // AFC Wildcard (rightmost)
        const afcWildcardDiv = document.createElement('div');
        afcWildcardDiv.className = 'conference-bracket flex-1';
        afcWildcardDiv.style.order = '5';

        const afcWildcardTitleDiv = document.createElement('div');
        afcWildcardTitleDiv.className = 'text-center mb-6';
        afcWildcardTitleDiv.innerHTML = `<h3 class="text-2xl font-bold text-red-400">AFC Wildcard</h3>`;
        afcWildcardDiv.appendChild(afcWildcardTitleDiv);

        renderWildcardBracket(afcWildcardDiv, afcTeams, 'AFC');
        bracketWrapper.appendChild(afcWildcardDiv);

        container.appendChild(bracketWrapper);
    }

    function renderWildcardBracket(container, confTeams, conference) {
        // Create wildcard round structure
        const roundsDiv = document.createElement('div');
        roundsDiv.className = 'space-y-6';

        // Wild Card Round (Seeds 2-7)
        const wildCardDiv = document.createElement('div');
        wildCardDiv.className = 'playoff-round';
        wildCardDiv.innerHTML = `<h4 class="text-lg font-bold mb-4 text-center">Wild Card Round</h4>`;

        const wildCardGames = document.createElement('div');
        wildCardGames.className = 'space-y-3';

        // Wild Card matchups: #7 at #2, #6 at #3, #5 at #4
        const wildCardMatchups = [
            { away: confTeams[6], home: confTeams[1], gameLabel: '#7 at #2' },
            { away: confTeams[5], home: confTeams[2], gameLabel: '#6 at #3' },
            { away: confTeams[4], home: confTeams[3], gameLabel: '#5 at #4' }
        ];

        wildCardMatchups.forEach((matchup, index) => {
            if (matchup.away && matchup.home) {
                const gameDiv = document.createElement('div');
                gameDiv.className = 'playoff-matchup bg-gray-700 p-3 rounded-lg border border-gray-600';
                gameDiv.innerHTML = `
                    <div class="text-center text-xs text-gray-400 mb-2">${matchup.gameLabel}</div>
                    <div class="flex flex-col space-y-2">
                        <div class="team-option flex items-center space-x-2 cursor-pointer p-2 rounded hover:bg-blue-600"
                             data-team-id="${matchup.away.id}" data-conf="${conference}" data-round="wildcard" data-key="game${index + 1}">
                            <img src="${teams[matchup.away.id].logo}" class="w-5 h-5">
                            <span class="text-sm">${teams[matchup.away.id].name}</span>
                        </div>
                        <div class="text-center text-xs text-gray-400">@</div>
                        <div class="team-option flex items-center space-x-2 cursor-pointer p-2 rounded hover:bg-blue-600"
                             data-team-id="${matchup.home.id}" data-conf="${conference}" data-round="wildcard" data-key="game${index + 1}">
                            <img src="${teams[matchup.home.id].logo}" class="w-5 h-5">
                            <span class="text-sm">${teams[matchup.home.id].name}</span>
                        </div>
                    </div>
                `;
                wildCardGames.appendChild(gameDiv);
            }
        });

        wildCardDiv.appendChild(wildCardGames);
        roundsDiv.appendChild(wildCardDiv);

        container.appendChild(roundsDiv);
    }

    function renderFullConferenceBracketSection(container, confTeams, conference) {
        // Create conference championship structure
        const roundsDiv = document.createElement('div');
        roundsDiv.className = 'space-y-8';

        // #1 Seed Bye
        const byeDiv = document.createElement('div');
        byeDiv.className = 'playoff-matchup bg-green-900 bg-opacity-20 border-green-500 p-4 rounded-lg';
        byeDiv.innerHTML = `
            <div class="text-center text-green-400 font-bold text-sm mb-2">BYE WEEK</div>
            <div class="flex justify-center items-center">
                <div class="flex items-center space-x-2">
                    <img src="${teams[confTeams[0].id].logo}" class="w-8 h-8">
                    <span class="font-bold">#1 ${teams[confTeams[0].id].name}</span>
                </div>
            </div>
            <div class="text-center mt-2">
                <div class="text-xs text-gray-400">Advances to Divisional Round</div>
            </div>
        `;
        roundsDiv.appendChild(byeDiv);

        // Divisional Round
        const divisionalDiv = document.createElement('div');
        divisionalDiv.className = 'playoff-round mt-8';
        divisionalDiv.innerHTML = `
            <h4 class="text-lg font-bold mb-4 text-center">Divisional Round</h4>
            <div class="space-y-3">
                <div class="playoff-matchup bg-gray-700 p-4 rounded-lg border border-gray-600">
                    <div class="text-center text-xs text-gray-400 mb-2">#1 Seed vs Lowest Remaining Seed</div>
                    <div class="text-center text-gray-300">TBD after Wild Card</div>
                </div>
                <div class="playoff-matchup bg-gray-700 p-4 rounded-lg border border-gray-600">
                    <div class="text-center text-xs text-gray-400 mb-2">Remaining Wild Card Winners</div>
                    <div class="text-center text-gray-300">TBD after Wild Card</div>
                </div>
            </div>
        `;
        roundsDiv.appendChild(divisionalDiv);

        // Conference Championship
        const confChampDiv = document.createElement('div');
        confChampDiv.className = 'playoff-round mt-8';
        confChampDiv.innerHTML = `
            <h4 class="text-lg font-bold mb-4 text-center">${conference} Championship</h4>
            <div class="playoff-matchup bg-gray-700 p-4 rounded-lg border border-gray-600">
                <div class="text-center text-xs text-gray-400 mb-2">Divisional Round Winners</div>
                <div class="text-center text-gray-300">TBD after Divisional Round</div>
            </div>
        `;
        roundsDiv.appendChild(confChampDiv);

        container.appendChild(roundsDiv);
    }

    function renderFullConferenceBracket(container, confTeams, conference) {
        // Create rounds structure with proper NFL reseeding
        const roundsDiv = document.createElement('div');
        roundsDiv.className = 'space-y-8';

        // Wild Card Round (Seeds 2-7)
        const wildCardDiv = document.createElement('div');
        wildCardDiv.className = 'playoff-round';
        wildCardDiv.innerHTML = `<h4 class="text-lg font-bold mb-4 text-center">Wild Card Round</h4>`;

        const wildCardGames = document.createElement('div');
        wildCardGames.className = 'space-y-3';

        // Wild Card matchups: #7 at #2, #6 at #3, #5 at #4
        const wildCardMatchups = [
            { away: confTeams[6], home: confTeams[1], gameLabel: '#7 at #2' },
            { away: confTeams[5], home: confTeams[2], gameLabel: '#6 at #3' },
            { away: confTeams[4], home: confTeams[3], gameLabel: '#5 at #4' }
        ];

        wildCardMatchups.forEach((matchup, index) => {
            if (matchup.away && matchup.home) {
                const gameDiv = document.createElement('div');
                gameDiv.className = 'playoff-matchup bg-gray-700 p-4 rounded-lg border border-gray-600';
                gameDiv.innerHTML = `
                    <div class="text-center text-xs text-gray-400 mb-2">${matchup.gameLabel}</div>
                    <div class="flex justify-between items-center">
                        <div class="team-option flex items-center space-x-2 cursor-pointer p-2 rounded hover:bg-blue-600"
                             data-team-id="${matchup.away.id}" data-conf="${conference}" data-round="wildcard" data-key="game${index + 1}">
                            <img src="${teams[matchup.away.id].logo}" class="w-6 h-6">
                            <span class="text-sm">${matchup.away.id} ${teams[matchup.away.id].name}</span>
                        </div>
                        <div class="text-xs text-gray-400">@</div>
                        <div class="team-option flex items-center space-x-2 cursor-pointer p-2 rounded hover:bg-blue-600"
                             data-team-id="${matchup.home.id}" data-conf="${conference}" data-round="wildcard" data-key="game${index + 1}">
                            <img src="${teams[matchup.home.id].logo}" class="w-6 h-6">
                            <span class="text-sm">${matchup.home.id} ${teams[matchup.home.id].name}</span>
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <div class="text-xs text-gray-400">Saturday/Sunday, January 11-12, 2025</div>
                    </div>
                `;
                wildCardGames.appendChild(gameDiv);
            }
        });

        wildCardDiv.appendChild(wildCardGames);

        // #1 Seed Bye
        const byeDiv = document.createElement('div');
        byeDiv.className = 'playoff-matchup bg-green-900 bg-opacity-20 border-green-500 p-4 rounded-lg';
        byeDiv.innerHTML = `
            <div class="text-center text-green-400 font-bold text-sm mb-2">BYE WEEK</div>
            <div class="flex justify-center items-center">
                <div class="flex items-center space-x-2">
                    <img src="${teams[confTeams[0].id].logo}" class="w-8 h-8">
                    <span class="font-bold">#1 ${teams[confTeams[0].id].name}</span>
                </div>
            </div>
            <div class="text-center mt-2">
                <div class="text-xs text-gray-400">Advances to Divisional Round</div>
            </div>
        `;

        roundsDiv.appendChild(wildCardDiv);
        roundsDiv.appendChild(byeDiv);

        // Divisional Round
        const divisionalDiv = document.createElement('div');
        divisionalDiv.className = 'playoff-round mt-8';
        divisionalDiv.innerHTML = `
            <h4 class="text-lg font-bold mb-4 text-center">Divisional Round</h4>
            <div class="space-y-3">
                <div class="playoff-matchup bg-gray-700 p-4 rounded-lg border border-gray-600">
                    <div class="text-center text-xs text-gray-400 mb-2">#1 Seed vs Lowest Remaining Seed</div>
                    <div class="text-center text-gray-300">TBD after Wild Card</div>
                    <div class="text-center mt-2">
                        <div class="text-xs text-gray-400">Saturday/Sunday, January 18-19, 2025</div>
                    </div>
                </div>
                <div class="playoff-matchup bg-gray-700 p-4 rounded-lg border border-gray-600">
                    <div class="text-center text-xs text-gray-400 mb-2">Remaining Wild Card Winners</div>
                    <div class="text-center text-gray-300">TBD after Wild Card</div>
                    <div class="text-center mt-2">
                        <div class="text-xs text-gray-400">Saturday/Sunday, January 18-19, 2025</div>
                    </div>
                </div>
            </div>
        `;

        roundsDiv.appendChild(divisionalDiv);

        // Conference Championship
        const confChampDiv = document.createElement('div');
        confChampDiv.className = 'playoff-round mt-8';
        confChampDiv.innerHTML = `
            <h4 class="text-lg font-bold mb-4 text-center">${conference} Championship</h4>
            <div class="playoff-matchup bg-gray-700 p-4 rounded-lg border border-gray-600">
                <div class="text-center text-xs text-gray-400 mb-2">Divisional Round Winners</div>
                <div class="text-center text-gray-300">TBD after Divisional Round</div>
                <div class="text-center mt-2">
                    <div class="text-xs text-gray-400">Sunday, January 26, 2025</div>
                </div>
            </div>
        `;

        roundsDiv.appendChild(confChampDiv);
        container.appendChild(roundsDiv);
    }

    function importBracket() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = JSON.parse(e.target.result);
                        if (data.gamePredictions) gamePredictions = data.gamePredictions;
                        if (data.playoffPredictions) playoffPredictions = data.playoffPredictions;
                        if (data.confidencePicks) confidencePicks = data.confidencePicks;

                        // Full UI refresh after import
                        renderGames();
                        renderPlayoffBracket();
                        renderScoreDashboard();
                        renderStandings();
                        renderPlayoffRace();
                        renderPowerRankings();
                        renderWeeklyChallenges();

                        showNotification('Bracket imported successfully!', 'success');
                    } catch (error) {
                        showNotification('Error importing bracket', 'error');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    function resetAll() {
        const totalPredictions = Object.keys(gamePredictions).length +
            Object.keys(playoffPredictions.AFC?.wildcard || {}).length +
            Object.keys(playoffPredictions.AFC?.divisional || {}).length +
            Object.keys(playoffPredictions.NFC?.wildcard || {}).length +
            Object.keys(playoffPredictions.NFC?.divisional || {}).length;

        if (totalPredictions === 0) {
            showNotification('No predictions to reset!', 'error');
            return;
        }

        // Use custom confirmation modal for Google Sites compatibility
        showConfirmationModal(
            'Reset All Predictions',
            `Are you sure you want to reset ALL ${totalPredictions} predictions? This cannot be undone.`,
            () => {
            gamePredictions = {};
            playoffPredictions = {
                AFC: { wildcard: {}, divisional: {}, conference: {} },
                NFC: { wildcard: {}, divisional: {}, conference: {} },
                superBowl: null
            };
            confidencePicks = {};

            // Full UI refresh after reset
            renderGames();
            renderPlayoffBracket();
            renderScoreDashboard();
            renderStandings();
            renderPlayoffRace();
            renderPowerRankings();
            renderWeeklyChallenges();

            // Save empty state to Firebase
            if (currentUserId && db) {
                try {
                    const docRef = doc(db, "users", currentUserId);
                    setDoc(docRef, { gamePredictions, playoffPredictions, confidencePicks }, { merge: true });
                } catch (error) {
                    console.error('Error saving reset to Firebase:', error);
                }
            }

            showNotification('All predictions reset successfully!', 'success');
            }
        );
    }

    function showConfirmationModal(title, message, onConfirm) {
        const modal = document.getElementById('game-modal');
        const content = document.getElementById('game-modal-content');

        content.innerHTML = `
            <h3 class="text-xl font-bold mb-4 text-red-400">${title}</h3>
            <div class="space-y-4">
                <p class="text-gray-300">${message}</p>
                <div class="flex space-x-3">
                    <button class="confirm-btn btn-primary bg-red-600 hover:bg-red-700 flex-1">
                        Yes, Reset All
                    </button>
                    <button class="cancel-btn btn-secondary flex-1">
                        Cancel
                    </button>
                </div>
            </div>
        `;

        // Add event listeners
        content.querySelector('.confirm-btn').addEventListener('click', () => {
            modal.style.display = 'none';
            onConfirm();
        });

        content.querySelector('.cancel-btn').addEventListener('click', () => {
            modal.style.display = 'none';
        });

        modal.style.display = 'flex';
        applyModalPositioning(modal);
    }

    function shareBracket() {
        const scores = calculateBracketScore();
        const upsetCount = Object.keys(gamePredictions).filter(gameId => isUpsetPick(gameId)).length;

        // Build detailed predictions text
        let text = `🏈 My NFL Predictions!\n\n`;

        // Add current week predictions
        text += `📅 Week ${currentWeek} Picks:\n`;
        const currentWeekPredictions = Object.keys(gamePredictions)
            .filter(gameId => gameId.startsWith(`${currentWeek}-`))
            .slice(0, 8); // Limit to avoid too long text

        currentWeekPredictions.forEach(gameId => {
            const [week, away, home] = gameId.split('-');
            const prediction = gamePredictions[gameId];
            const awayTeam = teams[away];
            const homeTeam = teams[home];
            const confidence = confidencePicks[gameId]?.confidence || '';
            const scorePrediction = scorePredictions[gameId];

            if (awayTeam && homeTeam && prediction) {
                const winnerTeam = teams[prediction.winner];
                let gameText = '';

                if (winnerTeam) {
                    gameText = `• ${winnerTeam.name}`;
                    if (scorePrediction && scorePrediction.awayScore && scorePrediction.homeScore) {
                        const awayScore = scorePrediction.awayScore;
                        const homeScore = scorePrediction.homeScore;
                        gameText += ` (${awayTeam.name} ${awayScore} - ${homeScore} ${homeTeam.name})`;
                    }
                    gameText += ` ${confidence ? `(${confidence})` : ''}\n`;
                } else if (prediction.winner === 'tie') {
                    gameText = `• ${awayTeam.name} vs ${homeTeam.name} - TIE`;
                    if (scorePrediction && scorePrediction.awayScore && scorePrediction.homeScore) {
                        gameText += ` (${scorePrediction.awayScore}-${scorePrediction.homeScore})`;
                    }
                    gameText += '\n';
                }

                text += gameText;
            }
        });

        // Add summary stats
        text += `\n📊 Stats:\n`;
        text += `• Total Predictions: ${Object.keys(gamePredictions).length}\n`;
        text += `• Upset Picks: ${upsetCount}\n`;
        text += `• Current Score: ${scores.total} points\n`;

        // Add playoff predictions if any
        const playoffPicks = Object.keys(playoffPredictions.AFC?.wildcard || {}).length +
                           Object.keys(playoffPredictions.NFC?.wildcard || {}).length;
        if (playoffPicks > 0) {
            text += `• Playoff Picks: ${playoffPicks}\n`;
        }

        text += `\n🏆 Made with NFL Predictor Pro`;

        // Always show manual copy modal for Google Sites compatibility
        showManualCopyModal(text);
    }

    function fallbackShare(text) {
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification('Bracket details copied to clipboard!', 'success');
            }).catch(() => {
                showManualCopyModal(text);
            });
        } else {
            showManualCopyModal(text);
        }
    }

    function showManualCopyModal(text) {
        const modal = document.getElementById('game-modal');
        const content = document.getElementById('game-modal-content');

        content.innerHTML = `
            <h3 class="text-xl font-bold mb-4">📱 Share Your Predictions</h3>
            <div class="space-y-4">
                <p class="text-gray-400">Copy the text below to share your predictions:</p>
                <textarea class="w-full h-32 p-3 bg-gray-700 rounded-lg text-white resize-none" readonly>${text}</textarea>
                <button class="copy-text-btn btn-primary w-full">
                    📋 Copy to Clipboard
                </button>
            </div>
        `;

        // Add event listener for copy button
        content.querySelector('.copy-text-btn').addEventListener('click', () => {
            const textarea = content.querySelector('textarea');
            textarea.select();
            try {
                document.execCommand('copy');
                showNotification('Copied!', 'success');
            } catch (err) {
                showNotification('Copy failed', 'error');
            }
        });

        modal.style.display = 'flex';
    }

    function getGameWeather(gameId) {
        // Sample weather data based on game matchup
        const [week, away, home] = gameId.split('-');
        const homeTeam = teams[home];
        const awayTeam = teams[away];

        if (!homeTeam || !awayTeam) return '<div>Weather data unavailable</div>';

        // Generate weather based on home team location and time of year
        const weatherConditions = {
            'BUF': { temp: '45°F', condition: '❄️ Snow possible', impact: 'High', wind: '15 mph' },
            'GB': { temp: '38°F', condition: '🌨️ Cold & windy', impact: 'High', wind: '20 mph' },
            'NE': { temp: '42°F', condition: '🌧️ Rain likely', impact: 'Medium', wind: '12 mph' },
            'CHI': { temp: '40°F', condition: '💨 Windy', impact: 'Medium', wind: '18 mph' },
            'DEN': { temp: '55°F', condition: '🏔️ Mile High', impact: 'Medium', wind: '8 mph' },
            'SEA': { temp: '52°F', condition: '☁️ Overcast', impact: 'Low', wind: '10 mph' },
            'SF': { temp: '65°F', condition: '🌤️ Partly cloudy', impact: 'Low', wind: '6 mph' },
            'MIA': { temp: '82°F', condition: '☀️ Sunny & hot', impact: 'Medium', wind: '5 mph' },
            'TB': { temp: '78°F', condition: '⛈️ Thunderstorms', impact: 'High', wind: '14 mph' },
            'NO': { temp: '75°F', condition: '🌫️ Humid', impact: 'Low', wind: '7 mph' },
            'LV': { temp: '88°F', condition: '🏜️ Hot & dry', impact: 'Medium', wind: '4 mph' },
            'LAR': { temp: '72°F', condition: '☀️ Perfect', impact: 'Low', wind: '3 mph' },
            'LAC': { temp: '70°F', condition: '☀️ Ideal', impact: 'Low', wind: '5 mph' }
        };

        const weather = weatherConditions[home] || { temp: '68°F', condition: '☀️ Clear', impact: 'Low', wind: '8 mph' };

        return `
            <div class="flex justify-between items-center">
                <div>
                    <div class="font-medium">${weather.condition}</div>
                    <div class="text-xs text-gray-400">Temperature: ${weather.temp}</div>
                    <div class="text-xs text-gray-400">Wind: ${weather.wind}</div>
                </div>
                <div class="text-right">
                    <div class="text-xs font-bold ${weather.impact === 'High' ? 'text-red-400' : weather.impact === 'Medium' ? 'text-yellow-400' : 'text-green-400'}">
                        ${weather.impact} Impact
                    </div>
                </div>
            </div>
        `;
    }

    function showMagicNumbers() {
        const standings = calculateStandings(currentWeek);
        const probabilities = calculatePlayoffProbabilities();

        let magicHtml = '<h3 class="text-lg font-bold mb-4">Magic Numbers</h3><div class="space-y-3">';

        ['AFC', 'NFC'].forEach(conf => {
            const confTeams = Object.values(standings)
                .filter(team => teams[team.id] && teams[team.id].conf === conf)
                .sort((a, b) => getWinPercentage(b) - getWinPercentage(a))
                .slice(0, 10);

            magicHtml += `<h4 class="font-semibold text-gray-300">${conf}</h4>`;

            confTeams.forEach((team, index) => {
                const gamesLeft = 17 - (team.w + team.l + team.t);
                let magicNumber = 'Clinched';

                if (index < 4) {
                    // Division leaders
                    magicNumber = Math.max(0, 12 - team.w);
                } else if (index < 7) {
                    // Wild card contenders
                    magicNumber = Math.max(0, 10 - team.w);
                } else {
                    magicNumber = Math.max(0, 11 - team.w);
                }

                if (magicNumber === 0) magicNumber = 'Clinched';

                const logoClass = team.id === 'NYJ' ? 'jets-logo' : team.id === 'CAR' ? 'panthers-logo' : '';

                magicHtml += `
                        <div class="flex items-center justify-between p-2 bg-gray-700 rounded">
                            <div class="flex items-center space-x-2">
                                <img src="${teams[team.id].logo}" class="w-5 h-5 ${logoClass}">
                                <span class="text-sm">${teams[team.id].name}</span>
                            </div>
                            <span class="text-sm font-bold ${magicNumber === 'Clinched' ? 'text-green-400' : 'text-yellow-400'}">${magicNumber}</span>
                        </div>
                    `;
            });
        });

        magicHtml += '</div>';

        const modal = document.getElementById('game-modal');
        document.getElementById('game-modal-title').textContent = 'Playoff Magic Numbers';
        document.getElementById('game-modal-content').innerHTML = magicHtml;
        modal.style.display = 'flex';
    }

    async function showLiveScores() {
        const modal = document.getElementById('game-modal');
        const content = document.getElementById('game-modal-content');

        // Get current week games for upcoming section
        const currentWeekGames = await loadWeeklySchedule(currentWeek);
        const upcomingGames = currentWeekGames.slice(0, 4); // Show first 4 games

        content.innerHTML = `
            <h3 class="text-xl font-bold mb-4">📈 Live Scores</h3>
            <div class="space-y-4">
                <div class="bg-green-900 bg-opacity-20 p-4 rounded-lg">
                    <h5 class="font-bold text-green-400 mb-2">🟢 Live Games</h5>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <span>KC Chiefs @ BAL Ravens</span>
                            <span class="font-bold">21-14 (Q3 8:42)</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span>BUF Bills @ MIA Dolphins</span>
                            <span class="font-bold">17-10 (Q2 2:15)</span>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-900 bg-opacity-20 p-4 rounded-lg">
                    <h5 class="font-bold text-blue-400 mb-2">✅ Final Scores</h5>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <span>DEN Broncos @ LAC Chargers</span>
                            <span class="font-bold">28-21 (Final)</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span>SEA Seahawks @ SF 49ers</span>
                            <span class="font-bold">24-17 (Final)</span>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-700 p-4 rounded-lg">
                    <h5 class="font-bold mb-2">⏰ Upcoming Games - Week ${currentWeek}</h5>
                    <div class="space-y-2 text-sm">
                        ${upcomingGames.map(game => {
                            const awayTeam = teams[game.away];
                            const homeTeam = teams[game.home];
                            if (!awayTeam || !homeTeam) return '';

                            return `
                                <div class="flex justify-between items-center">
                                    <span>${awayTeam.name} @ ${homeTeam.name}</span>
                                    <div class="text-right">
                                        <div class="text-gray-400">${formatGameDate(game)}</div>
                                        <div class="text-gray-300">${formatGameTime(game)}</div>
                                    </div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>

                <div class="text-center">
                    <a href="https://methstreams.ac/get-nflstreams/" target="_blank" class="btn-primary">
                        📺 Watch Live Streams
                    </a>
                </div>
            </div>
        `;

        modal.style.display = 'flex';
    }

    // Navigation Functions
    function switchToWeek(week) {
        currentWeek = week;
        currentView = 'schedule';

        document.getElementById('content-title').textContent = `Week ${week} Games`;
        document.getElementById('games-container').classList.remove('hidden');
        document.getElementById('playoff-container').classList.add('hidden');

        renderWeekNavigation();
        renderGames();
        renderWeeklyChallenges();
    }

    function switchToPlayoffs() {
        currentView = 'playoffs';

        document.getElementById('content-title').textContent = 'Playoff Bracket';
        document.getElementById('games-container').classList.add('hidden');
        document.getElementById('playoff-container').classList.remove('hidden');

        renderWeekNavigation();
        renderPlayoffBracket();
    }

    // Utility Functions
    function showNotification(message, type = 'success') {
        const notification = document.getElementById('notification');
        const content = document.getElementById('notification-content');

        content.textContent = message;
        notification.className = `notification ${type} show`;

        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }

    function toggleTheme() {
        console.log('toggleTheme called, current isDarkTheme:', isDarkTheme);
        isDarkTheme = !isDarkTheme;
        document.body.className = isDarkTheme ? 'dark-theme' : 'light-theme';
        const themeBtn = document.getElementById('theme-toggle');
        if (themeBtn) {
            themeBtn.textContent = isDarkTheme ? '🌙' : '☀️';
        }
        localStorage.setItem('theme', isDarkTheme ? 'dark' : 'light');
        console.log('Theme changed to:', isDarkTheme ? 'dark' : 'light');
    }

    async function refreshData() {
        const refreshBtn = document.getElementById('refresh-data');
        const originalText = refreshBtn.textContent;
        refreshBtn.textContent = '🔄 Syncing...';
        refreshBtn.disabled = true;

        try {
            // Refresh current data
            if (currentView === 'schedule') {
                await renderGames();
            } else {
                renderPlayoffBracket();
            }

            await renderNews();
            renderConditions();
            renderStandings();
            renderPlayoffRace();
            renderPowerRankings();

            showNotification('Data refreshed!', 'success');
        } catch (error) {
            showNotification('Error refreshing data', 'error');
        } finally {
            refreshBtn.textContent = originalText;
            refreshBtn.disabled = false;
        }
    }

    // Authentication Functions
    // Setup basic event listeners that should work regardless of Firebase availability
    function setupBasicEventListeners() {
        console.log('Setting up basic event listeners...');

        // Login button - works even without Firebase
        const loginBtn = document.getElementById('login-btn');
        console.log('Login button found:', !!loginBtn);
        if (loginBtn) {
            loginBtn.onclick = (e) => {
                console.log('Login button clicked!');
                positionModalNearElement(e.target);
                const authModal = document.getElementById('auth-modal');
                if (authModal) {
                    authModal.style.display = 'flex';
                    applyModalPositioning(authModal);
                } else {
                    console.log('Auth modal not found');
                }
            };
        }

        // Auth wall login button
        const authWallLoginBtn = document.getElementById('auth-wall-login');
        if (authWallLoginBtn) {
            authWallLoginBtn.onclick = (e) => {
                console.log('Auth wall login button clicked!');
                positionModalNearElement(e.target);
                const authModal = document.getElementById('auth-modal');
                if (authModal) {
                    authModal.style.display = 'flex';
                    applyModalPositioning(authModal);
                }
            };
        }

        // Auth modal close functionality
        const authModal = document.getElementById('auth-modal');
        if (authModal) {
            authModal.onclick = (e) => {
                if (e.target === authModal) {
                    hideModal(authModal);
                }
            };
        }

        console.log('Basic event listeners setup complete');
    }

    function setupAuth() {
        if (!auth) {
            console.log('Firebase auth not available, using basic event listeners only');
            return;
        }

        const loginBtn = document.getElementById('login-btn');
        const authWallLoginBtn = document.getElementById('auth-wall-login');
        const logoutBtn = document.getElementById('logout-btn');
        const authModal = document.getElementById('auth-modal');
        const authForm = document.getElementById('auth-form');
        const authSwitch = document.getElementById('auth-switch');

        let isLoginMode = true;

        loginBtn.onclick = (e) => {
            positionModalNearElement(e.target);
            authModal.style.display = 'flex';
            applyModalPositioning(authModal);
        };

        authWallLoginBtn.onclick = (e) => {
            positionModalNearElement(e.target);
            authModal.style.display = 'flex';
            applyModalPositioning(authModal);
        };

        logoutBtn.onclick = () => {
            signOut(auth);
        };

        authModal.onclick = (e) => {
            if (e.target === authModal) {
                hideModal(authModal);
            }
        };

        authSwitch.onclick = () => {
            isLoginMode = !isLoginMode;
            document.getElementById('auth-title').textContent = isLoginMode ? 'Login' : 'Register';
            document.getElementById('auth-submit').textContent = isLoginMode ? 'Login' : 'Register';
            document.getElementById('auth-switch-text').textContent = isLoginMode ? "Don't have an account?" : "Already have an account?";
            authSwitch.textContent = isLoginMode ? 'Register' : 'Login';
        };

        authForm.onsubmit = async (e) => {
            e.preventDefault();
            const email = document.getElementById('email-input').value;
            const password = document.getElementById('password-input').value;
            const errorEl = document.getElementById('auth-error');

            try {
                if (isLoginMode) {
                    await signInWithEmailAndPassword(auth, email, password);
                } else {
                    await createUserWithEmailAndPassword(auth, email, password);
                }
                hideModal(authModal);
                errorEl.classList.add('hidden');
            } catch (error) {
                let errorMessage = 'Authentication failed. Please try again.';
                switch (error.code) {
                    case 'auth/user-not-found':
                        errorMessage = 'No account found with this email';
                        break;
                    case 'auth/wrong-password':
                    case 'auth/invalid-credential':
                        errorMessage = 'Incorrect password';
                        break;
                    case 'auth/email-already-in-use':
                        errorMessage = 'Email already registered';
                        break;
                    case 'auth/weak-password':
                        errorMessage = 'Password should be at least 6 characters';
                        break;
                    case 'auth/invalid-email':
                        errorMessage = 'Invalid email address';
                        break;
                }
                errorEl.textContent = errorMessage;
                errorEl.classList.remove('hidden');
            }
        };

        onAuthStateChanged(auth, async (user) => {
            if (user) {
                currentUserId = user.uid;
                document.getElementById('login-btn').style.display = 'none';
                document.getElementById('user-info-container').classList.remove('hidden');
                document.getElementById('user-info').textContent = user.email;

                // Show main content and hide auth wall
                document.getElementById('auth-wall').classList.add('hidden');
                document.getElementById('main-content').classList.remove('hidden');

                // Load user data
                await loadUserData(user.uid);
            } else {
                currentUserId = null;
                document.getElementById('login-btn').style.display = 'block';
                document.getElementById('user-info-container').classList.add('hidden');

                // Hide main content and show auth wall
                document.getElementById('auth-wall').classList.remove('hidden');
                document.getElementById('main-content').classList.add('hidden');
            }
        });
    }

    async function loadUserData(userId) {
        if (!db) return;

        try {
            const docRef = doc(db, "users", userId);
            const docSnap = await getDoc(docRef);

            if (docSnap.exists()) {
                const data = docSnap.data();
                if (data.gamePredictions) gamePredictions = data.gamePredictions;
                if (data.playoffPredictions) playoffPredictions = data.playoffPredictions;
                if (data.confidencePicks) confidencePicks = data.confidencePicks;
                if (data.displayName) userDisplayName = data.displayName;

                // Refresh UI
                renderGames();
                renderPlayoffBracket();
                renderScoreDashboard();
                renderStandings();
                renderPlayoffRace();
                renderPowerRankings();
                renderWeeklyChallenges();
            }
        } catch (error) {
            console.error('Error loading user data:', error);
        }
    }

    // Mobile optimization functions
    function optimizeForMobile() {
        // Add touch-friendly interactions
        document.addEventListener('touchstart', function() {}, { passive: true });
        document.addEventListener('touchmove', function() {}, { passive: true });

        // Improve scrolling performance
        const scrollElements = document.querySelectorAll('.modal-content, .playoff-bracket, .standings-container');
        scrollElements.forEach(element => {
            element.style.webkitOverflowScrolling = 'touch';
        });

        // Add momentum scrolling to modals
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.style.webkitOverflowScrolling = 'touch';
            modal.addEventListener('touchmove', (e) => {
                e.stopPropagation();
            }, { passive: true });
        });

        // Optimize button interactions for mobile
        const buttons = document.querySelectorAll('button, .team-selector, .mobile-team, .leaderboard-entry');
        buttons.forEach(button => {
            button.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
                this.style.transition = 'transform 0.1s ease';
            }, { passive: true });

            button.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            }, { passive: true });

            button.addEventListener('touchcancel', function() {
                this.style.transform = '';
            }, { passive: true });
        });

        // Add smooth scrolling to week navigation
        const weekTabs = document.querySelectorAll('.week-tab');
        weekTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                setTimeout(() => {
                    smoothScrollTo(document.getElementById('games-container'));
                }, 100);
            });
        });

        // Improve scroll performance
        let ticking = false;
        function updateScrollPosition() {
            // Update any scroll-dependent UI here
            ticking = false;
        }

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollPosition);
                ticking = true;
            }
        }, { passive: true });
    }

    function smoothScrollTo(element) {
        if (element) {
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'nearest'
            });
        }
    }

    // Initialize App
    async function initApp() {
        console.log('Initializing NFL Predictor Pro...');

        // Optimize for mobile
        if (window.innerWidth <= 768) {
            optimizeForMobile();
        }

        // Handle responsive layout changes
        window.addEventListener('resize', () => {
            if (window.innerWidth <= 768) {
                optimizeForMobile();
            }
            // Re-render current view for responsive changes
            if (currentView === 'schedule') {
                renderGames();
            } else if (currentView === 'playoffs') {
                renderPlayoffBracket();
            }
        });

        // Load saved theme
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            isDarkTheme = savedTheme === 'dark';
            if (!isDarkTheme) toggleTheme();
        }

        // Setup event listeners
        const themeToggle = document.getElementById('theme-toggle');
        console.log('Theme toggle found:', !!themeToggle);
        if (themeToggle) {
            themeToggle.onclick = () => {
                console.log('Theme toggle clicked!');
                toggleTheme();
            };
        }
        document.getElementById('live-scores-btn').onclick = (e) => {
            positionModalNearElement(e.target);
            showLiveScores();
        };
        document.getElementById('refresh-data').onclick = refreshData;
        document.getElementById('quick-home').onclick = predictAllHome;
        document.getElementById('quick-favorites').onclick = predictAllFavorites;
        document.getElementById('upset-special').onclick = upsetSpecial;
        document.getElementById('show-leaderboard').onclick = (e) => {
            positionModalNearElement(e.target);
            showLeaderboard();
        };

        document.getElementById('import-bracket').onclick = importBracket;
        document.getElementById('reset-all').onclick = (e) => {
            positionModalNearElement(e.target);
            resetAll();
        };
        document.getElementById('share-bracket').onclick = shareBracket;
        document.getElementById('magic-numbers').onclick = (e) => {
            positionModalNearElement(e.target);
            showMagicNumbers();
        };

        // Temporary bypass for authentication issues in Google Sites
        if (!auth || !db) {
            console.warn('Firebase not available, using guest mode');
            currentUserId = 'guest-user';
            document.getElementById('login-btn').style.display = 'none';
            document.getElementById('user-info-container').classList.remove('hidden');
            document.getElementById('user-info').textContent = 'Guest User';
            document.getElementById('auth-wall').classList.add('hidden');
            document.getElementById('main-content').classList.remove('hidden');
        }

        // Modal close buttons
        document.getElementById('close-team-modal').onclick = () => {
            hideModal(document.getElementById('team-modal'));
        };
        document.getElementById('close-game-modal').onclick = () => {
            hideModal(document.getElementById('game-modal'));
        };
        document.getElementById('close-history-modal').onclick = () => {
            hideModal(document.getElementById('history-modal'));
        };
        document.getElementById('close-leaderboard-modal').onclick = () => {
            hideModal(document.getElementById('leaderboard-modal'));
        };
        document.getElementById('close-username-modal').onclick = () => {
            hideModal(document.getElementById('username-modal'));
        };
        document.getElementById('close-playoff-bracket-modal').onclick = () => {
            hideModal(document.getElementById('playoff-bracket-modal'));
        };
        document.getElementById('save-display-name').onclick = saveDisplayName;
        document.getElementById('cancel-display-name').onclick = cancelDisplayName;

        // Make showHistoryModal global for onclick handlers
        window.showHistoryModal = showHistoryModal;

        // Standings view toggle with proper event handling
        document.getElementById('division-view').onclick = () => {
            standingsView = 'division';
            document.getElementById('division-view').classList.add('active');
            document.getElementById('conference-view').classList.remove('active');
            renderStandings();
        };
        document.getElementById('conference-view').onclick = () => {
            standingsView = 'conference';
            document.getElementById('conference-view').classList.add('active');
            document.getElementById('division-view').classList.remove('active');
            renderStandings();
        };

        // Close modals when clicking outside
        document.addEventListener('click', (e) => {
            const modals = ['team-modal', 'game-modal', 'history-modal', 'auth-modal', 'leaderboard-modal', 'username-modal', 'playoff-bracket-modal'];
            modals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (e.target === modal) {
                    hideModal(modal);
                }
            });
        });

        // Initialize UI
        renderWeekNavigation();
        await renderGames();
        renderStandings();
        renderPlayoffRace();
        renderPowerRankings();
        renderScoreDashboard();
        renderWeeklyChallenges();
        await renderNews();
        renderConditions();

        // Setup authentication and basic event listeners
        setupAuth();

        // Setup basic event listeners even if Firebase is not available
        setupBasicEventListeners();

        // Load enhanced injury data
        await loadInjuryData();

        console.log('App initialized successfully!');
    }

    // Start the app
    document.addEventListener('DOMContentLoaded', initApp);
</script>
</body>
</html>
