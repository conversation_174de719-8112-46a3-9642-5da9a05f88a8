<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' https: data: 'unsafe-inline' 'unsafe-eval';">
    <title>NFL Playoff Predictor Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        /* Base theme variables */
        :root {
            --primary-blue: #1e40af;
            --primary-green: #059669;
            --accent-gold: #f59e0b;
            --dark-bg: #0f172a;
            --card-bg: #1e293b;
            --border-color: #334155;
            --light-bg: #f8fafc;
            --light-card-bg: #ffffff;
            --light-border-color: #e2e8f0;
            --text-light: #e2e8f0;
            --text-dark: #1e293b;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            transition: background-color 0.3s ease;
        }

        /* Theme styles */
        .dark-theme {
            background: linear-gradient(135deg, var(--dark-bg) 0%, #1e293b 100%);
            color: var(--text-light);
        }

        .light-theme {
            background: var(--light-bg);
            color: var(--text-dark);
        }

        /* Card styles with proper mobile handling */
        .card {
            background: var(--card-bg);
            border-radius: 1rem;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 1rem;
        }

        .light-theme .card {
            background: var(--light-card-bg);
            border-color: var(--light-border-color);
        }

        /* Team logo consistent styling */
        .team-logo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: white !important;
            padding: 2px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            object-fit: contain;
        }

        /* Mobile-optimized game cards */
        .game-card {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
            border-radius: 0.75rem;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
            width: 100%;
            box-sizing: border-box;
        }

        .light-theme .game-card {
            background: var(--light-card-bg);
            border-color: var(--light-border-color);
        }

        /* Playoff bracket styling */
        .playoff-bracket {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            overflow-x: auto;
            padding: 1rem;
            position: relative;
        }

        .playoff-round {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .playoff-matchup {
            position: relative;
            padding: 1rem;
            background: var(--card-bg);
            border-radius: 0.75rem;
            border: 1px solid var(--border-color);
        }

        .light-theme .playoff-matchup {
            background: var(--light-card-bg);
            border-color: var(--light-border-color);
        }

        /* Playoff connector lines */
        .playoff-connector {
            position: absolute;
            background: var(--border-color);
            z-index: 1;
        }

        .light-theme .playoff-connector {
            background: var(--light-border-color);
        }

        /* Score input styling */
        .score-input {
            width: 60px;
            height: 36px;
            text-align: center;
            border: 2px solid var(--border-color);
            border-radius: 0.5rem;
            background: transparent;
            color: inherit;
            font-size: 1rem;
            font-weight: bold;
            transition: border-color 0.3s ease;
        }

        .score-input:focus {
            outline: none;
            border-color: var(--primary-blue);
        }

        /* Modal positioning */
        .modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
            background: var(--card-bg);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            max-width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .light-theme .modal {
            background: var(--light-card-bg);
        }

        /* Notification styling */
        .notification {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            padding: 1rem;
            border-radius: 0.75rem;
            background: var(--card-bg);
            color: var(--text-light);
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        .light-theme .notification {
            background: var(--light-card-bg);
            color: var(--text-dark);
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            .card {
                padding: 1rem;
            }

            .team-logo {
                width: 32px;
                height: 32px;
            }

            .score-input {
                width: 50px;
                height: 32px;
                font-size: 0.875rem;
            }

            .playoff-bracket {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body class="dark-theme">
    <!-- Navigation -->
    <nav class="sticky top-0 z-50 bg-opacity-90 backdrop-blur-md border-b border-border-color">
        <div class="max-w-7xl mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-bold">🏈 NFL Predictor Pro</h1>
                <div id="live-indicator" class="hidden px-2 py-1 bg-red-600 text-white text-sm rounded-full">LIVE</div>
            </div>
            <div class="flex items-center space-x-2">
                <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-700">🌙</button>
                <button id="export-btn" class="p-2 rounded-full hover:bg-gray-700">💾</button>
                <button id="import-btn" class="p-2 rounded-full hover:bg-gray-700">📂</button>
                <button id="reset-btn" class="p-2 rounded-full hover:bg-gray-700">🔄</button>
            </div>
        </div>
    </nav>

    <!-- Main content container -->
    <main class="max-w-7xl mx-auto px-4 py-6">
        <!-- Statistics cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <!-- Stats cards will be dynamically inserted here -->
        </div>

        <!-- Week selection -->
        <div class="card mb-6">
            <div class="flex flex-wrap gap-2" id="week-selector">
                <!-- Week buttons will be dynamically inserted here -->
            </div>
        </div>

        <!-- Games container -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2">
                <div id="games-container">
                    <!-- Game cards will be dynamically inserted here -->
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <div class="card">
                    <h2 class="text-lg font-bold mb-4">Standings</h2>
                    <div id="standings-container">
                        <!-- Standings will be dynamically inserted here -->
                    </div>
                </div>

                <div class="card">
                    <h2 class="text-lg font-bold mb-4">Injury Report</h2>
                    <div id="injury-container">
                        <!-- Injury reports will be dynamically inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modals container -->
    <div id="modals-container"></div>

    <!-- Notification container -->
    <div id="notification-container"></div>

    <script>

</body>
</html>
// Constants and State Management
const state = {
currentWeek: 1,
predictions: {},
playoffPredictions: {},
scores: {},
settings: {
theme: 'dark',
lastUpdate: null,
submissionDeadlines: {}
}
};

// Teams data with proper logo backgrounds
const teams = {
// AFC East
BUF: { name: 'Bills', conf: 'AFC', div: 'East', logo: 'path/to/bills.png' },
MIA: { name: 'Dolphins', conf: 'AFC', div: 'East', logo: 'path/to/dolphins.png' },
NE: { name: 'Patriots', conf: 'AFC', div: 'East', logo: 'path/to/patriots.png' },
NYJ: { name: 'Jets', conf: 'AFC', div: 'East', logo: 'path/to/jets.png' },
// Add other teams...
};

// Utility Functions
const debounce = (func, wait) => {
let timeout;
return function executedFunction(...args) {
const later = () => {
clearTimeout(timeout);
func(...args);
};
clearTimeout(timeout);
timeout = setTimeout(later, wait);
};
};

const showNotification = (message, type = 'info') => {
const notification = document.createElement('div');
notification.className = `notification ${type} ${state.settings.theme}-theme`;
notification.textContent = message;
document.getElementById('notification-container').appendChild(notification);
setTimeout(() => notification.remove(), 3000);
};

// Score Input Handler with Fix for Weeks 2-18
const handleScoreInput = (event) => {
const { gameId, team } = event.target.dataset;
const value = parseInt(event.target.value) || 0;

if (!state.scores[gameId]) {
state.scores[gameId] = {};
}

state.scores[gameId][team] = value;

// Prevent page refresh
event.preventDefault();

// Save without refreshing
saveState();

// Update UI smoothly
updateGameCard(gameId);
};

// Prediction Handler without Refresh
const handlePrediction = (event) => {
const { gameId, team } = event.target.dataset;
const gameWeek = parseInt(gameId.split('-')[0]);

// Check if prediction is allowed (game hasn't started)
if (isPredictionLocked(gameId)) {
showNotification("Predictions are locked for this game", "error");
return;
}

// Update prediction without page refresh
state.predictions[gameId] = team;

// Save state
saveState();

// Smooth UI update
updateGameCard(gameId);
updateStats();
};

// Playoff Bracket Visualization
const renderPlayoffBracket = () => {
const container = document.getElementById('playoff-bracket');
container.innerHTML = '';

const rounds = ['wildcard', 'divisional', 'conference', 'superbowl'];

rounds.forEach((round, roundIndex) => {
const roundDiv = document.createElement('div');
roundDiv.className = 'playoff-round';

// Add connecting lines
if (roundIndex > 0) {
addConnectorLines(roundDiv, roundIndex);
}

// Add matchups
const matchups = getPlayoffMatchups(round);
matchups.forEach(matchup => {
const matchupDiv = createMatchupElement(matchup, round);
roundDiv.appendChild(matchupDiv);
});

container.appendChild(roundDiv);
});
};

// Fix for Monday Night Games Display
const processSchedule = (weekData) => {
return weekData.map(game => {
// Ensure Monday night games are properly marked
if (game.day === 'Monday') {
game.isMonday = true;
game.network = game.network || 'ESPN';
game.gameTime = game.gameTime || '8:15 PM ET';
}
return game;
});
};

// Dynamic Injury Report
const updateInjuryReport = async () => {
const container = document.getElementById('injury-container');
try {
const injuries = await fetchInjuryData();
container.innerHTML = '';

if (!injuries.length) {
container.innerHTML = '<p class="text-gray-400">No injuries reported</p>';
return;
}

injuries.forEach(injury => {
const injuryElement = createInjuryElement(injury);
container.appendChild(injuryElement);
});
} catch (error) {
console.error('Error updating injury report:', error);
container.innerHTML = '<p class="text-red-400">Error loading injury report</p>';
}
};

// Light Mode Optimization
const updateTheme = (theme) => {
document.body.className = `${theme}-theme`;

// Update text colors for better contrast
const textElements = document.querySelectorAll('.text-content');
textElements.forEach(el => {
el.className = theme === 'light'
? 'text-content text-gray-800'
: 'text-content text-gray-200';
});

// Update notification colors
const notifications = document.querySelectorAll('.notification');
notifications.forEach(n => {
n.className = `notification ${theme}-theme`;
});

state.settings.theme = theme;
saveState();
};

// Reset and Magic Numbers Implementation
const handleReset = () => {
if (confirm('Are you sure you want to reset all predictions?')) {
state.predictions = {};
state.scores = {};
state.playoffPredictions = {};
saveState();
location.reload();
}
};

const calculateMagicNumbers = () => {
const standings = calculateStandings();
const magicNumbers = {};

Object.keys(teams).forEach(team => {
magicNumbers[team] = calculateTeamMagicNumber(team, standings);
});

return magicNumbers;
};

// Export/Import Functionality
const exportPredictions = async () => {
try {
const element = document.getElementById('predictions-container');
const canvas = await html2canvas(element);

// Create full-page screenshot
const pdf = new jsPDF();
const imgData = canvas.toDataURL('image/png');
pdf.addImage(imgData, 'PNG', 0, 0, 210, 297);
pdf.save('predictions.pdf');

showNotification('Predictions exported successfully!', 'success');
} catch (error) {
console.error('Export error:', error);
showNotification('Error exporting predictions', 'error');
}
};
// Helper Functions
const formatGameTime = (game) => {
try {
const gameDate = new Date(game.timestamp);
const options = {
weekday: 'short',
hour: 'numeric',
minute: '2-digit',
timeZoneName: 'short'
};
return gameDate.toLocaleString('en-US', options);
} catch (error) {
console.error('Error formatting game time:', error);
return game.gameTime || 'TBD';
}
};

const getTeamRecord = (teamId) => {
try {
const standings = calculateStandings();
const record = standings[teamId] || { wins: 0, losses: 0, ties: 0 };
return `${record.wins}-${record.losses}${record.ties ? `-${record.ties}` : ''}`;
} catch (error) {
console.error('Error getting team record:', error);
return '0-0';
}
};

const calculateStandings = () => {
const standings = {};

// Initialize standings for all teams
Object.keys(teams).forEach(teamId => {
standings[teamId] = { wins: 0, losses: 0, ties: 0, id: teamId };
});

// Process predictions and scores
Object.entries(state.predictions).forEach(([gameId, winner]) => {
const [week, away, home] = gameId.split('-');
if (parseInt(week) < state.currentWeek) {
if (winner === 'tie') {
standings[away].ties++;
standings[home].ties++;
} else {
standings[winner].wins++;
standings[winner === home ? away : home].losses++;
}
}
});

return standings;
};

const getWinPercentage = (record) => {
const { wins, losses, ties } = record;
const totalGames = wins + losses + ties;
return totalGames === 0 ? 0 : (wins + ties * 0.5) / totalGames;
};

const isPredictionLocked = (gameId) => {
try {
const [week, away, home] = gameId.split('-');
const gameWeek = parseInt(week);

// Check if game is in the past
if (gameWeek < state.currentWeek) return true;

// Check submission deadline
const deadline = state.settings.submissionDeadlines[gameId];
if (deadline) {
return new Date() > new Date(deadline);
}

return false;
} catch (error) {
console.error('Error checking prediction lock:', error);
return true; // Lock by default if there's an error
}
};

// Error Handling Wrapper
const errorHandler = (fn) => {
return async (...args) => {
try {
return await fn(...args);
} catch (error) {
console.error(`Error in ${fn.name}:`, error);
showNotification(`Error: ${error.message}`, 'error');
throw error;
}
};
};

// Additional CSS (add to the existing style tag)
const additionalStyles = `
/* Enhanced Mobile Optimizations */
@media (max-width: 768px) {
.mobile-optimized {
padding: 0.75rem;
}

.mobile-optimized .team-row {
flex-direction: row;
align-items: center;
padding: 0.5rem;
}

.mobile-optimized .team-logo {
width: 32px;
height: 32px;
}

.mobile-optimized .score-input {
width: 40px;
height: 32px;
font-size: 0.875rem;
}

.mobile-optimized .game-header {
flex-direction: column;
gap: 0.25rem;
}

.game-card.monday-night {
background: linear-gradient(135deg, var(--card-bg), var(--primary-blue));
}

.mobile-input {
-webkit-appearance: none;
border-radius: 4px;
padding: 4px;
}
}

/* Enhanced Animation Effects */
.winner {
animation: pulse 2s infinite;
}

@keyframes pulse {
0% { transform: scale(1); }
50% { transform: scale(1.02); }
100% { transform: scale(1); }
}

/* Loading States */
.loading {
display: flex;
align-items: center;
justify-content: center;
min-height: 200px;
background: var(--card-bg);
border-radius: 1rem;
animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
0% { opacity: 0.5; }
50% { opacity: 1; }
100% { opacity: 0.5; }
}

/* Enhanced Notification Styles */
.notification {
animation: slideIn 0.3s ease, fadeOut 0.3s ease 2.7s;
border-left: 4px solid var(--primary-blue);
}

.notification.error {
border-left-color: #ef4444;
}

.notification.success {
border-left-color: #10b981;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
.winner,
.notification,
.loading {
animation: none;
}
}

/* High Contrast Mode */
@media (prefers-contrast: more) {
.game-card {
border-width: 2px;
}

.team-logo {
border: 2px solid currentColor;
}
}
`;

// Initialize Error Tracking
const initErrorTracking = () => {
window.onerror = (msg, url, lineNo, columnNo, error) => {
console.error('Global error:', { msg, url, lineNo, columnNo, error });
showNotification('An unexpected error occurred', 'error');
return false;
};

window.addEventListener('unhandledrejection', (event) => {
console.error('Unhandled promise rejection:', event.reason);
showNotification('An unexpected error occurred', 'error');
});
};

// Performance Monitoring
const performanceMonitor = {
metrics: {},

start: (label) => {
performanceMonitor.metrics[label] = performance.now();
},

end: (label) => {
const startTime = performanceMonitor.metrics[label];
if (startTime) {
const duration = performance.now() - startTime;
console.debug(`${label} took ${duration.toFixed(2)}ms`);
delete performanceMonitor.metrics[label];
}
}
};

// Data Validation
const validateGameData = (game) => {
const required = ['away', 'home', 'timestamp'];
const missing = required.filter(field => !game[field]);

if (missing.length > 0) {
throw new Error(`Invalid game data: missing ${missing.join(', ')}`);
}

if (!teams[game.away] || !teams[game.home]) {
throw new Error(`Invalid team ID in game: ${game.away} @ ${game.home}`);
}

return true;
};

// Initialize the application with error handling
const safeInitialize = errorHandler(async () => {
initErrorTracking();
performanceMonitor.start('app-init');

await initializeApp();

// Add the additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);

performanceMonitor.end('app-init');
});

// Start the application
document.addEventListener('DOMContentLoaded', safeInitialize);
// Helper Functions
const formatGameTime = (game) => {
try {
const gameDate = new Date(game.timestamp);
const options = {
weekday: 'short',
hour: 'numeric',
minute: '2-digit',
timeZoneName: 'short'
};
return gameDate.toLocaleString('en-US', options);
} catch (error) {
console.error('Error formatting game time:', error);
return game.gameTime || 'TBD';
}
};

const getTeamRecord = (teamId) => {
try {
const standings = calculateStandings();
const record = standings[teamId] || { wins: 0, losses: 0, ties: 0 };
return `${record.wins}-${record.losses}${record.ties ? `-${record.ties}` : ''}`;
} catch (error) {
console.error('Error getting team record:', error);
return '0-0';
}
};

const calculateStandings = () => {
const standings = {};

// Initialize standings for all teams
Object.keys(teams).forEach(teamId => {
standings[teamId] = { wins: 0, losses: 0, ties: 0, id: teamId };
});

// Process predictions and scores
Object.entries(state.predictions).forEach(([gameId, winner]) => {
const [week, away, home] = gameId.split('-');
if (parseInt(week) < state.currentWeek) {
if (winner === 'tie') {
standings[away].ties++;
standings[home].ties++;
} else {
standings[winner].wins++;
standings[winner === home ? away : home].losses++;
}
}
});

return standings;
};

const getWinPercentage = (record) => {
const { wins, losses, ties } = record;
const totalGames = wins + losses + ties;
return totalGames === 0 ? 0 : (wins + ties * 0.5) / totalGames;
};

const isPredictionLocked = (gameId) => {
try {
const [week, away, home] = gameId.split('-');
const gameWeek = parseInt(week);

// Check if game is in the past
if (gameWeek < state.currentWeek) return true;

// Check submission deadline
const deadline = state.settings.submissionDeadlines[gameId];
if (deadline) {
return new Date() > new Date(deadline);
}

return false;
} catch (error) {
console.error('Error checking prediction lock:', error);
return true; // Lock by default if there's an error
}
};

// Error Handling Wrapper
const errorHandler = (fn) => {
return async (...args) => {
try {
return await fn(...args);
} catch (error) {
console.error(`Error in ${fn.name}:`, error);
showNotification(`Error: ${error.message}`, 'error');
throw error;
}
};
};

// Additional CSS (add to the existing style tag)
const additionalStyles = `
/* Enhanced Mobile Optimizations */
@media (max-width: 768px) {
.mobile-optimized {
padding: 0.75rem;
}

.mobile-optimized .team-row {
flex-direction: row;
align-items: center;
padding: 0.5rem;
}

.mobile-optimized .team-logo {
width: 32px;
height: 32px;
}

.mobile-optimized .score-input {
width: 40px;
height: 32px;
font-size: 0.875rem;
}

.mobile-optimized .game-header {
flex-direction: column;
gap: 0.25rem;
}

.game-card.monday-night {
background: linear-gradient(135deg, var(--card-bg), var(--primary-blue));
}

.mobile-input {
-webkit-appearance: none;
border-radius: 4px;
padding: 4px;
}
}

/* Enhanced Animation Effects */
.winner {
animation: pulse 2s infinite;
}

@keyframes pulse {
0% { transform: scale(1); }
50% { transform: scale(1.02); }
100% { transform: scale(1); }
}

/* Loading States */
.loading {
display: flex;
align-items: center;
justify-content: center;
min-height: 200px;
background: var(--card-bg);
border-radius: 1rem;
animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
0% { opacity: 0.5; }
50% { opacity: 1; }
100% { opacity: 0.5; }
}

/* Enhanced Notification Styles */
.notification {
animation: slideIn 0.3s ease, fadeOut 0.3s ease 2.7s;
border-left: 4px solid var(--primary-blue);
}

.notification.error {
border-left-color: #ef4444;
}

.notification.success {
border-left-color: #10b981;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
.winner,
.notification,
.loading {
animation: none;
}
}

/* High Contrast Mode */
@media (prefers-contrast: more) {
.game-card {
border-width: 2px;
}

.team-logo {
border: 2px solid currentColor;
}
}
`;

// Initialize Error Tracking
const initErrorTracking = () => {
window.onerror = (msg, url, lineNo, columnNo, error) => {
console.error('Global error:', { msg, url, lineNo, columnNo, error });
showNotification('An unexpected error occurred', 'error');
return false;
};

window.addEventListener('unhandledrejection', (event) => {
console.error('Unhandled promise rejection:', event.reason);
showNotification('An unexpected error occurred', 'error');
});
};

// Performance Monitoring
const performanceMonitor = {
metrics: {},

start: (label) => {
performanceMonitor.metrics[label] = performance.now();
},

end: (label) => {
const startTime = performanceMonitor.metrics[label];
if (startTime) {
const duration = performance.now() - startTime;
console.debug(`${label} took ${duration.toFixed(2)}ms`);
delete performanceMonitor.metrics[label];
}
}
};

// Data Validation
const validateGameData = (game) => {
const required = ['away', 'home', 'timestamp'];
const missing = required.filter(field => !game[field]);

if (missing.length > 0) {
throw new Error(`Invalid game data: missing ${missing.join(', ')}`);
}

if (!teams[game.away] || !teams[game.home]) {
throw new Error(`Invalid team ID in game: ${game.away} @ ${game.home}`);
}

return true;
};

// Initialize the application with error handling
const safeInitialize = errorHandler(async () => {
initErrorTracking();
performanceMonitor.start('app-init');

await initializeApp();

// Add the additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);

performanceMonitor.end('app-init');
});

// Start the application
document.addEventListener('DOMContentLoaded', safeInitialize);
// Accessibility Enhancements
const initializeA11y = () => {
// Add ARIA labels and roles
document.querySelectorAll('.game-card').forEach(card => {
card.setAttribute('role', 'region');
card.setAttribute('aria-label', 'Game prediction card');
});

// Add keyboard navigation
initKeyboardNavigation();

// Add screen reader announcements
initScreenReaderAnnouncements();

// Add focus management
initFocusManagement();
};

const initKeyboardNavigation = () => {
const handleKeyPress = (event) => {
const target = event.target;

if (target.classList.contains('team-row')) {
if (event.key === 'Enter' || event.key === ' ') {
event.preventDefault();
handleTeamClick({ currentTarget: target });
}
}

if (target.classList.contains('score-input')) {
if (event.key === 'ArrowUp') {
event.preventDefault();
incrementScore(target);
} else if (event.key === 'ArrowDown') {
event.preventDefault();
decrementScore(target);
}
}
};

document.addEventListener('keydown', handleKeyPress);
};

// Advanced Caching System
const cache = {
data: new Map(),
timeouts: new Map(),

async get(key) {
const cached = this.data.get(key);
if (!cached) return null;

if (cached.expiry < Date.now()) {
this.data.delete(key);
return null;
}

return cached.value;
},

set(key, value, ttl = 300000) { // 5 minutes default TTL
const expiry = Date.now() + ttl;
this.data.set(key, { value, expiry });

// Clear existing timeout
if (this.timeouts.has(key)) {
clearTimeout(this.timeouts.get(key));
}

// Set new timeout
const timeout = setTimeout(() => {
this.data.delete(key);
this.timeouts.delete(key);
}, ttl);

this.timeouts.set(key, timeout);
},

clear() {
this.data.clear();
this.timeouts.forEach(clearTimeout);
this.timeouts.clear();
}
};

// Performance Optimizations
const optimizePerformance = () => {
// Implement virtual scrolling for long lists
initVirtualScroll();

// Optimize images
lazyLoadImages();

// Implement request batching
initRequestBatching();

// Add resource hints
addResourceHints();
};

const initVirtualScroll = () => {
const virtualScroller = {
container: null,
items: [],
itemHeight: 0,
visibleItems: 10,

init(containerId, items, itemHeight) {
this.container = document.getElementById(containerId);
this.items = items;
this.itemHeight = itemHeight;

this.container.style.height = `${items.length * itemHeight}px`;
this.container.style.position = 'relative';

this.render();
this.attachScrollListener();
},

render() {
const scrollTop = this.container.scrollTop;
const startIndex = Math.floor(scrollTop / this.itemHeight);
const endIndex = startIndex + this.visibleItems;

const visibleItems = this.items
.slice(startIndex, endIndex)
.map((item, index) => {
const top = (startIndex + index) * this.itemHeight;
return `
<div class="virtual-item" style="position: absolute; top: ${top}px;">
    ${item}
</div>
`;
})
.join('');

this.container.innerHTML = visibleItems;
},

attachScrollListener() {
this.container.addEventListener('scroll',
debounce(() => this.render(), 16) // ~60fps
);
}
};

return virtualScroller;
};

// Progressive Enhancement
const progressiveEnhancement = {
init() {
this.checkFeatures();
this.applyEnhancements();
},

checkFeatures() {
const features = {
localStorage: !!window.localStorage,
serviceWorker: 'serviceWorker' in navigator,
webp: this.checkWebP(),
touchEvents: 'ontouchstart' in window,
connection: 'connection' in navigator
};

return features;
},

async checkWebP() {
const webP = new Image();
webP.src = 'data:image/webp;base64,UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==';
return new Promise(resolve => {
webP.onload = webP.onerror = () => {
resolve(webP.height === 1);
};
});
},

applyEnhancements() {
const features = this.checkFeatures();

if (features.serviceWorker) {
this.registerServiceWorker();
}

if (features.webp) {
this.enableWebPImages();
}

if (features.connection) {
this.adaptToConnection();
}
}
};

// Network Request Optimization
const networkOptimizer = {
queue: [],
batchSize: 5,
batchDelay: 50,
processingBatch: false,

addRequest(request) {
this.queue.push(request);
this.processBatch();
},

async processBatch() {
if (this.processingBatch || this.queue.length === 0) return;

this.processingBatch = true;
const batch = this.queue.splice(0, this.batchSize);

try {
const results = await Promise.all(batch.map(req => fetch(req)));
this.processingBatch = false;

if (this.queue.length > 0) {
setTimeout(() => this.processBatch(), this.batchDelay);
}

return results;
} catch (error) {
console.error('Batch processing error:', error);
this.processingBatch = false;
}
}
};

// Resource Loading Optimization
const resourceLoader = {
preloadedResources: new Set(),

preloadCriticalResources() {
const criticalResources = [
{ type: 'image', url: '/path/to/logo.png' },
{ type: 'script', url: '/path/to/critical.js' },
{ type: 'style', url: '/path/to/critical.css' }
];

criticalResources.forEach(resource => {
if (!this.preloadedResources.has(resource.url)) {
this.preload(resource);
}
});
},

preload({ type, url }) {
const link = document.createElement('link');
link.rel = 'preload';
link.as = type;
link.href = url;
document.head.appendChild(link);
this.preloadedResources.add(url);
}
};

// Initialize optimizations
document.addEventListener('DOMContentLoaded', () => {
initializeA11y();
optimizePerformance();
progressiveEnhancement.init();
resourceLoader.preloadCriticalResources();
});

// Service Worker Registration
if ('serviceWorker' in navigator) {
window.addEventListener('load', () => {
navigator.serviceWorker.register('/sw.js').then(registration => {
console.log('ServiceWorker registration successful');
}).catch(err => {
console.error('ServiceWorker registration failed:', err);
});
});
}
// Firebase Configuration and Initialization
const firebaseConfig = {
// Replace with your Firebase config
apiKey: "YOUR_API_KEY",
authDomain: "your-app.firebaseapp.com",
projectId: "your-project-id",
storageBucket: "your-app.appspot.com",
messagingSenderId: "your-messaging-sender-id",
appId: "your-app-id"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const db = firebase.firestore();

// Authentication State Management
const authState = {
user: null,
isAuthenticated: false,
loading: true
};

// Authentication UI Components
const createAuthUI = () => {
const authContainer = document.createElement('div');
authContainer.id = 'auth-container';
authContainer.innerHTML = `
<div class="auth-modal">
    <div class="auth-tabs">
        <button class="auth-tab active" data-tab="login">Login</button>
        <button class="auth-tab" data-tab="signup">Sign Up</button>
    </div>

    <div class="auth-form-container">
        <!-- Login Form -->
        <form id="login-form" class="auth-form active">
            <h2>Welcome Back</h2>
            <div class="form-group">
                <input type="email" id="login-email" placeholder="Email" required>
            </div>
            <div class="form-group">
                <input type="password" id="login-password" placeholder="Password" required>
            </div>
            <button type="submit" class="auth-button">Login</button>
            <button type="button" class="google-auth-button">
                <img src="/images/google-icon.png" alt="Google">
                Sign in with Google
            </button>
            <p class="forgot-password">
                <a href="#" id="forgot-password-link">Forgot Password?</a>
            </p>
        </form>

        <!-- Sign Up Form -->
        <form id="signup-form" class="auth-form">
            <h2>Create Account</h2>
            <div class="form-group">
                <input type="text" id="signup-name" placeholder="Full Name" required>
            </div>
            <div class="form-group">
                <input type="email" id="signup-email" placeholder="Email" required>
            </div>
            <div class="form-group">
                <input type="password" id="signup-password" placeholder="Password" required>
            </div>
            <button type="submit" class="auth-button">Sign Up</button>
            <button type="button" class="google-auth-button">
                <img src="/images/google-icon.png" alt="Google">
                Sign up with Google
            </button>
        </form>
    </div>
</div>
`;

return authContainer;
};

// Authentication Styles
const authStyles = `
.auth-modal {
background: var(--card-bg);
border-radius: 12px;
padding: 2rem;
max-width: 400px;
width: 90%;
margin: 2rem auto;
box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.auth-tabs {
display: flex;
margin-bottom: 2rem;
border-bottom: 2px solid var(--border-color);
}

.auth-tab {
flex: 1;
padding: 1rem;
background: none;
border: none;
color: var(--text-color);
cursor: pointer;
font-size: 1.1rem;
transition: all 0.3s ease;
}

.auth-tab.active {
color: var(--primary-blue);
border-bottom: 2px solid var(--primary-blue);
}

.auth-form {
display: none;
}

.auth-form.active {
display: block;
}

.form-group {
margin-bottom: 1.5rem;
}

.form-group input {
width: 100%;
padding: 0.75rem;
border: 1px solid var(--border-color);
border-radius: 6px;
background: var(--input-bg);
color: var(--text-color);
font-size: 1rem;
}

.auth-button {
width: 100%;
padding: 0.75rem;
background: var(--primary-blue);
color: white;
border: none;
border-radius: 6px;
font-size: 1rem;
cursor: pointer;
transition: background 0.3s ease;
}

.auth-button:hover {
background: var(--primary-blue-dark);
}

.google-auth-button {
width: 100%;
padding: 0.75rem;
background: white;
color: #757575;
border: 1px solid #dadce0;
border-radius: 6px;
font-size: 1rem;
cursor: pointer;
display: flex;
align-items: center;
justify-content: center;
margin-top: 1rem;
}

.google-auth-button img {
width: 18px;
margin-right: 0.5rem;
}

.forgot-password {
text-align: center;
margin-top: 1rem;
}

.forgot-password a {
color: var(--primary-blue);
text-decoration: none;
}
`;

// Authentication Logic
const initializeAuth = () => {
// Add auth styles
const style = document.createElement('style');
style.textContent = authStyles;
document.head.appendChild(style);

// Listen for auth state changes
auth.onAuthStateChanged(user => {
authState.loading = false;
authState.user = user;
authState.isAuthenticated = !!user;

if (user) {
hideAuthUI();
showApp();
syncUserData(user);
} else {
showAuthUI();
hideApp();
}
});
};

const authHandlers = {
async login(email, password) {
try {
await auth.signInWithEmailAndPassword(email, password);
showNotification('Successfully logged in!', 'success');
} catch (error) {
console.error('Login error:', error);
showNotification(error.message, 'error');
}
},

async signup(email, password, name) {
try {
const userCredential = await auth.createUserWithEmailAndPassword(email, password);
await userCredential.user.updateProfile({ displayName: name });

// Create user document in Firestore
await db.collection('users').doc(userCredential.user.uid).set({
name,
email,
createdAt: firebase.firestore.FieldValue.serverTimestamp(),
predictions: {},
settings: {
theme: 'dark',
notifications: true
}
});

showNotification('Account created successfully!', 'success');
} catch (error) {
console.error('Signup error:', error);
showNotification(error.message, 'error');
}
},

async googleAuth() {
try {
const provider = new firebase.auth.GoogleAuthProvider();
const result = await auth.signInWithPopup(provider);

// Check if this is a new user
const userDoc = await db.collection('users').doc(result.user.uid).get();

if (!userDoc.exists) {
// Create user document for new Google users
await db.collection('users').doc(result.user.uid).set({
name: result.user.displayName,
email: result.user.email,
createdAt: firebase.firestore.FieldValue.serverTimestamp(),
predictions: {},
settings: {
theme: 'dark',
notifications: true
}
});
}

showNotification('Successfully logged in with Google!', 'success');
} catch (error) {
console.error('Google auth error:', error);
showNotification(error.message, 'error');
}
},

async resetPassword(email) {
try {
await auth.sendPasswordResetEmail(email);
showNotification('Password reset email sent!', 'success');
} catch (error) {
console.error('Password reset error:', error);
showNotification(error.message, 'error');
}
},

async logout() {
try {
await auth.signOut();
showNotification('Successfully logged out!', 'success');
} catch (error) {
console.error('Logout error:', error);
showNotification(error.message, 'error');
}
}
};

// UI Helpers
const showAuthUI = () => {
const main = document.querySelector('main');
main.innerHTML = '';
main.appendChild(createAuthUI());

// Add event listeners
document.querySelectorAll('.auth-tab').forEach(tab => {
tab.addEventListener('click', () => {
document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
document.querySelectorAll('.auth-form').forEach(f => f.classList.remove('active'));
tab.classList.add('active');
document.querySelector(`#${tab.dataset.tab}-form`).classList.add('active');
});
});

// Form submissions
document.getElementById('login-form').addEventListener('submit', async (e) => {
e.preventDefault();
const email = document.getElementById('login-email').value;
const password = document.getElementById('login-password').value;
await authHandlers.login(email, password);
});

document.getElementById('signup-form').addEventListener('submit', async (e) => {
e.preventDefault();
const name = document.getElementById('signup-name').value;
const email = document.getElementById('signup-email').value;
const password = document.getElementById('signup-password').value;
await authHandlers.signup(email, password, name);
});
};

const hideAuthUI = () => {
const authContainer = document.getElementById('auth-container');
if (authContainer) {
authContainer.remove();
}
};

// Data Synchronization
const syncUserData = async (user) => {
try {
const doc = await db.collection('users').doc(user.uid).get();
if (doc.exists) {
const userData = doc.data();
state.predictions = userData.predictions || {};
state.settings = userData.settings || {};
updateUI();
}
} catch (error) {
console.error('Error syncing user data:', error);
showNotification('Error loading your predictions', 'error');
}
};

// Modified save state to sync with Firebase
const saveState = debounce(async () => {
if (!authState.isAuthenticated) return;

try {
await db.collection('users').doc(authState.user.uid).update({
predictions: state.predictions,
settings: state.settings,
lastUpdated: firebase.firestore.FieldValue.serverTimestamp()
});

showNotification('Progress saved', 'success');
} catch (error) {
console.error('Error saving state:', error);
showNotification('Error saving progress', 'error');
}
}, 1000);

// Initialize the app with authentication
document.addEventListener('DOMContentLoaded', () => {
initializeAuth();
});
// Real-time Synchronization
const realtimeSync = {
activeSubscriptions: new Map(),

initializeSync() {
if (!authState.isAuthenticated) return;

// Subscribe to user's predictions
this.subscribeToPredictions();

// Subscribe to leagues
this.subscribeToLeagues();

// Subscribe to friends' activities
this.subscribeToFriendActivities();
},

async subscribeToPredictions() {
const unsubscribe = db.collection('users')
.doc(authState.user.uid)
.onSnapshot(doc => {
if (doc.exists) {
const userData = doc.data();
state.predictions = userData.predictions || {};
state.settings = userData.settings || {};
updateUI();
}
});

this.activeSubscriptions.set('predictions', unsubscribe);
},

async subscribeToLeagues() {
const unsubscribe = db.collection('leagues')
.where('members', 'array-contains', authState.user.uid)
.onSnapshot(snapshot => {
const leagues = [];
snapshot.forEach(doc => {
leagues.push({ id: doc.id, ...doc.data() });
});
state.leagues = leagues;
updateLeaguesUI();
});

this.activeSubscriptions.set('leagues', unsubscribe);
},

cleanup() {
this.activeSubscriptions.forEach(unsubscribe => unsubscribe());
this.activeSubscriptions.clear();
}
};

// Social Features
const socialFeatures = {
async createLeague(leagueName, isPrivate = true) {
try {
const leagueRef = await db.collection('leagues').add({
name: leagueName,
creator: authState.user.uid,
members: [authState.user.uid],
admins: [authState.user.uid],
isPrivate,
created: firebase.firestore.FieldValue.serverTimestamp(),
inviteCode: isPrivate ? this.generateInviteCode() : null,
standings: {}
});

showNotification('League created successfully!', 'success');
return leagueRef.id;
} catch (error) {
console.error('Error creating league:', error);
showNotification('Error creating league', 'error');
}
},

async joinLeague(inviteCode) {
try {
const leagueSnapshot = await db.collection('leagues')
.where('inviteCode', '==', inviteCode)
.get();

if (leagueSnapshot.empty) {
throw new Error('Invalid invite code');
}

const league = leagueSnapshot.docs[0];
await league.ref.update({
members: firebase.firestore.FieldValue.arrayUnion(authState.user.uid)
});

showNotification('Successfully joined league!', 'success');
} catch (error) {
console.error('Error joining league:', error);
showNotification(error.message, 'error');
}
},

async sharePrediction(gameId, leagueId) {
try {
await db.collection('leagues')
.doc(leagueId)
.collection('shared_predictions')
.add({
gameId,
userId: authState.user.uid,
prediction: state.predictions[gameId],
timestamp: firebase.firestore.FieldValue.serverTimestamp()
});

showNotification('Prediction shared!', 'success');
} catch (error) {
console.error('Error sharing prediction:', error);
showNotification('Error sharing prediction', 'error');
}
},

generateInviteCode() {
return Math.random().toString(36).substring(2, 8).toUpperCase();
}
};

// User Profile Management
const userProfile = {
async updateProfile(updates) {
try {
const user = authState.user;

// Update display name if provided
if (updates.displayName) {
await user.updateProfile({ displayName: updates.displayName });
}

// Update user document
await db.collection('users').doc(user.uid).update({
name: updates.displayName || user.displayName,
avatar: updates.avatar,
preferences: updates.preferences,
updatedAt: firebase.firestore.FieldValue.serverTimestamp()
});

showNotification('Profile updated successfully!', 'success');
} catch (error) {
console.error('Error updating profile:', error);
showNotification('Error updating profile', 'error');
}
},

async getProfileStats() {
try {
const userDoc = await db.collection('users').doc(authState.user.uid).get();
const userData = userDoc.data();

const stats = {
totalPredictions: Object.keys(userData.predictions || {}).length,
correctPredictions: 0,
leagues: [],
ranking: null
};

// Calculate correct predictions
Object.entries(userData.predictions || {}).forEach(([gameId, prediction]) => {
if (gameResults[gameId] && gameResults[gameId].winner === prediction) {
stats.correctPredictions++;
}
});

// Get user's leagues
const leaguesSnapshot = await db.collection('leagues')
.where('members', 'array-contains', authState.user.uid)
.get();

stats.leagues = leaguesSnapshot.docs.map(doc => ({
id: doc.id,
name: doc.data().name
}));

return stats;
} catch (error) {
console.error('Error fetching profile stats:', error);
throw error;
}
}
};

// Admin Panel
class AdminPanel {
constructor() {
this.isAdmin = false;
this.checkAdminStatus();
}

async checkAdminStatus() {
if (!authState.isAuthenticated) return;

try {
const userDoc = await db.collection('users').doc(authState.user.uid).get();
this.isAdmin = userDoc.data()?.isAdmin || false;

if (this.isAdmin) {
this.initializeAdminPanel();
}
} catch (error) {
console.error('Error checking admin status:', error);
}
}

initializeAdminPanel() {
const adminContainer = document.createElement('div');
adminContainer.id = 'admin-panel';
adminContainer.innerHTML = this.getAdminPanelHTML();
document.body.appendChild(adminContainer);

this.attachEventListeners();
this.loadAdminStats();
}

getAdminPanelHTML() {
return `
<div class="admin-panel-container">
    <h2>Admin Panel</h2>
    <div class="admin-tabs">
        <button class="admin-tab active" data-tab="overview">Overview</button>
        <button class="admin-tab" data-tab="users">Users</button>
        <button class="admin-tab" data-tab="leagues">Leagues</button>
        <button class="admin-tab" data-tab="games">Games</button>
    </div>

    <div class="admin-content">
        <div id="admin-overview" class="admin-section active">
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Total Users</h3>
                    <p id="total-users">Loading...</p>
                </div>
                <div class="stat-card">
                    <h3>Active Leagues</h3>
                    <p id="active-leagues">Loading...</p>
                </div>
                <div class="stat-card">
                    <h3>Total Predictions</h3>
                    <p id="total-predictions">Loading...</p>
                </div>
            </div>
        </div>

        <div id="admin-users" class="admin-section">
            <div class="search-bar">
                <input type="text" id="user-search" placeholder="Search users...">
            </div>
            <div id="users-table"></div>
            <div class="pagination"></div>
        </div>

        <div id="admin-leagues" class="admin-section">
            <div class="search-bar">
                <input type="text" id="league-search" placeholder="Search leagues...">
            </div>
            <div id="leagues-table"></div>
            <div class="pagination"></div>
        </div>

        <div id="admin-games" class="admin-section">
            <div class="game-controls">
                <button id="add-game">Add Game</button>
                <button id="update-scores">Update Scores</button>
            </div>
            <div id="games-table"></div>
        </div>
    </div>
</div>
`;
}

async loadAdminStats() {
try {
const stats = await this.getAdminStats();
document.getElementById('total-users').textContent = stats.totalUsers;
document.getElementById('active-leagues').textContent = stats.activeLeagues;
document.getElementById('total-predictions').textContent = stats.totalPredictions;
} catch (error) {
console.error('Error loading admin stats:', error);
}
}

async getAdminStats() {
// Get users count
const usersSnapshot = await db.collection('users').count().get();
const totalUsers = usersSnapshot.data().count;

// Get leagues count
const leaguesSnapshot = await db.collection('leagues').count().get();
const activeLeagues = leaguesSnapshot.data().count;

// Get total predictions
const predictionsSnapshot = await db.collection('users').get();
let totalPredictions = 0;
predictionsSnapshot.forEach(doc => {
totalPredictions += Object.keys(doc.data().predictions || {}).length;
});

return {
totalUsers,
activeLeagues,
totalPredictions
};
}

async handleUserManagement(action, userId) {
try {
switch (action) {
case 'ban':
await db.collection('users').doc(userId).update({
banned: true,
banTimestamp: firebase.firestore.FieldValue.serverTimestamp()
});
break;

case 'unban':
await db.collection('users').doc(userId).update({
banned: false,
banTimestamp: null
});
break;

case 'delete':
// Delete user data
await db.collection('users').doc(userId).delete();

// Remove from leagues
const leaguesSnapshot = await db.collection('leagues')
.where('members', 'array-contains', userId)
.get();

const batch = db.batch();
leaguesSnapshot.forEach(doc => {
batch.update(doc.ref, {
members: firebase.firestore.FieldValue.arrayRemove(userId)
});
});

await batch.commit();
break;
}

showNotification(`User ${action} successful`, 'success');
} catch (error) {
console.error(`Error ${action} user:`, error);
showNotification(`Error ${action} user`, 'error');
}
}
}

// Initialize components
document.addEventListener('DOMContentLoaded', () => {
realtimeSync.initializeSync();

// Initialize admin panel if user is admin
const adminPanel = new AdminPanel();
});
// League Management and Competition Features
class LeagueManager {
constructor() {
this.currentLeague = null;
this.leagueMembers = new Map();
}

async createCompetition(leagueId, options) {
try {
const competition = {
name: options.name,
startDate: options.startDate,
endDate: options.endDate,
type: options.type, // 'weekly', 'season', 'custom'
prizes: options.prizes,
participants: [],
standings: {},
rules: options.rules,
status: 'upcoming',
createdAt: firebase.firestore.FieldValue.serverTimestamp(),
createdBy: authState.user.uid
};

const competitionRef = await db.collection('leagues')
.doc(leagueId)
.collection('competitions')
.add(competition);

await this.notifyLeagueMembers(leagueId, 'new_competition', {
competitionId: competitionRef.id,
competitionName: options.name
});

return competitionRef.id;
} catch (error) {
console.error('Error creating competition:', error);
throw error;
}
}

async updateLeagueStandings(leagueId) {
try {
const leagueRef = db.collection('leagues').doc(leagueId);
const membersSnapshot = await leagueRef.get();
const members = membersSnapshot.data().members;

const standings = {};

// Calculate standings for each member
for (const memberId of members) {
const userDoc = await db.collection('users').doc(memberId).get();
const predictions = userDoc.data().predictions || {};

standings[memberId] = {
userId: memberId,
name: userDoc.data().name,
points: 0,
correctPredictions: 0,
totalPredictions: Object.keys(predictions).length,
winStreak: 0,
currentStreak: 0
};

// Calculate points and streaks
let currentStreak = 0;
Object.entries(predictions).forEach(([gameId, prediction]) => {
if (gameResults[gameId]) {
if (gameResults[gameId].winner === prediction) {
standings[memberId].points += this.calculatePoints(gameId);
standings[memberId].correctPredictions++;
currentStreak++;
} else {
currentStreak = 0;
}

standings[memberId].currentStreak = currentStreak;
standings[memberId].winStreak = Math.max(
standings[memberId].winStreak,
currentStreak
);
}
});
}

// Update league standings
await leagueRef.update({ standings });

return standings;
} catch (error) {
console.error('Error updating standings:', error);
throw error;
}
}

calculatePoints(gameId) {
const game = gameResults[gameId];
let points = 10; // Base points

// Bonus points for correct spread prediction
if (game.spread && Math.abs(game.spread) === Math.abs(game.actualSpread)) {
points += 5;
}

// Bonus points for prime time games
if (game.isPrimeTime) {
points += 5;
}

// Bonus points for playoff games
if (game.isPlayoff) {
points += 10;
}

return points;
}

async getLeagueAnalytics(leagueId) {
try {
const leagueRef = db.collection('leagues').doc(leagueId);
const leagueDoc = await leagueRef.get();
const leagueData = leagueDoc.data();

const analytics = {
totalMembers: leagueData.members.length,
averageAccuracy: 0,
topPerformers: [],
recentActivity: [],
predictionsDistribution: {},
weeklyParticipation: {}
};

// Calculate detailed analytics
const membersData = await Promise.all(
leagueData.members.map(async (memberId) => {
const userDoc = await db.collection('users').doc(memberId).get();
const userData = userDoc.data();
return {
userId: memberId,
name: userData.name,
predictions: userData.predictions || {}
};
})
);

// Process member data
membersData.forEach(member => {
const userStats = this.calculateUserStats(member.predictions);
analytics.averageAccuracy += userStats.accuracy;

analytics.topPerformers.push({
userId: member.userId,
name: member.name,
accuracy: userStats.accuracy,
points: userStats.points
});
});

// Calculate averages and sort top performers
analytics.averageAccuracy /= membersData.length;
analytics.topPerformers.sort((a, b) => b.points - a.points);
analytics.topPerformers = analytics.topPerformers.slice(0, 5);

return analytics;
} catch (error) {
console.error('Error getting league analytics:', error);
throw error;
}
}
}

// Analytics and Statistics System
class AnalyticsSystem {
constructor() {
this.cache = new Map();
this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
}

async getUserStats(userId) {
const cacheKey = `user_stats_${userId}`;

if (this.cache.has(cacheKey)) {
const cached = this.cache.get(cacheKey);
if (cached.timestamp > Date.now() - this.cacheTimeout) {
return cached.data;
}
}

try {
const userDoc = await db.collection('users').doc(userId).get();
const userData = userDoc.data();
const predictions = userData.predictions || {};

const stats = {
totalPredictions: Object.keys(predictions).length,
correctPredictions: 0,
accuracy: 0,
favoriteTeams: this.calculateFavoriteTeams(predictions),
predictionsBreakdown: {
home: 0,
away: 0,
correct: 0,
incorrect: 0
},
streaks: {
current: 0,
longest: 0
},
weeklyPerformance: {},
pointsHistory: []
};

// Process predictions
Object.entries(predictions).forEach(([gameId, prediction]) => {
const game = gameResults[gameId];
if (!game) return;

const week = this.getGameWeek(gameId);
if (!stats.weeklyPerformance[week]) {
stats.weeklyPerformance[week] = {
correct: 0,
total: 0,
points: 0
};
}

stats.weeklyPerformance[week].total++;

if (game.winner === prediction) {
stats.correctPredictions++;
stats.weeklyPerformance[week].correct++;
stats.weeklyPerformance[week].points += this.calculatePoints(game);
}

if (prediction === game.homeTeam) {
stats.predictionsBreakdown.home++;
} else {
stats.predictionsBreakdown.away++;
}
});

// Calculate accuracy
stats.accuracy = stats.totalPredictions > 0
? (stats.correctPredictions / stats.totalPredictions) * 100
: 0;

// Cache results
this.cache.set(cacheKey, {
timestamp: Date.now(),
data: stats
});

return stats;
} catch (error) {
console.error('Error calculating user stats:', error);
throw error;
}
}

calculateFavoriteTeams(predictions) {
const teamPicks = {};

Object.values(predictions).forEach(prediction => {
teamPicks[prediction] = (teamPicks[prediction] || 0) + 1;
});

return Object.entries(teamPicks)
.sort(([, a], [, b]) => b - a)
.slice(0, 3)
.map(([teamId, count]) => ({
teamId,
name: teams[teamId].name,
count
}));
}

async getGlobalStats() {
const cacheKey = 'global_stats';

if (this.cache.has(cacheKey)) {
const cached = this.cache.get(cacheKey);
if (cached.timestamp > Date.now() - this.cacheTimeout) {
return cached.data;
}
}

try {
const usersSnapshot = await db.collection('users').get();
const stats = {
totalUsers: 0,
totalPredictions: 0,
averageAccuracy: 0,
popularTeams: {},
weeklyParticipation: {},
upsetPredictions: {}
};

usersSnapshot.forEach(doc => {
const userData = doc.data();
stats.totalUsers++;

const predictions = userData.predictions || {};
stats.totalPredictions += Object.keys(predictions).length;

// Process predictions for popular teams
Object.values(predictions).forEach(teamId => {
stats.popularTeams[teamId] = (stats.popularTeams[teamId] || 0) + 1;
});
});

// Calculate averages and sort popular teams
stats.averageAccuracy = stats.totalPredictions > 0
? (stats.correctPredictions / stats.totalPredictions) * 100
: 0;

stats.popularTeams = Object.entries(stats.popularTeams)
.sort(([, a], [, b]) => b - a)
.slice(0, 10)
.reduce((acc, [teamId, count]) => {
acc[teamId] = count;
return acc;
}, {});

// Cache results
this.cache.set(cacheKey, {
timestamp: Date.now(),
data: stats
});

return stats;
} catch (error) {
console.error('Error calculating global stats:', error);
throw error;
}
}
}

// Notification System
class NotificationSystem {
constructor() {
this.initialized = false;
this.notifications = [];
this.subscriptions = new Map();
}

async initialize() {
if (this.initialized) return;

try {
await this.requestNotificationPermission();
await this.initializeFirebaseMessaging();
this.subscribeToNotifications();
this.initialized = true;
} catch (error) {
console.error('Error initializing notification system:', error);
throw error;
}
}

async requestNotificationPermission() {
try {
const permission = await Notification.requestPermission();
return permission === 'granted';
} catch (error) {
console.error('Error requesting notification permission:', error);
return false;
}
}

async initializeFirebaseMessaging() {
try {
const messaging = firebase.messaging();
const token = await messaging.getToken();

await db.collection('users')
.doc(authState.user.uid)
.update({
notificationToken: token,
notificationSettings: {
predictions: true,
leagueUpdates: true,
gameReminders: true
}
});
} catch (error) {
console.error('Error initializing Firebase messaging:', error);
throw error;
}
}
}
// Achievement and Rewards System
class AchievementSystem {
constructor() {
this.achievements = {
FIRST_PREDICTION: {
id: 'FIRST_PREDICTION',
name: 'Rookie Predictor',
description: 'Made your first prediction',
points: 10,
icon: '🎯'
},
PERFECT_WEEK: {
id: 'PERFECT_WEEK',
name: 'Perfect Week',
description: 'Correctly predicted all games in a week',
points: 50,
icon: '🌟'
},
STREAK_MASTER: {
id: 'STREAK_MASTER',
name: 'Streak Master',
description: 'Maintained a 5-game prediction streak',
points: 30,
icon: '🔥'
},
UPSET_KING: {
id: 'UPSET_KING',
name: 'Upset King',
description: 'Correctly predicted 3 upset victories',
points: 40,
icon: '👑'
},
LEAGUE_CHAMPION: {
id: 'LEAGUE_CHAMPION',
name: 'League Champion',
description: 'Won a league competition',
points: 100,
icon: '🏆'
}
};
}

async checkAchievements(userId) {
try {
const userDoc = await db.collection('users').doc(userId).get();
const userData = userDoc.data();
const userAchievements = userData.achievements || {};
const predictions = userData.predictions || {};

const newAchievements = [];

// Check for first prediction
if (!userAchievements.FIRST_PREDICTION && Object.keys(predictions).length > 0) {
newAchievements.push(this.achievements.FIRST_PREDICTION);
}

// Check for perfect week
const weeklyPredictions = this.groupPredictionsByWeek(predictions);
Object.entries(weeklyPredictions).forEach(([week, weekPredictions]) => {
if (this.isPerfectWeek(weekPredictions) &&
!userAchievements[`PERFECT_WEEK_${week}`]) {
newAchievements.push({
...this.achievements.PERFECT_WEEK,
id: `PERFECT_WEEK_${week}`
});
}
});

// Check for streak achievements
const currentStreak = this.calculateStreak(predictions);
if (currentStreak >= 5 && !userAchievements.STREAK_MASTER) {
newAchievements.push(this.achievements.STREAK_MASTER);
}

// Award new achievements
if (newAchievements.length > 0) {
await this.awardAchievements(userId, newAchievements);
}

return newAchievements;
} catch (error) {
console.error('Error checking achievements:', error);
throw error;
}
}

async awardAchievements(userId, achievements) {
try {
const userRef = db.collection('users').doc(userId);

await db.runTransaction(async (transaction) => {
const userDoc = await transaction.get(userRef);
const userData = userDoc.data();
const userAchievements = userData.achievements || {};
const rewardPoints = achievements.reduce((total, ach) => total + ach.points, 0);

// Update achievements and points
achievements.forEach(achievement => {
userAchievements[achievement.id] = {
...achievement,
earnedAt: firebase.firestore.FieldValue.serverTimestamp()
};
});

transaction.update(userRef, {
achievements: userAchievements,
rewardPoints: (userData.rewardPoints || 0) + rewardPoints
});
});

// Notify user
achievements.forEach(achievement => {
this.notifyAchievement(userId, achievement);
});
} catch (error) {
console.error('Error awarding achievements:', error);
throw error;
}
}

async notifyAchievement(userId, achievement) {
const notification = {
type: 'achievement',
title: 'New Achievement Unlocked!',
message: `You've earned the ${achievement.name} achievement!`,
icon: achievement.icon,
points: achievement.points,
timestamp: firebase.firestore.FieldValue.serverTimestamp()
};

await notificationSystem.sendNotification(userId, notification);
}
}

// Rewards System
class RewardsSystem {
constructor() {
this.rewards = {
CUSTOM_BADGE: {
id: 'CUSTOM_BADGE',
name: 'Custom Profile Badge',
cost: 100,
type: 'cosmetic'
},
LEAGUE_CREATE: {
id: 'LEAGUE_CREATE',
name: 'Create Private League',
cost: 200,
type: 'feature'
},
PREMIUM_STATS: {
id: 'PREMIUM_STATS',
name: 'Advanced Statistics Access',
cost: 300,
type: 'feature'
}
};
}

async purchaseReward(userId, rewardId) {
try {
const reward = this.rewards[rewardId];
if (!reward) throw new Error('Invalid reward');

const userRef = db.collection('users').doc(userId);

await db.runTransaction(async (transaction) => {
const userDoc = await transaction.get(userRef);
const userData = userDoc.data();
const userPoints = userData.rewardPoints || 0;

if (userPoints < reward.cost) {
throw new Error('Insufficient points');
}

const userRewards = userData.rewards || {};
userRewards[rewardId] = {
...reward,
purchasedAt: firebase.firestore.FieldValue.serverTimestamp()
};

transaction.update(userRef, {
rewardPoints: userPoints - reward.cost,
rewards: userRewards
});
});

return true;
} catch (error) {
console.error('Error purchasing reward:', error);
throw error;
}
}
}

// Data Export/Import System
class DataManager {
async exportUserData(userId) {
try {
const userDoc = await db.collection('users').doc(userId).get();
const userData = userDoc.data();

const exportData = {
predictions: userData.predictions || {},
achievements: userData.achievements || {},
rewards: userData.rewards || {},
settings: userData.settings || {},
leagues: await this.getUserLeagues(userId),
stats: await analyticsSystem.getUserStats(userId),
exportDate: new Date().toISOString()
};

const blob = new Blob([JSON.stringify(exportData, null, 2)],
{ type: 'application/json' });
const url = URL.createObjectURL(blob);

const a = document.createElement('a');
a.href = url;
a.download = `nfl_predictions_${userId}_${new Date().toISOString()}.json`;
document.body.appendChild(a);
a.click();
document.body.removeChild(a);
URL.revokeObjectURL(url);

return true;
} catch (error) {
console.error('Error exporting user data:', error);
throw error;
}
}

async importUserData(userId, fileData) {
try {
const importData = JSON.parse(fileData);

// Validate import data
if (!this.validateImportData(importData)) {
throw new Error('Invalid import data format');
}

const userRef = db.collection('users').doc(userId);

await db.runTransaction(async (transaction) => {
const userDoc = await transaction.get(userRef);
const userData = userDoc.data();

// Merge predictions
const mergedPredictions = {
...userData.predictions,
...importData.predictions
};

// Merge settings
const mergedSettings = {
...userData.settings,
...importData.settings
};

transaction.update(userRef, {
predictions: mergedPredictions,
settings: mergedSettings
});
});

return true;
} catch (error) {
console.error('Error importing user data:', error);
throw error;
}
}

validateImportData(data) {
const requiredFields = ['predictions', 'settings'];
return requiredFields.every(field =>
data.hasOwnProperty(field) &&
typeof data[field] === 'object'
);
}
}

// Advanced Game Analysis
class GameAnalyzer {
async analyzeGame(gameId) {
try {
const game = gameResults[gameId];
if (!game) throw new Error('Game not found');

const analysis = {
gameId,
basicStats: await this.getBasicStats(gameId),
predictionDistribution: await this.getPredictionDistribution(gameId),
teamComparison: await this.getTeamComparison(game.homeTeam, game.awayTeam),
historicalMatchups: await this.getHistoricalMatchups(game.homeTeam, game.awayTeam),
weatherImpact: await this.getWeatherImpact(gameId),
keyFactors: []
};

// Analyze key factors
analysis.keyFactors = this.analyzeKeyFactors(analysis);

return analysis;
} catch (error) {
console.error('Error analyzing game:', error);
throw error;
}
}

async getBasicStats(gameId) {
// Implementation for basic game statistics
}

async getPredictionDistribution(gameId) {
// Implementation for prediction distribution analysis
}

async getTeamComparison(homeTeamId, awayTeamId) {
// Implementation for team comparison
}

async getHistoricalMatchups(homeTeamId, awayTeamId) {
// Implementation for historical matchups analysis
}

async getWeatherImpact(gameId) {
// Implementation for weather impact analysis
}

analyzeKeyFactors(analysis) {
// Implementation for key factors analysis
}
}

// Initialize all systems
document.addEventListener('DOMContentLoaded', () => {
const achievementSystem = new AchievementSystem();
const rewardsSystem = new RewardsSystem();
const dataManager = new DataManager();
const gameAnalyzer = new GameAnalyzer();

// Check for achievements periodically
setInterval(() => {
if (authState.isAuthenticated) {
achievementSystem.checkAchievements(authState.user.uid);
}
}, 300000); // Check every 5 minutes
});
// Advanced Visualization System
class DataVisualization {
constructor() {
this.chartColors = {
primary: '#4CAF50',
secondary: '#2196F3',
accent: '#FFC107',
error: '#F44336',
background: '#1E1E1E',
text: '#FFFFFF'
};
this.chartDefaults = this.initChartDefaults();
}

initChartDefaults() {
Chart.defaults.color = this.chartColors.text;
Chart.defaults.scale.grid.color = 'rgba(255, 255, 255, 0.1)';

return {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'bottom',
labels: {
color: this.chartColors.text
}
},
tooltip: {
backgroundColor: this.chartColors.background,
titleColor: this.chartColors.text,
bodyColor: this.chartColors.text,
borderColor: this.chartColors.primary,
borderWidth: 1
}
}
};
}

async createPredictionAccuracyChart(userId, timeRange = 'season') {
const stats = await analyticsSystem.getUserStats(userId);
const ctx = document.getElementById('prediction-accuracy-chart').getContext('2d');

const data = {
labels: Object.keys(stats.weeklyPerformance),
datasets: [{
label: 'Accuracy (%)',
data: Object.values(stats.weeklyPerformance).map(week =>
(week.correct / week.total) * 100
),
borderColor: this.chartColors.primary,
backgroundColor: `${this.chartColors.primary}50`,
fill: true,
tension: 0.4
}]
};

new Chart(ctx, {
type: 'line',
data: data,
options: {
...this.chartDefaults,
scales: {
y: {
beginAtZero: true,
max: 100,
title: {
display: true,
text: 'Accuracy %'
}
},
x: {
title: {
display: true,
text: 'Week'
}
}
}
}
});
}

createTeamPerformanceRadar(teamStats) {
const ctx = document.getElementById('team-performance-radar').getContext('2d');

const data = {
labels: [
'Offense', 'Defense', 'Special Teams',
'Home Performance', 'Away Performance', 'Consistency'
],
datasets: [{
label: teamStats.name,
data: [
teamStats.offense,
teamStats.defense,
teamStats.specialTeams,
teamStats.homePerformance,
teamStats.awayPerformance,
teamStats.consistency
],
backgroundColor: `${this.chartColors.primary}50`,
borderColor: this.chartColors.primary,
pointBackgroundColor: this.chartColors.accent
}]
};

new Chart(ctx, {
type: 'radar',
data: data,
options: {
...this.chartDefaults,
scales: {
r: {
min: 0,
max: 100,
ticks: {
stepSize: 20
}
}
}
}
});
}

createLeagueStandingsChart(leagueData) {
const ctx = document.getElementById('league-standings-chart').getContext('2d');

const sortedStandings = Object.entries(leagueData.standings)
.sort(([, a], [, b]) => b.points - a.points)
.slice(0, 10);

const data = {
labels: sortedStandings.map(([, user]) => user.name),
datasets: [{
label: 'Points',
data: sortedStandings.map(([, user]) => user.points),
backgroundColor: this.chartColors.primary,
borderColor: this.chartColors.secondary,
borderWidth: 1
}]
};

new Chart(ctx, {
type: 'bar',
data: data,
options: {
...this.chartDefaults,
indexAxis: 'y'
}
});
}
}

// Mobile Responsiveness and PWA Features
class PWAManager {
constructor() {
this.initServiceWorker();
this.setupResponsiveUI();
}

async initServiceWorker() {
if ('serviceWorker' in navigator) {
try {
const registration = await navigator.serviceWorker.register('/sw.js');
console.log('ServiceWorker registered:', registration);
} catch (error) {
console.error('ServiceWorker registration failed:', error);
}
}
}

setupResponsiveUI() {
const mediaQuery = window.matchMedia('(max-width: 768px)');
this.handleResponsiveLayout(mediaQuery.matches);
mediaQuery.addListener((e) => this.handleResponsiveLayout(e.matches));
}

handleResponsiveLayout(isMobile) {
const root = document.documentElement;

if (isMobile) {
root.style.setProperty('--grid-columns', '1');
root.style.setProperty('--card-width', '100%');
this.enableSwipeNavigation();
} else {
root.style.setProperty('--grid-columns', '3');
root.style.setProperty('--card-width', '300px');
this.disableSwipeNavigation();
}
}

enableSwipeNavigation() {
let touchStartX = 0;
let touchEndX = 0;

document.addEventListener('touchstart', e => {
touchStartX = e.changedTouches[0].screenX;
});

document.addEventListener('touchend', e => {
touchEndX = e.changedTouches[0].screenX;
this.handleSwipe();
});
}

handleSwipe() {
const swipeThreshold = 50;
const diff = touchEndX - touchStartX;

if (Math.abs(diff) < swipeThreshold) return;

if (diff > 0) {
// Swipe right - go back
window.history.back();
} else {
// Swipe left - go forward
window.history.forward();
}
}
}

// NFL Data Integration
class NFLDataIntegration {
constructor() {
this.API_KEY = 'YOUR_API_KEY'; // Replace with actual API key
this.BASE_URL = 'https://api.nfl.com/v1';
this.cache = new Map();
this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
}

async fetchGameData(gameId) {
const cacheKey = `game_${gameId}`;

if (this.cache.has(cacheKey)) {
const cached = this.cache.get(cacheKey);
if (cached.timestamp > Date.now() - this.cacheTimeout) {
return cached.data;
}
}

try {
const response = await fetch(`${this.BASE_URL}/games/${gameId}`, {
headers: {
'Authorization': `Bearer ${this.API_KEY}`,
'Content-Type': 'application/json'
}
});

if (!response.ok) throw new Error('NFL API request failed');

const data = await response.json();

this.cache.set(cacheKey, {
timestamp: Date.now(),
data
});

return data;
} catch (error) {
console.error('Error fetching NFL game data:', error);
throw error;
}
}

async getTeamStats(teamId) {
const cacheKey = `team_${teamId}`;

if (this.cache.has(cacheKey)) {
const cached = this.cache.get(cacheKey);
if (cached.timestamp > Date.now() - this.cacheTimeout) {
return cached.data;
}
}

try {
const response = await fetch(`${this.BASE_URL}/teams/${teamId}/stats`, {
headers: {
'Authorization': `Bearer ${this.API_KEY}`,
'Content-Type': 'application/json'
}
});

if (!response.ok) throw new Error('NFL API request failed');

const data = await response.json();

this.cache.set(cacheKey, {
timestamp: Date.now(),
data
});

return data;
} catch (error) {
console.error('Error fetching team stats:', error);
throw error;
}
}
}

// Prediction Algorithm and Machine Learning
class PredictionEngine {
constructor() {
this.model = null;
this.features = [
'home_team_win_rate',
'away_team_win_rate',
'home_team_points_avg',
'away_team_points_avg',
'home_team_defense_rating',
'away_team_defense_rating',
'weather_condition',
'injury_impact',
'historical_matchup'
];
}

async initializeModel() {
try {
this.model = await tf.loadLayersModel('/models/prediction_model/model.json');
} catch (error) {
console.error('Error loading prediction model:', error);
throw error;
}
}

async predictGame(gameData) {
if (!this.model) await this.initializeModel();

const features = await this.prepareFeatures(gameData);
const tensor = tf.tensor2d([features]);

const prediction = this.model.predict(tensor);
const probabilities = await prediction.data();

tensor.dispose();
prediction.dispose();

return {
homeTeamWinProbability: probabilities[0],
awayTeamWinProbability: probabilities[1],
confidence: Math.max(probabilities[0], probabilities[1])
};
}

async prepareFeatures(gameData) {
const homeTeamStats = await nflDataIntegration.getTeamStats(gameData.homeTeam);
const awayTeamStats = await nflDataIntegration.getTeamStats(gameData.awayTeam);

return [
homeTeamStats.winRate,
awayTeamStats.winRate,
homeTeamStats.pointsPerGame,
awayTeamStats.pointsPerGame,
homeTeamStats.defenseRating,
awayTeamStats.defenseRating,
this.normalizeWeatherCondition(gameData.weather),
this.calculateInjuryImpact(gameData.injuries),
this.calculateHistoricalMatchupFactor(gameData.homeTeam, gameData.awayTeam)
];
}

normalizeWeatherCondition(weather) {
// Implement weather normalization logic
return 0.5;
}

calculateInjuryImpact(injuries) {
// Implement injury impact calculation
return 0.5;
}

calculateHistoricalMatchupFactor(homeTeam, awayTeam) {
// Implement historical matchup factor calculation
return 0.5;
}

async trainModel(trainingData) {
const model = tf.sequential({
layers: [
tf.layers.dense({ inputShape: [this.features.length], units: 64, activation: 'relu' }),
tf.layers.dropout({ rate: 0.2 }),
tf.layers.dense({ units: 32, activation: 'relu' }),
tf.layers.dropout({ rate: 0.2 }),
tf.layers.dense({ units: 2, activation: 'softmax' })
]
});

model.compile({
optimizer: tf.train.adam(0.001),
loss: 'binaryCrossentropy',
metrics: ['accuracy']
});

// Prepare training data
const { inputs, labels } = this.prepareTrainingData(trainingData);

// Train the model
await model.fit(inputs, labels, {
epochs: 50,
batchSize: 32,
validationSplit: 0.2,
callbacks: {
onEpochEnd: (epoch, logs) => {
console.log(`Epoch ${epoch}: loss = ${logs.loss.toFixed(4)}, accuracy = ${logs.acc.toFixed(4)}`);
}
}
});

this.model = model;
await this.saveModel();
}

async saveModel() {
try {
await this.model.save('indexeddb://prediction_model');
console.log('Model saved successfully');
} catch (error) {
console.error('Error saving model:', error);
throw error;
}
}
}

// Initialize all systems
document.addEventListener('DOMContentLoaded', () => {
const visualization = new DataVisualization();
const pwaManager = new PWAManager();
const nflDataIntegration = new NFLDataIntegration();
const predictionEngine = new PredictionEngine();

// Initialize systems
predictionEngine.initializeModel();

// Set up real-time updates
setInterval(() => {
if (authState.isAuthenticated) {
nflDataIntegration.updateGameData();
visualization.updateCharts();
}
}, 60000); // Update every minute
});
// Enhanced Data Visualization System
class AdvancedVisualization {
constructor() {
this.dashboards = new Map();
this.activeCharts = new Set();
this.themeColors = {
...this.chartColors,
gradients: {
success: ['#4CAF50', '#81C784'],
danger: ['#F44336', '#E57373'],
info: ['#2196F3', '#64B5F6'],
warning: ['#FFC107', '#FFD54F']
}
};
}

createInteractiveDashboard(containerId, config) {
const container = document.getElementById(containerId);
const dashboard = new Dashboard(container, config);
this.dashboards.set(containerId, dashboard);
return dashboard;
}

// Advanced Heatmap for Prediction Accuracy
createPredictionHeatmap(data) {
const ctx = document.getElementById('prediction-heatmap').getContext('2d');

const weeks = Array.from({ length: 18 }, (_, i) => `Week ${i + 1}`);
const teams = Object.keys(data.teamAccuracy);

const heatmapData = teams.map(team =>
weeks.map(week => data.teamAccuracy[team][week] || 0)
);

const chart = new Chart(ctx, {
type: 'matrix',
data: {
datasets: [{
data: this.processHeatmapData(heatmapData, teams, weeks),
backgroundColor(context) {
const value = context.dataset.data[context.dataIndex].v;
const alpha = value / 100;
return `rgba(76, 175, 80, ${alpha})`;
},
borderWidth: 1,
borderColor: '#1E1E1E',
width: ({ chart }) => (chart.chartArea.width / weeks.length) - 1,
height: ({ chart }) => (chart.chartArea.height / teams.length) - 1
}]
},
options: {
...this.chartDefaults,
plugins: {
tooltip: {
callbacks: {
title() {
return '';
},
label(context) {
const { team, week, v } = context.dataset.data[context.dataIndex];
return [`Team: ${team}`, `Week: ${week}`, `Accuracy: ${v.toFixed(1)}%`];
}
}
},
legend: {
display: false
}
},
scales: {
x: {
type: 'category',
labels: weeks,
ticks: {
font: {
size: 10
}
}
},
y: {
type: 'category',
labels: teams,
offset: true,
ticks: {
font: {
size: 10
}
}
}
}
}
});

this.activeCharts.add(chart);
}

// Interactive Bubble Chart for League Analysis
createLeagueBubbleChart(leagueData) {
const ctx = document.getElementById('league-bubble-chart').getContext('2d');

const data = leagueData.members.map(member => ({
x: member.totalPredictions,
y: member.accuracy,
r: Math.sqrt(member.points) / 2,
memberId: member.id,
name: member.name
}));

const chart = new Chart(ctx, {
type: 'bubble',
data: {
datasets: [{
label: 'League Members',
data: data,
backgroundColor: data.map(d =>
`rgba(33, 150, 243, ${d.y / 100})`
)
}]
},
options: {
...this.chartDefaults,
scales: {
x: {
title: {
display: true,
text: 'Total Predictions'
}
},
y: {
title: {
display: true,
text: 'Accuracy %'
},
min: 0,
max: 100
}
},
plugins: {
tooltip: {
callbacks: {
label(context) {
const d = context.raw;
return [
`Member: ${d.name}`,
`Predictions: ${d.x}`,
`Accuracy: ${d.y.toFixed(1)}%`,
`Points: ${Math.pow(d.r * 2, 2)}`
];
}
}
}
}
}
});

this.activeCharts.add(chart);
}

// Sunburst Chart for Team Performance Breakdown
createTeamSunburstChart(teamData) {
const container = document.getElementById('team-sunburst');

const data = {
name: 'Team Performance',
children: [
{
name: 'Offense',
children: [
{ name: 'Passing', value: teamData.offense.passing },
{ name: 'Rushing', value: teamData.offense.rushing },
{ name: 'Scoring', value: teamData.offense.scoring }
]
},
{
name: 'Defense',
children: [
{ name: 'Run Defense', value: teamData.defense.runDef },
{ name: 'Pass Defense', value: teamData.defense.passDef },
{ name: 'Turnovers', value: teamData.defense.turnovers }
]
},
{
name: 'Special Teams',
children: [
{ name: 'Kicking', value: teamData.specialTeams.kicking },
{ name: 'Returns', value: teamData.specialTeams.returns },
{ name: 'Coverage', value: teamData.specialTeams.coverage }
]
}
]
};

const width = container.clientWidth;
const height = container.clientHeight;
const radius = Math.min(width, height) / 2;

const color = d3.scaleOrdinal()
.domain(['Offense', 'Defense', 'Special Teams'])
.range(Object.values(this.themeColors.gradients));

const partition = data => d3.partition()
.size([2 * Math.PI, radius])
(d3.hierarchy(data)
.sum(d => d.value)
.sort((a, b) => b.value - a.value));

const arc = d3.arc()
.startAngle(d => d.x0)
.endAngle(d => d.x1)
.padAngle(d => Math.min((d.x1 - d.x0) / 2, 0.005))
.padRadius(radius / 2)
.innerRadius(d => d.y0)
.outerRadius(d => d.y1 - 1);

const svg = d3.select(container)
.append('svg')
.attr('viewBox', `-${width / 2} -${height / 2} ${width} ${height}`);

const root = partition(data);

svg.selectAll('path')
.data(root.descendants())
.join('path')
.attr('fill', d => {
while (d.depth > 1) d = d.parent;
return color(d.data.name);
})
.attr('d', arc)
.append('title')
.text(d => `${d.ancestors().map(d => d.data.name).reverse().join('/')}\n${d.value.toFixed(2)}`);
}

// Sankey Diagram for Prediction Flow Analysis
createPredictionSankeyDiagram(predictionData) {
const container = document.getElementById('prediction-sankey');
const margin = { top: 10, right: 10, bottom: 10, left: 10 };
const width = container.clientWidth - margin.left - margin.right;
const height = container.clientHeight - margin.top - margin.bottom;

const svg = d3.select(container)
.append('svg')
.attr('width', width + margin.left + margin.right)
.attr('height', height + margin.top + margin.bottom)
.append('g')
.attr('transform', `translate(${margin.left},${margin.top})`);

const sankey = d3.sankey()
.nodeWidth(15)
.nodePadding(10)
.extent([[1, 1], [width - 1, height - 6]]);

const { nodes, links } = sankey({
nodes: predictionData.nodes.map(d => Object.assign({}, d)),
links: predictionData.links.map(d => Object.assign({}, d))
});

svg.append('g')
.selectAll('rect')
.data(nodes)
.join('rect')
.attr('x', d => d.x0)
.attr('y', d => d.y0)
.attr('height', d => d.y1 - d.y0)
.attr('width', d => d.x1 - d.x0)
.attr('fill', d => this.themeColors.primary)
.append('title')
.text(d => `${d.name}\n${d.value}`);

svg.append('g')
.attr('fill', 'none')
.selectAll('g')
.data(links)
.join('path')
.attr('d', d3.sankeyLinkHorizontal())
.attr('stroke', d => this.themeColors.secondary)
.attr('stroke-width', d => Math.max(1, d.width))
.attr('opacity', 0.5)
.style('mix-blend-mode', 'multiply')
.append('title')
.text(d => `${d.source.name} → ${d.target.name}\n${d.value}`);
}

// Interactive Scatter Plot Matrix
createScatterPlotMatrix(data, variables) {
const container = document.getElementById('scatter-matrix');
const padding = 20;
const size = 150;
const total = variables.length;
const width = size * total + padding * (total + 1);
const height = width;

const svg = d3.select(container)
.append('svg')
.attr('width', width)
.attr('height', height)
.append('g')
.attr('transform', `translate(${padding},${padding})`);

variables.forEach((varY, i) => {
variables.forEach((varX, j) => {
const cell = svg.append('g')
.attr('transform', `translate(${j * (size + padding)},${i * (size + padding)})`);

if (i === j) {
// Diagonal: Show distribution
const values = data.map(d => d[varX]);
const histogram = d3.histogram()(values);

const y = d3.scaleLinear()
.domain([0, d3.max(histogram, d => d.length)])
.range([size, 0]);

const x = d3.scaleLinear()
.domain(d3.extent(values))
.range([0, size]);

cell.selectAll('rect')
.data(histogram)
.join('rect')
.attr('x', d => x(d.x0))
.attr('y', d => y(d.length))
.attr('width', d => x(d.x1) - x(d.x0))
.attr('height', d => size - y(d.length))
.attr('fill', this.themeColors.primary);
} else {
// Scatter plot
const x = d3.scaleLinear()
.domain(d3.extent(data, d => d[varX]))
.range([0, size]);

const y = d3.scaleLinear()
.domain(d3.extent(data, d => d[varY]))
.range([size, 0]);

cell.selectAll('circle')
.data(data)
.join('circle')
.attr('cx', d => x(d[varX]))
.attr('cy', d => y(d[varY]))
.attr('r', 3)
.attr('fill', this.themeColors.secondary)
.attr('opacity', 0.5);
}

// Add axis labels
if (i === total - 1) {
cell.append('text')
.attr('y', size + padding / 2)
.attr('x', size / 2)
.style('text-anchor', 'middle')
.text(varX);
}
if (j === 0) {
cell.append('text')
.attr('transform', 'rotate(-90)')
.attr('y', -padding / 2)
.attr('x', -size / 2)
.style('text-anchor', 'middle')
.text(varY);
}
});
});
}
}

// Interactive Dashboard Management
class Dashboard {
constructor(container, config) {
this.container = container;
this.config = config;
this.widgets = new Map();
this.init();
}

init() {
this.setupGrid();
this.createWidgets();
this.setupInteractions();
}

setupGrid() {
this.container.style.display = 'grid';
this.container.style.gridTemplateColumns = `repeat(${this.config.columns}, 1fr)`;
this.container.style.gap = '1rem';
this.container.style.padding = '1rem';
}

createWidgets() {
this.config.widgets.forEach(widget => {
const widgetElement = this.createWidget(widget);
this.widgets.set(widget.id, widgetElement);
});
}

createWidget(config) {
const widget = document.createElement('div');
widget.className = 'dashboard-widget';
widget.style.gridColumn = `span ${config.width || 1}`;
widget.style.gridRow = `span ${config.height || 1}`;

const header = document.createElement('div');
header.className = 'widget-header';
header.innerHTML = `
<h3>${config.title}</h3>
<div class="widget-controls">
    <button class="refresh-btn">↻</button>
    <button class="maximize-btn">⛶</button>
</div>
`;

const content = document.createElement('div');
content.className = 'widget-content';
content.id = `widget-content-${config.id}`;

widget.appendChild(header);
widget.appendChild(content);
this.container.appendChild(widget);

this.initializeWidgetContent(config, content);
return widget;
}

initializeWidgetContent(config, container) {
switch (config.type) {
case 'chart':
this.createChart(config, container);
break;
case 'table':
this.createTable(config, container);
break;
case 'metrics':
this.createMetrics(config, container);
break;
case 'custom':
config.render(container);
break;
}
}

setupInteractions() {
this.container.addEventListener('click', (e) => {
const refreshBtn = e.target.closest('.refresh-btn');
const maximizeBtn = e.target.closest('.maximize-btn');

if (refreshBtn) {
const widget = refreshBtn.closest('.dashboard-widget');
this.refreshWidget(widget);
} else if (maximizeBtn) {
const widget = maximizeBtn.closest('.dashboard-widget');
this.toggleWidgetSize(widget);
}
});
}

async refreshWidget(widget) {
const widgetId = Array.from(this.widgets.entries())
.find(([, element]) => element === widget)[0];
const config = this.config.widgets.find(w => w.id === widgetId);

const content = widget.querySelector('.widget-content');
content.innerHTML = '<div class="loading">Refreshing...</div>';

await this.initializeWidgetContent(config, content);
}

toggleWidgetSize(widget) {
if (widget.classList.contains('maximized')) {
widget.classList.remove('maximized');
widget.style.gridColumn = widget.dataset.originalColumn;
widget.style.gridRow = widget.dataset.originalRow;
} else {
widget.dataset.originalColumn = widget.style.gridColumn;
widget.dataset.originalRow = widget.style.gridRow;
widget.classList.add('maximized');
widget.style.gridColumn = `1 / -1`;
widget.style.gridRow = `span 2`;
}
}
}

// Initialize visualization system
const advancedViz = new AdvancedVisualization();

// Create interactive dashboard
const dashboardConfig = {
columns: 4,
widgets: [
{
id: 'prediction-accuracy',
title: 'Prediction Accuracy Trends',
type: 'chart',
width: 2,
height: 1,
chartType: 'line'
},
{
id: 'team-performance',
title: 'Team Performance Analysis',
type: 'chart',
width: 2,
height: 1,
chartType: 'radar'
},
{
id: 'league-standings',
title: 'League Standings',
type: 'chart',
width: 2,
height: 2,
chartType: 'bar'
},
{
id: 'prediction-flow',
title: 'Prediction Flow Analysis',
type: 'custom',
width: 2,
height: 2,
render: (container) => advancedViz.createPredictionSankeyDiagram(predictionData)
}
]
};

document.addEventListener('DOMContentLoaded', () => {
const dashboard = advancedViz.createInteractiveDashboard('main-dashboard', dashboardConfig);
});
    </script>