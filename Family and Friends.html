<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>For Spouses & Partners: How to Help When Your Partner is Addicted | Williamsville Wellness</title>
    <meta name="description" content="Learn how to help your addicted spouse or partner while protecting yourself. Expert guidance on boundaries, communication, and family support.">
    <meta name="keywords" content="how to help addicted spouse, supporting partner addiction, spouse addiction help, partner addiction recovery, family addiction support, addiction boundaries, enabling vs helping")
    <link rel="preconnect" href="https://fonts.googleapis.com"/>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
<style>
    .cta-section p a {
        color: var(--accent-light);
        font-weight: 600;
        text-decoration: underline;
    }

    .cta-section p a:hover {
        color: var(--white);
    }
    .hero a {
        color: var(--accent-light);
        font-weight: 600; /* Makes the link slightly bolder */
        text-decoration: underline; /* Adds an underline for clarity */
    }

    .hero a:hover {
        color: var(--white);
    }
    :root {
        --primary: #0a4d2a;
        --primary-light: #0f5e32;
        --primary-dark: #063d21;
        --accent: #d4a574;
        --accent-dark: #b8935f;
        --accent-light: #e8c49a;
        --gold: #f4d03f;
        --emerald: #2ecc71;
        --sage: #95a5a6;
        --cream: #fdfcf8;
        --pearl: #f8f9fa;
        --shadow: #1a1a1a;
        --text-primary: #1a1a1a;
        --text-secondary: #4a5568;
        --text-light: #718096;
        --white: #ffffff;
        --glass-bg: rgba(255, 255, 255, 0.25);
        --glass-border: rgba(255, 255, 255, 0.18);
        --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        --backdrop-blur: blur(16px);
        --radius-xs: 6px;
        --radius-sm: 12px;
        --radius-md: 20px;
        --radius-lg: 32px;
        --radius-xl: 48px;
        --shadow-xs: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
        --shadow-sm: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
        --shadow-md: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
        --shadow-lg: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
        --shadow-xl: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);
        --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
        --gradient-accent: linear-gradient(135deg, var(--accent) 0%, var(--accent-dark) 50%, var(--gold) 100%);
        --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
        --gradient-hero: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 50%, var(--primary-light) 100%);
        --gradient-subtle: linear-gradient(135deg, var(--cream) 0%, var(--pearl) 100%);
        --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        --transition-spring: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    /* Enhanced Base Styles */
    *, *::before, *::after {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    html {
        scroll-behavior: smooth;
        font-size: 16px;
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: clamp(1rem, 1.2vw, 1.125rem);
        line-height: 1.75;
        color: var(--text-primary);
        font-weight: 400;
        background:
                radial-gradient(circle at 25% 25%, rgba(10, 77, 42, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(212, 165, 116, 0.04) 0%, transparent 50%),
                radial-gradient(circle at 50% 0%, rgba(46, 204, 113, 0.02) 0%, transparent 50%),
                var(--gradient-subtle);
        min-height: 100vh;
        overflow-x: hidden;
        position: relative;
    }

    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
                radial-gradient(circle at 20% 80%, rgba(212, 165, 116, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(10, 77, 42, 0.06) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(46, 204, 113, 0.04) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
        animation: backgroundFloat 20s ease-in-out infinite;
    }

    @keyframes backgroundFloat {
        0%, 100% {
            transform: scale(1) rotate(0deg);
            opacity: 0.5;
        }
        33% {
            transform: scale(1.1) rotate(1deg);
            opacity: 0.7;
        }
        66% {
            transform: scale(0.9) rotate(-1deg);
            opacity: 0.6;
        }
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: 'Plus Jakarta Sans', sans-serif;
        font-weight: 700;
        line-height: 1.25;
        color: var(--primary);
        margin-bottom: 1rem;
        letter-spacing: -0.025em;
    }

    h1 { font-size: clamp(2.5rem, 5vw, 4rem); }
    h2 { font-size: clamp(2rem, 4vw, 3rem); }
    h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
    h4 { font-size: clamp(1.25rem, 2.5vw, 1.5rem); }

    p {
        margin-bottom: 1.25rem;
        color: var(--text-secondary);
    }

    a {
        color: var(--primary);
        text-decoration: none;
        transition: var(--transition-smooth);
        position: relative;
    }

    a:hover {
        color: var(--accent);
    }

    .container {
        width: min(1400px, 92%);
        margin-inline: auto;
        position: relative;
    }

    /* Enhanced Hero Section */
    .hero {
        background: var(--gradient-hero);
        color: var(--white);
        text-align: center;
        padding: clamp(3rem, 8vw, 8rem) clamp(2rem, 5vw, 4rem);
        max-width: 1600px;
        margin: clamp(2rem, 5vw, 4rem) auto;
        border-radius: var(--radius-xl);
        font-family: 'Plus Jakarta Sans', sans-serif;
        box-shadow: var(--shadow-xl);
        position: relative;
        overflow: hidden;
        backdrop-filter: var(--backdrop-blur);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .hero::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background:
                radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%),
                linear-gradient(45deg, transparent 30%, rgba(212, 165, 116, 0.1) 50%, transparent 70%);
        animation: heroFloat 12s ease-in-out infinite;
        z-index: 1;
    }

    .hero::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
                radial-gradient(circle at 70% 30%, rgba(46, 204, 113, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 30% 70%, rgba(212, 165, 116, 0.08) 0%, transparent 50%);
        animation: heroFloat 15s ease-in-out infinite reverse;
        z-index: 1;
    }

    @keyframes heroFloat {
        0%, 100% {
            transform: translate(-50%, -50%) rotate(0deg) scale(1);
            opacity: 0.8;
        }
        33% {
            transform: translate(-45%, -55%) rotate(120deg) scale(1.1);
            opacity: 1;
        }
        66% {
            transform: translate(-55%, -45%) rotate(240deg) scale(0.9);
            opacity: 0.9;
        }
    }

    .hero-content {
        position: relative;
        z-index: 10;
    }

    .hero h1 {
        font-weight: 800;
        margin-bottom: 1rem;
        color: var(--white);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: titleGlow 3s ease-in-out infinite alternate;
    }

    @keyframes titleGlow {
        0% {
            filter: drop-shadow(0 0 10px rgba(212, 165, 116, 0.3));
        }
        100% {
            filter: drop-shadow(0 0 20px rgba(212, 165, 116, 0.6));
        }
    }

    .hero-underline {
        width: 80px;
        height: 4px;
        background: var(--gradient-accent);
        margin: 0 auto 2rem;
        border-radius: var(--radius-xs);
        box-shadow: 0 4px 15px rgba(212, 165, 116, 0.4);
        animation: underlinePulse 2s ease-in-out infinite alternate;
    }

    @keyframes underlinePulse {
        0% {
            transform: scaleX(1);
            box-shadow: 0 4px 15px rgba(212, 165, 116, 0.4);
        }
        100% {
            transform: scaleX(1.2);
            box-shadow: 0 6px 25px rgba(212, 165, 116, 0.7);
        }
    }

    .hero p {
        font-size: clamp(1.1rem, 2vw, 1.4rem);
        max-width: 800px;
        margin: 0 auto 3rem;
        line-height: 1.6;
        color: rgba(255, 255, 255, 0.95);
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        font-weight: 400;
    }

    /* Enhanced Navigation Pills */
    .nav-section {
        margin: clamp(3rem, 6vw, 6rem) 0;
    }

    .nav-pills {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        justify-content: center;
        margin-bottom: 4rem;
        padding: 3rem;
        background: var(--glass-bg);
        backdrop-filter: var(--backdrop-blur);
        border: 1px solid var(--glass-border);
        border-radius: var(--radius-lg);
        box-shadow: var(--glass-shadow);
        position: relative;
        overflow: hidden;
    }

    .nav-pills::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--gradient-glass);
        opacity: 0.5;
        z-index: 1;
    }

    .nav-pill {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: var(--backdrop-blur);
        border: 2px solid rgba(10, 77, 42, 0.1);
        border-radius: 50px;
        padding: 1rem 2rem;
        color: var(--primary);
        font-weight: 600;
        font-size: 0.95rem;
        text-decoration: none;
        transition: var(--transition-bounce);
        box-shadow: var(--shadow-sm);
        position: relative;
        overflow: hidden;
        transform: translateY(0) scale(1);
        z-index: 2;
        letter-spacing: 0.5px;
    }

    .nav-pill::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--gradient-primary);
        transition: var(--transition-spring);
        z-index: -1;
    }

    .nav-pill::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: var(--transition-smooth);
        z-index: -1;
    }

    .nav-pill:hover {
        color: var(--white) !important;
        border-color: var(--primary);
        transform: translateY(-8px) scale(1.08);
        box-shadow: var(--shadow-lg);
    }

    .nav-pill:hover::before {
        left: 0;
    }

    .nav-pill:hover::after {
        width: 150px;
        height: 150px;
    }

    .nav-pill:active {
        transform: translateY(-4px) scale(1.04);
    }

    /* Enhanced Help Cards */
    .help-section {
        margin: clamp(3rem, 6vw, 6rem) 0;
    }

    .help-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 3rem;
        margin-bottom: 5rem;
    }

    /* Two cards side by side on tablet and larger screens, hide extra cards */
    @media (min-width: 800px) {
        .help-grid {
            grid-template-columns: 1fr 1fr;
            max-width: none;
        }

        .help-grid .help-card:nth-child(n+3) {
            display: none;
        }
    }

    .help-card {
        background: var(--glass-bg);
        backdrop-filter: var(--backdrop-blur);
        border-radius: var(--radius-lg);
        padding: 3.5rem;
        box-shadow: var(--glass-shadow);
        border: 1px solid var(--glass-border);
        transition: var(--transition-spring);
        position: relative;
        overflow: hidden;
        transform: translateY(0) scale(1);
    }

    .help-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 6px;
        background: var(--gradient-accent);
        box-shadow: 0 4px 15px rgba(212, 165, 116, 0.4);
    }

    .help-card::after {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background:
                radial-gradient(circle, rgba(212, 165, 116, 0.04) 0%, transparent 50%),
                radial-gradient(circle, rgba(10, 77, 42, 0.02) 0%, transparent 70%);
        transition: var(--transition-smooth);
        z-index: 1;
    }

    .help-card:hover {
        transform: translateY(-20px) scale(1.03);
        box-shadow:
                var(--shadow-xl),
                0 0 0 1px rgba(10, 77, 42, 0.1),
                0 1px 0px rgba(255, 255, 255, 0.8) inset;
    }

    .help-card:hover::after {
        transform: rotate(180deg);
    }

    .help-card h3 {
        color: var(--primary);
        font-size: 1.6rem;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 1.5rem;
        position: relative;
        z-index: 2;
        font-weight: 700;
    }

    .help-card-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.95);
        border: 3px solid var(--accent);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--accent);
        font-size: 1.8rem;
        box-shadow: var(--shadow-md);
        position: relative;
        transition: var(--transition-bounce);
    }

    .help-card-icon::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: radial-gradient(circle, rgba(212, 165, 116, 0.3) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: var(--transition-smooth);
    }

    .help-card:hover .help-card-icon {
        transform: scale(1.15) rotate(10deg);
        box-shadow: var(--shadow-lg);
    }

    .help-card:hover .help-card-icon::before {
        width: 120px;
        height: 120px;
    }

    .help-card p {
        margin-bottom: 1.2rem;
        line-height: 1.7;
        position: relative;
        z-index: 2;
        color: var(--text-secondary);
    }

    .help-card strong {
        color: var(--primary);
        font-weight: 600;
    }

    .help-card a {
        color: var(--accent);
        font-weight: 600;
        text-decoration: none;
        position: relative;
        transition: var(--transition-smooth);
    }

    .help-card a::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 0;
        height: 3px;
        background: var(--gradient-accent);
        transition: var(--transition-smooth);
        border-radius: var(--radius-xs);
    }

    .help-card a:hover {
        color: var(--primary);
        transform: translateY(-2px);
    }

    .help-card a:hover::after {
        width: 100%;
    }

    /* Enhanced Content Sections */
    .content-section {
        background: var(--glass-bg);
        backdrop-filter: var(--backdrop-blur);
        margin: clamp(4rem, 8vw, 8rem) 0;
        padding: clamp(3rem, 6vw, 6rem);
        border-radius: var(--radius-xl);
        box-shadow: var(--glass-shadow);
        border: 1px solid var(--glass-border);
        position: relative;
        overflow: hidden;
    }

    .content-section::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 400px;
        height: 400px;
        background:
                radial-gradient(circle, rgba(212, 165, 116, 0.06) 0%, rgba(212, 165, 116, 0.02) 40%, transparent 70%);
        border-radius: 50%;
        transform: translate(40%, -40%);
        z-index: 1;
        animation: sectionFloat 10s ease-in-out infinite;
    }

    .content-section::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 300px;
        height: 300px;
        background:
                radial-gradient(circle, rgba(10, 77, 42, 0.04) 0%, rgba(10, 77, 42, 0.01) 40%, transparent 70%);
        border-radius: 50%;
        transform: translate(-40%, 40%);
        z-index: 1;
        animation: sectionFloat 12s ease-in-out infinite reverse;
    }

    @keyframes sectionFloat {
        0%, 100% {
            transform: translate(40%, -40%) scale(1);
            opacity: 0.6;
        }
        50% {
            transform: translate(50%, -30%) scale(1.2);
            opacity: 0.8;
        }
    }

    .section-title {
        font-size: clamp(2.2rem, 4vw, 3.2rem);
        color: var(--primary);
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 2;
        text-align: center;
        font-weight: 800;
        letter-spacing: -0.02em;
    }

    .section-underline {
        width: 100px;
        height: 5px;
        background: var(--gradient-accent);
        margin: 0 auto 3rem;
        border-radius: var(--radius-xs);
        box-shadow: 0 4px 15px rgba(212, 165, 116, 0.4);
        position: relative;
        z-index: 2;
        animation: underlineGlow 3s ease-in-out infinite alternate;
    }

    @keyframes underlineGlow {
        0% {
            box-shadow: 0 4px 15px rgba(212, 165, 116, 0.4);
        }
        100% {
            box-shadow: 0 6px 25px rgba(212, 165, 116, 0.7);
        }
    }

    .section-content {
        position: relative;
        z-index: 2;
    }

    .section-content p {
        font-size: 1.15rem;
        line-height: 1.8;
        margin-bottom: 1.8rem;
        color: var(--text-secondary);
    }

    /* Enhanced Lists */
    .styled-list {
        list-style: none;
        padding: 0;
    }

    .styled-list li {
        position: relative;
        padding-left: 2.5rem;
        margin-bottom: 1.5rem;
        line-height: 1.7;
        color: var(--text-secondary);
        transition: var(--transition-smooth);
        text-align: left;
    }

    .styled-list li::before {
        content: "✓";
        position: absolute;
        left: 0;
        top: 0;
        color: var(--white);
        font-weight: bold;
        font-size: 1rem;
        width: 28px;
        height: 28px;
        background: var(--gradient-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--shadow-sm);
        transition: var(--transition-bounce);
    }

    .styled-list li:hover::before {
        transform: scale(1.15) rotate(10deg);
        box-shadow: var(--shadow-md);
    }

    /* Enhanced Warning Box */
    .warning-box {
        background:
                linear-gradient(135deg, rgba(244, 208, 63, 0.15) 0%, rgba(244, 208, 63, 0.05) 100%),
                var(--glass-bg);
        backdrop-filter: var(--backdrop-blur);
        border-left: 6px solid var(--gold);
        border-radius: var(--radius-md);
        padding: 2.5rem;
        margin: 3rem 0;
        position: relative;
        box-shadow: var(--shadow-md);
        border: 1px solid rgba(244, 208, 63, 0.2);
    }

    .warning-box::before {
        content: "⚠️";
        position: absolute;
        top: 2rem;
        right: 2rem;
        font-size: 2rem;
        animation: warningPulse 2s ease-in-out infinite;
    }

    @keyframes warningPulse {
        0%, 100% {
            transform: scale(1);
            opacity: 0.8;
        }
        50% {
            transform: scale(1.1);
            opacity: 1;
        }
    }

    .warning-box h4 {
        color: #d68910;
        margin-bottom: 1rem;
        font-size: 1.3rem;
        font-weight: 700;
    }

    /* Enhanced Feature Cards */
    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 3rem;
        margin: 5rem 0;
    }

    .feature-card {
        background: var(--glass-bg);
        backdrop-filter: var(--backdrop-blur);
        border-radius: var(--radius-lg);
        padding: 3.5rem;
        text-align: center;
        box-shadow: var(--glass-shadow);
        border: 1px solid var(--glass-border);
        transition: var(--transition-spring);
        position: relative;
        overflow: hidden;
        transform: translateY(0) scale(1);
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--gradient-primary);
        opacity: 0;
        transition: var(--transition-smooth);
        z-index: 1;
    }

    .feature-card::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: var(--transition-smooth);
        z-index: 2;
    }

    .feature-card:hover::before {
        opacity: 0.08;
    }

    .feature-card:hover::after {
        width: 400px;
        height: 400px;
    }

    .feature-card:hover {
        transform: translateY(-20px) scale(1.03);
        box-shadow: var(--shadow-xl);
    }

    .feature-card-content {
        position: relative;
        z-index: 3;
    }

    .feature-icon {
        width: 100px;
        height: 100px;
        background: var(--gradient-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2.5rem;
        box-shadow: var(--shadow-lg);
        transition: var(--transition-bounce);
        position: relative;
        overflow: hidden;
    }

    .feature-icon::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.4) 50%, transparent 70%);
        transform: rotate(-45deg);
        transition: var(--transition-smooth);
    }

    .feature-card:hover .feature-icon {
        transform: scale(1.15) rotate(10deg);
        box-shadow: var(--shadow-xl);
    }

    .feature-card:hover .feature-icon::before {
        transform: rotate(-45deg) translateX(200%);
    }

    .feature-icon svg {
        width: 44px;
        height: 44px;
        stroke: var(--white);
        position: relative;
        z-index: 2;
        stroke-width: 2.5;
    }

    .feature-card h3 {
        font-size: 1.6rem;
        margin-bottom: 1.8rem;
        color: var(--primary);
        font-weight: 700;
    }

    .feature-card p {
        color: var(--text-secondary);
        line-height: 1.7;
        font-size: 1.05rem;
    }

    /* Enhanced Image Sections */
    .image-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 5rem;
        align-items: center;
        margin: 5rem 0;
    }

    .image-content img {
        width: 100%;
        height: 600px;
        object-fit: cover;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        transition: var(--transition-smooth);
    }

    .image-content img:hover {
        transform: scale(1.02);
        box-shadow: var(--shadow-xl);
    }

    /* Enhanced Tips Box */
    .tips-box {
        background:
                linear-gradient(135deg, rgba(46, 204, 113, 0.1) 0%, rgba(46, 204, 113, 0.05) 100%),
                var(--glass-bg);
        backdrop-filter: var(--backdrop-blur);
        border-radius: var(--radius-lg);
        padding: 3.5rem;
        margin: 4rem 0;
        border: 1px solid rgba(46, 204, 113, 0.2);
        position: relative;
        box-shadow: var(--shadow-md);
    }

    .tips-box::before {
        content: "💡";
        position: absolute;
        top: 2rem;
        right: 2rem;
        font-size: 2.5rem;
        animation: tipsPulse 3s ease-in-out infinite;
    }

    @keyframes tipsPulse {
        0%, 100% {
            transform: scale(1) rotate(0deg);
            opacity: 0.8;
        }
        50% {
            transform: scale(1.15) rotate(5deg);
            opacity: 1;
        }
    }

    .tips-box h3 {
        color: var(--primary);
        margin-bottom: 2rem;
        font-size: 1.7rem;
        font-weight: 700;
    }

    /* Enhanced CTA Section */
    .cta-section {
        background: var(--gradient-hero);
        color: var(--white);
        padding: clamp(4rem, 8vw, 8rem) clamp(3rem, 6vw, 5rem);
        border-radius: var(--radius-xl);
        text-align: center;
        box-shadow: var(--shadow-xl);
        margin: clamp(4rem, 8vw, 8rem) 0;
        position: relative;
        overflow: hidden;
        backdrop-filter: var(--backdrop-blur);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .cta-section::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background:
                radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
                linear-gradient(45deg, transparent 40%, rgba(212, 165, 116, 0.15) 50%, transparent 60%);
        animation: ctaPulse 8s ease-in-out infinite;
        z-index: 1;
    }

    .cta-section::after {
        content: '';
        position: absolute;
        top: 20%;
        right: 15%;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(212, 165, 116, 0.12) 0%, transparent 70%);
        border-radius: 50%;
        animation: ctaFloat 10s ease-in-out infinite reverse;
        z-index: 1;
    }

    @keyframes ctaPulse {
        0%, 100% {
            transform: translate(-50%, -50%) scale(1) rotate(0deg);
            opacity: 0.4;
        }
        50% {
            transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
            opacity: 0.8;
        }
    }

    @keyframes ctaFloat {
        0%, 100% {
            transform: translateY(0) scale(1);
            opacity: 0.6;
        }
        50% {
            transform: translateY(-30px) scale(1.2);
            opacity: 1;
        }
    }

    .cta-content {
        position: relative;
        z-index: 10;
    }

    .cta-section h2 {
        color: var(--white);
        font-size: clamp(2.5rem, 5vw, 4rem);
        margin-bottom: 2rem;
        font-weight: 800;
        text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
    }

    .cta-section p {
        max-width: 850px;
        margin: 0 auto 4rem;
        font-size: 1.4rem;
        opacity: 0.95;
        line-height: 1.8;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
        color: rgba(255, 255, 255, 0.95);
    }

    .cta-buttons {
        display: flex;
        justify-content: center;
        gap: 2.5rem;
        flex-wrap: wrap;
    }

    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        font-weight: 700;
        padding: 1.6rem 3.5rem;
        border-radius: 50px;
        transition: var(--transition-bounce);
        cursor: pointer;
        border: none;
        font-family: inherit;
        font-size: 1.15rem;
        white-space: nowrap;
        position: relative;
        box-shadow: var(--shadow-lg);
        transform: translateY(0) scale(1);
        overflow: hidden;
        letter-spacing: 0.5px;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: var(--transition-smooth);
    }

    .btn:hover::before {
        width: 400px;
        height: 400px;
    }

    .btn--primary {
        background: var(--gradient-accent);
        color: var(--white);
        border: 3px solid rgba(255, 255, 255, 0.2);
    }

    .btn--primary:hover {
        transform: translateY(-8px) scale(1.08);
        box-shadow: var(--shadow-xl);
        color: var(--white);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .btn--secondary {
        background: rgba(255, 255, 255, 0.95);
        color: var(--primary);
        border: 3px solid rgba(255, 255, 255, 0.3);
    }

    .btn--secondary:hover {
        transform: translateY(-8px) scale(1.08);
        box-shadow: var(--shadow-xl);
        color: var(--primary);
        background: rgba(255, 255, 255, 1);
    }

    .btn:active {
        transform: translateY(-4px) scale(1.04);
    }

    /* Enhanced Responsive Design */
    @media (max-width: 1200px) {
        .feature-grid {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2.5rem;
        }
    }

    @media (max-width: 992px) {
        .image-section {
            grid-template-columns: 1fr;
            gap: 3rem;
        }

        .content-section {
            padding: clamp(2rem, 5vw, 4rem);
        }

        .help-grid {
            grid-template-columns: 1fr;
            gap: 2.5rem;
        }
    }

    @media (max-width: 768px) {
        .hero {
            margin: 1.5rem auto;
            padding: clamp(3rem, 6vw, 5rem) clamp(2rem, 5vw, 3rem);
            border-radius: var(--radius-lg);
        }

        .nav-pills {
            padding: 2rem;
            gap: 1rem;
        }

        .nav-pill {
            padding: 0.8rem 1.6rem;
            font-size: 0.9rem;
        }

        .feature-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        .cta-buttons {
            flex-direction: column;
            align-items: center;
            gap: 1.5rem;
        }

        .btn {
            width: 100%;
            max-width: 350px;
        }
    }

    @media (max-width: 576px) {
        .help-card, .feature-card {
            padding: 2.5rem;
        }

        .tips-box, .warning-box {
            padding: 2rem;
        }

        .hero-underline {
            width: 60px;
            height: 3px;
        }

        .section-underline {
            width: 80px;
            height: 4px;
        }
    }

    /* Enhanced scroll animations */
    .scroll-reveal {
        opacity: 0;
        transform: translateY(50px);
        transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .scroll-reveal.revealed {
        opacity: 1;
        transform: translateY(0);
    }

    /* Support links grid styles */
    .support-links-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
        margin: 3rem 0;
    }

    .simple-link-card {
        background: var(--glass-bg);
        backdrop-filter: var(--backdrop-blur);
        border-radius: var(--radius-md);
        padding: 2.5rem;
        border: 1px solid var(--glass-border);
        box-shadow: var(--shadow-sm);
        transition: var(--transition-spring);
        text-decoration: none;
        color: inherit;
        position: relative;
        overflow: hidden;
    }

    .simple-link-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: var(--gradient-primary);
        transform: scaleX(0);
        transition: var(--transition-smooth);
    }

    .simple-link-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: var(--shadow-lg);
    }

    .simple-link-card:hover::before {
        transform: scaleX(1);
    }

    .link-header h4 {
        color: var(--primary);
        font-size: 1.3rem;
        margin-bottom: 0.8rem;
        font-weight: 700;
    }

    .simple-link-card p {
        color: var(--text-secondary);
        margin: 0;
        font-size: 1rem;
    }
</style>

<!-- Hero Section -->
<div class="hero scroll-reveal">
    <div class="hero-content">
        <h1>How to Help Your Partner with Addiction</h1>
        <div class="hero-underline"></div>
        <p>
            Learning how to help your partner or spouse with addiction is one of the most challenging situations you'll face. You're not alone in this journey. Our comprehensive guide provides expert guidance on boundaries, communication, and family support during recovery from <a href="https://williamsvillewellness.com/what-we-treat/alcohol-addiction-treatment-virginia/">alcohol</a>, <a href="https://williamsvillewellness.com/what-we-treat/drug-addiction-treatment-virginia/">drug</a>, or <a href="https://williamsvillewellness.com/what-we-treat/gambling-addiction-treatment-in-virginia/">gambling addiction</a>.
        </p>
    </div>
</div>

<!-- Navigation Section -->
<div class="container">
    <div class="nav-section scroll-reveal">
        <div class="nav-pills">
            <a href="#understanding" class="nav-pill">Understanding Addiction</a>
            <a href="#how-to-help" class="nav-pill">How to Help</a>
            <a href="#boundaries" class="nav-pill">Setting Boundaries</a>
            <a href="#self-care" class="nav-pill">Self-Care</a>
            <a href="#communication" class="nav-pill">Communication</a>
            <a href="#what-to-expect" class="nav-pill">What to Expect</a>
            <a href="#resources" class="nav-pill">Resources</a>
        </div>
    </div>

    <!-- Help Cards -->
    <div class="help-section">
        <div class="help-grid">
            <div class="help-card scroll-reveal">
                <h3>
                    <div class="help-card-icon">🆘</div>
                    Immediate Help
                </h3>
                <p><strong>Crisis Support:</strong><br>
                    <a href="tel:988">988 Suicide & Crisis Lifeline</a></p>
                <p><strong>Al-Anon Family Groups:</strong><br>
                    <a href="tel:**************">**************</a></p>
                <p><strong>Gam-Anon (Gambling):</strong><br>
                    <a href="tel:**************">**************</a></p>
            </div>

            <div class="help-card scroll-reveal">
                <h3>
                    <div class="help-card-icon">🏥</div>
                    Professional Treatment
                </h3>
                <p><strong>Williamsville Wellness:</strong><br>
                    <a href="tel:8046550094">(*************</a></p>
                <p>Call to learn about our comprehensive addiction treatment programs and family support services.</p>
            </div>
        </div>
    </div>

    <!-- Understanding Addiction Section -->
    <div id="understanding" class="content-section scroll-reveal">
        <div class="section-content">
            <h2 class="section-title">Understanding Addiction as a Disease</h2>
            <div class="section-underline"></div>

            <div class="image-section">
                <div class="text-content">
                    <p>The first step in helping your partner is understanding that addiction—whether to alcohol, drugs, or gambling—is a chronic brain disease, not a moral failing or lack of willpower. This perspective shift is crucial for both your relationship and your partner's recovery.</p>

                    <div class="warning-box">
                        <h4>Remember:</h4>
                        <p>You didn't cause your partner's addiction, you can't control it, and you can't cure it. But you can learn how to respond in ways that support recovery rather than enable the addiction.</p>
                    </div>
                </div>
                <div class="image-content">
                    <img src="https://images.unsplash.com/photo-1426543881949-cbd9a76740a4?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y291cGxlfGVufDB8fDB8fHww" alt="Couple supporting each other">
                </div>
            </div>

            <h3>Key Facts About Addiction:</h3>
            <ul class="styled-list">
                <li>Addiction changes brain chemistry and affects decision-making</li>
                <li>Recovery is possible with proper treatment and support</li>
                <li>Relapses are common and don't mean treatment has failed</li>
                <li>Professional help is usually necessary for lasting recovery</li>
                <li>Gambling addiction affects the same brain reward pathways as substance addiction</li>
                <li>Co-occurring disorders (addiction + mental health issues) are common</li>
            </ul>
        </div>
    </div>

    <!-- How to Help Section -->
    <div id="how-to-help" class="content-section scroll-reveal">
        <div class="section-content">
            <h2 class="section-title">How to Help Your Partner</h2>
            <div class="section-underline"></div>

            <p>Understanding how to help your partner or spouse with addiction requires a delicate balance between being helpful and avoiding enabling behaviors. <a href="https://williamsvillewellness.com/how-family-dynamics-influence-addiction/">Family dynamics</a> play a crucial role in both addiction and recovery. Here's how you can provide meaningful support:</p>

            <div class="tips-box">
                <h3>Effective Ways to Help:</h3>
                <ul class="styled-list">
                    <li>Encourage professional treatment and offer to help find resources</li>
                    <li>Learn about addiction and recovery processes</li>
                    <li>Attend family therapy sessions when invited</li>
                    <li>Celebrate small victories and milestones in recovery</li>
                    <li>Be patient and understanding during difficult moments</li>
                    <li>Support healthy activities and relationships</li>
                    <li>Keep emergency numbers and resources handy</li>
                </ul>
            </div>

            <h3>Avoid Enabling Behaviors:</h3>
            <ul class="styled-list">
                <li>Don't make excuses for their behavior or lie to cover for them</li>
                <li>Don't give money that could be used to buy substances or for gambling</li>
                <li>Don't bail them out of consequences related to their addiction</li>
                <li>Don't threaten consequences you're not prepared to follow through on</li>
                <li>Don't use substances yourself as a coping mechanism</li>
                <li>Don't pay gambling debts or loans taken out for gambling</li>
                <li>Don't give access to credit cards, bank accounts, or financial assets</li>
            </ul>
        </div>
    </div>

    <!-- Boundaries Section -->
    <div id="boundaries" class="content-section scroll-reveal">
        <div class="section-content">
            <h2 class="section-title">Setting Healthy Boundaries</h2>
            <div class="section-underline"></div>

            <div class="image-section">
                <div class="image-content">
                    <img src="https://images.unsplash.com/photo-*************-937abd82a4e1?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjZ8fGNvdXBsZXN8ZW58MHx8MHx8fDA%3D" alt="Couple in nature setting boundaries">
                </div>
                <div class="text-content">
                    <p>Boundaries are essential for your well-being and your partner's recovery. They're not about punishment—they're about protecting yourself and creating conditions that support sobriety.</p>

                    <div class="warning-box">
                        <h4>Important:</h4>
                        <p>Be prepared to follow through on your boundaries. Inconsistent enforcement can actually make the situation worse and undermine your credibility.</p>
                    </div>
                </div>
            </div>

            <h3>Examples of Healthy Boundaries:</h3>
            <ul class="styled-list">
                <li><strong>Financial:</strong> "I will not give you money unless I know exactly what it's for"</li>
                <li><strong>Behavioral:</strong> "I will not tolerate substance use in our home"</li>
                <li><strong>Gambling-specific:</strong> "I will not cover debts related to gambling" or "I will not lie to creditors for you"</li>
                <li><strong>Communication:</strong> "I will not engage in conversations when you're under the influence"</li>
                <li><strong>Safety:</strong> "I will leave the situation if I feel unsafe"</li>
                <li><strong>Time:</strong> "I need time for my own activities and friends"</li>
                <li><strong>Technology:</strong> "I will not give you access to online banking or credit cards"</li>
            </ul>
        </div>
    </div>

    <!-- Self-Care Section -->
    <div id="self-care" class="content-section scroll-reveal">
        <div class="section-content">
            <h2 class="section-title">Taking Care of Yourself</h2>
            <div class="section-underline"></div>

            <p>Supporting someone with addiction can be emotionally and physically exhausting. Your well-being matters too, and taking care of yourself isn't selfish—it's necessary.</p>

            <div class="feature-grid">
                <div class="feature-card scroll-reveal">
                    <div class="feature-card-content">
                        <div class="feature-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                            </svg>
                        </div>
                        <h3>Physical Health</h3>
                        <p>Maintain regular exercise, eat well, get enough sleep, and see your doctor regularly. Your physical health impacts your emotional resilience.</p>
                    </div>
                </div>

                <div class="feature-card scroll-reveal">
                    <div class="feature-card-content">
                        <div class="feature-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="3"/>
                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                            </svg>
                        </div>
                        <h3>Mental Health</h3>
                        <p>Consider therapy for yourself, practice stress management, and don't isolate yourself from friends and family.</p>
                    </div>
                </div>

                <div class="feature-card scroll-reveal">
                    <div class="feature-card-content">
                        <div class="feature-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                        </div>
                        <h3>Social Support</h3>
                        <p>Join <a href="https://al-anon.org" target="_blank" rel="noopener noreferrer">Al-Anon</a> or other support groups, maintain friendships, and don't be afraid to ask for help when you need it.</p>
                    </div>
                </div>

                <div class="feature-card scroll-reveal">
                    <div class="feature-card-content">
                        <div class="feature-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <h3>Emotional Balance</h3>
                        <p>Practice mindfulness, journaling, or meditation. Set aside time for activities that bring you joy and help you process your emotions.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Communication Section -->
    <div id="communication" class="content-section scroll-reveal">
        <div class="section-content">
            <h2 class="section-title">Communication Strategies</h2>
            <div class="section-underline"></div>

            <p style="text-align: center; font-size: 1.2rem; margin-bottom: 4rem;">Your words have power to heal or harm. Learn communication strategies that support recovery and strengthen relationships.</p>

            <div class="simple-communication">
                <div class="feature-grid" style="grid-template-columns: 1fr 1fr; gap: 3rem;">
                    <div class="feature-card scroll-reveal">
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M9 12l2 2 4-4"/>
                                    <circle cx="12" cy="12" r="10"/>
                                </svg>
                            </div>
                            <h3>Helpful Communication</h3>
                            <p style="color: var(--text-light); margin-bottom: 1.5rem;">These approaches support recovery and strengthen relationships:</p>
                            <ul class="styled-list">
                                <li>Use "I" statements to express your feelings</li>
                                <li>Listen without judgment when they're ready to talk</li>
                                <li>Be specific about behaviors rather than making general accusations</li>
                                <li>Choose the right time and place for serious conversations</li>
                                <li>Express love and support while setting clear boundaries</li>
                            </ul>
                        </div>
                    </div>

                    <div class="feature-card scroll-reveal">
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <line x1="15" y1="9" x2="9" y2="15"/>
                                    <line x1="9" y1="9" x2="15" y2="15"/>
                                </svg>
                            </div>
                            <h3>Harmful Communication</h3>
                            <p style="color: var(--text-light); margin-bottom: 1.5rem;">These approaches can damage recovery and relationships:</p>
                            <ul class="styled-list">
                                <li>Having serious conversations when they're under the influence</li>
                                <li>Using shame, guilt, or blame as motivators</li>
                                <li>Making threats you're not prepared to follow through on</li>
                                <li>Repeatedly bringing up past mistakes</li>
                                <li>Trying to control or micromanage their recovery</li>
                            </ul>
                        </div>
                    </div>

                    <div class="feature-card scroll-reveal">
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="4"/>
                                    <path d="M1.05 12a9 9 0 1 1 17.95 0A9 9 0 0 1 1.05 12z"/>
                                    <path d="M12 8v4l3 3"/>
                                </svg>
                            </div>
                            <h3>Timing Conversations</h3>
                            <p style="color: var(--text-light); margin-bottom: 1.5rem;">When and how to bring up difficult topics:</p>
                            <ul class="styled-list">
                                <li>Choose times when both of you are calm and sober</li>
                                <li>Avoid important discussions during stressful periods</li>
                                <li>Pick a private, comfortable setting without distractions</li>
                                <li>Start with smaller concerns before addressing major issues</li>
                                <li>Be prepared to table the conversation if emotions escalate</li>
                            </ul>
                        </div>
                    </div>

                    <div class="feature-card scroll-reveal">
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                            </div>
                            <h3>Building Trust</h3>
                            <p style="color: var(--text-light); margin-bottom: 1.5rem;">Rebuild your relationship foundation gradually:</p>
                            <ul class="styled-list">
                                <li>Keep your promises and follow through on commitments</li>
                                <li>Be honest about your own feelings and concerns</li>
                                <li>Acknowledge progress and celebrate small wins together</li>
                                <li>Be patient - trust is rebuilt through consistent actions over time</li>
                                <li>Work together on shared goals and activities</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="tips-box" style="margin-top: 3rem;">
                    <h3>Communication Tips</h3>
                    <ul class="styled-list">
                        <li><strong>Timing matters:</strong> Wait for calm, sober moments to discuss important issues</li>
                        <li><strong>Focus on the present:</strong> Address current behavior rather than past mistakes</li>
                        <li><strong>Use specific examples:</strong> "When you came home at 3am without calling" vs "You never communicate"</li>
                        <li><strong>Express your feelings:</strong> "I feel scared when..." vs "You make me feel..."</li>
                        <li><strong>Separate the person from the addiction:</strong> Love the person, hate the disease</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- What to Expect Section -->
    <div id="what-to-expect" class="content-section scroll-reveal">
        <div class="section-content">
            <h2 class="section-title">What to Expect During Recovery</h2>
            <div class="section-underline"></div>

            <p style="text-align: center; font-size: 1.2rem; margin-bottom: 4rem;">Recovery is a journey with distinct phases. Understanding what lies ahead helps you provide better support and manage expectations.</p>

            <div class="simple-timeline">
                <div class="feature-grid" style="grid-template-columns: 1fr 1fr; gap: 3rem;">
                    <div class="feature-card scroll-reveal">
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 2L3 7l9 5 9-5-9-5zM3 17l9 5 9-5M3 12l9 5 9-5"/>
                                </svg>
                            </div>
                            <h3>Early Recovery (0-90 Days)</h3>
                            <p style="color: var(--text-light); margin-bottom: 1.5rem;">The foundation-building phase focuses on stabilization and establishing new routines.</p>
                            <ul class="styled-list">
                                <li>Physical and emotional withdrawal symptoms</li>
                                <li>Mood swings and irritability</li>
                                <li>Establishing daily routines and structure</li>
                                <li>Learning new coping mechanisms</li>
                                <li>Temporary personality changes during adjustment</li>
                            </ul>
                        </div>
                    </div>

                    <div class="feature-card scroll-reveal">
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <path d="M10 2a20 20 0 0 0 0 20"/>
                                    <path d="M14 2a20 20 0 0 1 0 20"/>
                                    <line x1="2" y1="12" x2="22" y2="12"/>
                                </svg>
                            </div>
                            <h3>Ongoing Recovery (3+ Months)</h3>
                            <p style="color: var(--text-light); margin-bottom: 1.5rem;">Building lasting change and addressing root causes for long-term success.</p>
                            <ul class="styled-list">
                                <li>Gradual improvement in physical and mental health</li>
                                <li>Working on underlying issues and triggers</li>
                                <li>Rebuilding trust and relationships</li>
                                <li>Developing new interests and social connections</li>
                                <li>Ongoing therapy and support group participation</li>
                            </ul>
                        </div>
                    </div>

                    <div class="feature-card scroll-reveal">
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                            </div>
                            <h3>Potential Challenges</h3>
                            <p style="color: var(--text-light); margin-bottom: 1.5rem;">Common obstacles you may encounter during the recovery journey:</p>
                            <ul class="styled-list">
                                <li>Cravings and urges may persist for months</li>
                                <li>Relationship tensions as dynamics change</li>
                                <li>Financial stress from treatment costs or past damage</li>
                                <li>Social challenges with old friends or activities</li>
                                <li>Setbacks or relapses are common and don't mean failure</li>
                            </ul>
                        </div>
                    </div>

                    <div class="feature-card scroll-reveal">
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                                    <polyline points="22,4 12,14.01 9,11.01"/>
                                </svg>
                            </div>
                            <h3>Signs of Progress</h3>
                            <p style="color: var(--text-light); margin-bottom: 1.5rem;">Positive indicators that recovery is taking hold:</p>
                            <ul class="styled-list">
                                <li>Improved sleep patterns and physical health</li>
                                <li>Better emotional regulation and mood stability</li>
                                <li>Increased honesty and open communication</li>
                                <li>Taking responsibility for actions and decisions</li>
                                <li>Actively participating in treatment and support groups</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="warning-box">
                <h4>About Relapses:</h4>
                <p>Relapses are common and don't mean treatment has failed. If a relapse occurs, encourage your partner to get back into treatment immediately and avoid taking it personally.</p>
            </div>
        </div>
    </div>

    <!-- Resources Section -->
    <div id="resources" class="content-section scroll-reveal">
        <div class="section-content">
            <h2 class="section-title">Resources and Support</h2>
            <div class="section-underline"></div>

            <p style="text-align: center; font-size: 1.2rem; margin-bottom: 4rem;">You don't have to navigate this alone. When learning how to help your partner or spouse with addiction, connecting with trusted resources and communities designed to support families through recovery is essential.</p>

            <div class="simple-resources">
                <!-- Support Groups -->
                <div class="resource-section">
                    <h3 style="text-align: center; color: var(--primary); font-size: 1.8rem; margin-bottom: 2rem;">Support Groups for Families</h3>
                    <div class="support-links-grid">
                        <a href="https://al-anon.org" target="_blank" rel="noopener noreferrer" class="simple-link-card">
                            <div class="link-header">
                                <h4>Al-Anon</h4>
                            </div>
                            <p>For families and friends of alcoholics</p>
                        </a>
                        <a href="https://www.nar-anon.org" target="_blank" rel="noopener noreferrer" class="simple-link-card">
                            <div class="link-header">
                                <h4>Nar-Anon</h4>
                            </div>
                            <p>For families affected by drug addiction</p>
                        </a>
                        <a href="https://www.gam-anon.org" target="_blank" rel="noopener noreferrer" class="simple-link-card">
                            <div class="link-header">
                                <h4>Gam-Anon</h4>
                            </div>
                            <p>For families of compulsive gamblers</p>
                        </a>
                        <a href="https://www.smartrecovery.org/family" target="_blank" rel="noopener noreferrer" class="simple-link-card">
                            <div class="link-header">
                                <h4>SMART Recovery Family & Friends</h4>
                            </div>
                            <p>Science-based family support program</p>
                        </a>
                        <a href="https://www.familiesanonymous.org" target="_blank" rel="noopener noreferrer" class="simple-link-card">
                            <div class="link-header">
                                <h4>Families Anonymous</h4>
                            </div>
                            <p>Comprehensive family addiction support</p>
                        </a>
                        <a href="https://coda.org" target="_blank" rel="noopener noreferrer" class="simple-link-card">
                            <div class="link-header">
                                <h4>Co-Dependents Anonymous</h4>
                            </div>
                            <p>Support for those affected by codependency</p>
                        </a>
                    </div>
                </div>

                <!-- Resource Categories -->
                <div class="feature-grid" style="margin-top: 4rem;">
                    <div class="feature-card scroll-reveal">
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M9 12l2 2 4-4"/>
                                    <circle cx="12" cy="12" r="10"/>
                                </svg>
                            </div>
                            <h3>Professional Resources</h3>
                            <ul class="styled-list">
                                <li>Individual therapy for yourself</li>
                                <li>Couples counseling (when appropriate)</li>
                                <li>Family therapy sessions</li>
                                <li>Intervention specialists</li>
                                <li>Addiction counselors</li>
                            </ul>
                        </div>
                    </div>

                    <div class="feature-card scroll-reveal">
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"/>
                                    <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"/>
                                </svg>
                            </div>
                            <h3>Educational Resources</h3>
                            <ul class="styled-list">
                                <li>Books about addiction and recovery</li>
                                <li>Online courses for family support</li>
                                <li>Workshops and seminars</li>
                                <li>Podcasts about family recovery</li>
                                <li>Webinars and online training</li>
                            </ul>
                        </div>
                    </div>

                    <div class="feature-card scroll-reveal">
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                                </svg>
                            </div>
                            <h3>Online Communities</h3>
                            <ul class="styled-list">
                                <li>Virtual support group meetings</li>
                                <li>Online forums and chat groups</li>
                                <li>Social media support communities</li>
                                <li>24/7 text-based crisis support</li>
                                <li>Mobile apps for family support</li>
                            </ul>
                        </div>
                    </div>

                    <div class="feature-card scroll-reveal">
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <line x1="12" y1="8" x2="12" y2="12"/>
                                    <line x1="12" y1="16" x2="12.01" y2="16"/>
                                </svg>
                            </div>
                            <h3>Crisis & Emergency Resources</h3>
                            <ul class="styled-list">
                                <li><a href="tel:988" style="color: var(--text-secondary); text-decoration: none;">988</a> <a href="https://988lifeline.org" target="_blank" rel="noopener noreferrer" style="color: var(--text-secondary); text-decoration: none;">Suicide & Crisis Lifeline</a> available 24/7</li>
                                <li>Local emergency services and mobile crisis teams</li>
                                <li><a href="https://www.samhsa.gov/find-help/national-helpline" target="_blank" rel="noopener noreferrer" style="color: var(--text-secondary); text-decoration: none;">SAMHSA National Helpline</a>: <a href="tel:18006624357" style="color: var(--text-secondary); text-decoration: none;">**************</a></li>
                                <li><a href="https://www.thehotline.org" target="_blank" rel="noopener noreferrer" style="color: var(--text-secondary); text-decoration: none;">National Domestic Violence Hotline</a>: <a href="tel:18007997233" style="color: var(--text-secondary); text-decoration: none;">**************</a></li>
                                <li>Emergency financial assistance programs</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Resources Section -->
<div class="container">
    <div class="content-section scroll-reveal">
        <div class="section-content">
            <h2 class="section-title">Additional Support Resources</h2>
            <div class="section-underline"></div>
            <p style="text-align: center; margin-bottom: 3rem;">Connect with organizations and resources specifically designed to help families affected by addiction.</p>

            <div class="feature-grid">
                <div class="feature-card scroll-reveal">
                    <div class="feature-card-content">
                        <div class="feature-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                            </svg>
                        </div>
                        <h3>24/7 Crisis Support</h3>
                        <p>If you're in immediate crisis or your partner is threatening self-harm, call 988 for the Suicide & Crisis Lifeline or 911 for emergency services.</p>
                    </div>
                </div>

                <div class="feature-card scroll-reveal">
                    <div class="feature-card-content">
                        <div class="feature-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                        </div>
                        <h3>Al-Anon & Support Meetings</h3>
                        <p>Find local Al-Anon, Nar-Anon, or Gam-Anon meetings in your area. These support groups are specifically for family members and friends affected by addiction.</p>
                    </div>
                </div>

                <div class="feature-card scroll-reveal">
                    <div class="feature-card-content">
                        <div class="feature-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                <polyline points="22,6 12,13 2,6"/>
                            </svg>
                        </div>
                        <h3>Family Therapy</h3>
                        <p>Professional family therapy can help improve communication, rebuild trust, and address the impact of addiction on your entire family system.</p>
                    </div>
                </div>

                <div class="feature-card scroll-reveal">
                    <div class="feature-card-content">
                        <div class="feature-icon">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <line x1="12" y1="8" x2="12" y2="12"/>
                                <line x1="12" y1="16" x2="12.01" y2="16"/>
                            </svg>
                        </div>
                        <h3>Financial & Legal Guidance</h3>
                        <p>Find resources for debt counseling, legal aid for addiction-related issues, and financial planning during recovery. Many communities offer free services.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CTA Section -->
<div class="container">
    <div class="cta-section scroll-reveal">
        <div class="cta-content">
            <h2>Get Professional Support Today</h2>
            <p>
                Learning how to help your partner or spouse with addiction starts with professional treatment. Understanding <a href="https://williamsvillewellness.com/does-insurance-cover-rehab/">insurance coverage options</a> and <a href="https://williamsvillewellness.com/how-long-is-residential-rehab/">treatment program lengths</a> can help you make informed decisions. Our family support services help both individuals and their loved ones navigate the recovery process together with expert guidance and compassionate care.
            </p>
            <div class="cta-buttons">
                <a href="/contact/" class="btn btn--secondary">Learn About Our Programs</a>
                <a href="tel:8046550094" class="btn btn--secondary">(*************</a>
            </div>
        </div>
    </div>
</div>

<script>
    // Enhanced smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Enhanced scroll reveal animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('revealed');
                }, index * 100); // Stagger animations
            }
        });
    }, observerOptions);

    // Observe all scroll-reveal elements
    document.querySelectorAll('.scroll-reveal').forEach(el => {
        observer.observe(el);
    });

    // Enhanced highlight current section in navigation
    const sections = document.querySelectorAll('div[id]');
    const navLinks = document.querySelectorAll('.nav-pill');

    function highlightCurrentSection() {
        let current = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            if (scrollY >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.style.background = '';
            link.style.color = '';
            link.style.transform = '';
            link.style.boxShadow = '';

            if (link.getAttribute('href') === '#' + current) {
                link.style.background = 'var(--gradient-primary)';
                link.style.color = 'var(--white)';
                link.style.transform = 'translateY(-5px) scale(1.05)';
                link.style.boxShadow = 'var(--shadow-lg)';
            }
        });
    }

    window.addEventListener('scroll', highlightCurrentSection);
    highlightCurrentSection(); // Call once on load

    // Enhanced parallax effect for background elements
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('body::before');
        if (parallax) {
            const speed = scrolled * 0.2;
            document.documentElement.style.setProperty('--scroll-offset', `${speed}px`);
        }
    });

    // Enhanced button interactions
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.08)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });

        btn.addEventListener('mousedown', function() {
            this.style.transform = 'translateY(-4px) scale(1.04)';
        });

        btn.addEventListener('mouseup', function() {
            this.style.transform = 'translateY(-8px) scale(1.08)';
        });
    });

    // Add mouse tracking effect for hero section
    const hero = document.querySelector('.hero');
    if (hero) {
        // Removed mouse move effect - hero keeps visual look but no hover wiggle
        hero.addEventListener('mouseleave', () => {
            hero.style.transform = 'perspective(1000px) rotateX(0) rotateY(0)';
        });
    }

    // Preload images for better performance
    const images = [
        'https://images.unsplash.com/photo-1426543881949-cbd9a76740a4?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y291cGxlfGVufDB8fDB8fHww',
        'https://images.unsplash.com/photo-*************-937abd82a4e1?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjZ8fGNvdXBsZXN8ZW58MHx8MHx8fDA%3D'
    ];

    images.forEach(src => {
        const img = new Image();
        img.src = src;
    });
</script>
</body>
</html>