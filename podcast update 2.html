<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Recovery Conversations Podcast | Williamsville Wellness</title>
  <meta name="description" content="Listen to Recovery Conversations, the official podcast of Williamsville Wellness. Expert insights, recovery stories, and addiction treatment guidance available on YouTube, Spotify, and Apple Podcasts.">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
<!-- CSS Variables and Base Styles -->
<style>
  :root {
    --primary: #06502A;
    --primary-light: #097D43;
    --accent: #a8763e;
    --gold: #A4743E;
    --bg-light: #f7f9f8;
    --text-dark: #333;
    --text-light: #555;
    --white: #fff;
    --shadow-sm: 0 5px 15px rgba(0,0,0,0.05);
    --shadow-md: 0 10px 30px rgba(0,0,0,0.1);
    --shadow-lg: 0 15px 40px rgba(0,0,0,0.15);
    --radius-sm: 8px;
    --radius-md: 10px;
    --radius-lg: 30px;
  }

  *, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: clamp(1rem, 1.5vw, 1.1rem);
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--white);
  }

  .container {
    width: min(1200px, 90%);
    margin-inline: auto;
    position: relative;
  }

  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-weight: 600;
    padding: 0.8rem 1.5rem;
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    font-family: inherit;
    font-size: 0.9rem;
    white-space: nowrap;
    position: relative;
    box-shadow: 0 4px 14px 0 rgba(0,0,0,0.15);
    transform: translateY(0);
  }

  .btn--primary {
    background: linear-gradient(135deg, var(--gold) 0%, #c2975f 100%);
    color: var(--white);
  }

  .btn--primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px 0 rgba(164,116,62,0.3);
  }

  .btn--secondary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: var(--white);
  }

  .btn--secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px 0 rgba(6,80,42,0.25);
  }

  .section {
    padding: 5rem 0;
    position: relative;
  }

  .section--alt {
    background-color: var(--bg-light);
  }

  .section__header {
    text-align: center;
    margin-bottom: 3rem;
  }

  .section__title {
    color: var(--primary);
    font-size: clamp(1.8rem, 3vw, 2.5rem);
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .section__line {
    width: 60px;
    height: 3px;
    background: var(--accent);
    margin: 0 auto 1.5rem;
  }

  .section__intro {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
  }

  /* Hero Styles */
  .hero {
    background: linear-gradient(135deg,#06502A,#097D43);
    color: #fff;
    text-align: center;
    padding: 20px 5%;
    max-width: 1400px;
    margin: 40px auto;
    border-radius: 40px;
    font-family: 'Poppins', sans-serif;
  }

  .hero h1 {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: #fff;
  }

  .hero-underline {
    width: 48px;
    height: 3px;
    background: #A4743E;
    margin: 0 auto 24px;
    border-radius: 2px;
  }

  .hero p {
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto 32px;
    line-height: 1.5;
  }
</style>

<!-- Hero Section -->
<div class="hero">
  <h1>Williamsville Wellness Podcast</h1>
  <div class="hero-underline"></div>
  <p>
    Welcome to the official podcast of Williamsville Wellness, where we explore the complexities of mental health, addiction recovery, and personal growth. Through expert insights, real-life stories, and practical strategies, we aim to break the stigma around mental health and provide valuable tools for healing and resilience. Whether you're seeking guidance, supporting a loved one, or simply interested in learning more, join us as we navigate the journey to wellness, balance, and lasting recovery.
  </p>

  <div class="platform-selector">
    <p class="platform-selector__label">Listen on your preferred platform:</p>
    <div class="platform-buttons">
      <button class="platform-btn active" data-platform="all">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2L3 7l9 5 9-5-9-5zM3 17l9 5 9-5M3 12l9 5 9-5"/>
        </svg>
        All Platforms
      </button>
      <button class="platform-btn" data-platform="youtube">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
        </svg>
        YouTube
      </button>
      <button class="platform-btn" data-platform="spotify">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
        </svg>
        Spotify
      </button>
      <button class="platform-btn" data-platform="apple">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
        </svg>
        Apple Podcasts
      </button>
    </div>
  </div>
</div>

<!-- Recent Episodes Section -->
<section class="section section--alt">
  <div class="container">
    <header class="section__header">
      <h2 class="section__title">Recent Episodes</h2>
      <div class="section__line"></div>
      <p class="section__intro">
        New episodes released 2-4 times per month. Each episode is available across all platforms.
      </p>
    </header>

    <div class="episodes-grid" id="episodesGrid">
      <!-- Latest Episode -->
      <article class="episode-card">
        <div class="episode-card__image">
          <img src="https://is1-ssl.mzstatic.com/image/thumb/PodcastSource211/v4/46/7b/da/467bda6e-0c05-ed70-3e37-1ce789704431/078ab542-3989-4de5-a25b-e28b05e75734.png/470x470bb.jpg" alt="EMDR & Adult Children of Alcoholics: Healing Trauma & Uncovering Memories" />
        </div>
        <div class="episode-card__content">
          <span class="episode-card__date">July 10, 2025</span>
          <h3 class="episode-card__title">EMDR & Adult Children of Alcoholics: Healing Trauma & Uncovering Memories</h3>
          <p class="episode-card__description">
            Billy Hoffman and Stephen Smith explore EMDR therapy and its benefits for adult children of alcoholics. Stephen explains the EMDR process, different types of bilateral stimulation, and how this powerful therapy helps uncover and reprocess distressing memories for those who find traditional talk therapy challenging.
          </p>
          <div class="episode-card__platforms">
            <a href="https://www.youtube.com/watch?v=PWb601FgEvM" class="platform-link youtube" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
              Watch on YouTube
            </a>
            <a href="https://open.spotify.com/episode/1dCDKVMzh03PGrGINp8VGm?si=lmk3DFdzQuCdfGjFLUr0kQ" class="platform-link spotify" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
              </svg>
              Listen on Spotify
            </a>
            <a href="#" class="platform-link apple" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
              </svg>
              Listen on Apple
            </a>
          </div>
        </div>
      </article>

      <!-- Episode 2 -->
      <article class="episode-card">
        <div class="episode-card__image">
          <img src="https://is1-ssl.mzstatic.com/image/thumb/PodcastSource211/v4/bf/c1/4d/bfc14d0d-6567-951e-bf5e-82bf7ceedc0b/86b49d20-19c3-443f-b268-c79132c0de42.png/470x470bb.jpg" alt="Beyond the Laundry List: An ACA Recovery Story" />
        </div>
        <div class="episode-card__content">
          <span class="episode-card__date">June 20, 2025</span>
          <h3 class="episode-card__title">Beyond the Laundry List: An ACA Recovery Story</h3>
          <p class="episode-card__description">
            In this powerful episode of the Williamsville Podcast, Billy Hoffman and Stephen Smith dive into the world of Adult Children of Alcoholics (ACA). Billy shares his personal journey discovering ACA traits years into recovery and how this awareness helped him understand and work through childhood pain.
          </p>
          <div class="episode-card__platforms">
            <a href="https://www.youtube.com/watch?v=qWajeoeOJhE&list=PLxmZBzOL_rhFAsOsnBUsyogSIKdyqE7D-" class="platform-link youtube" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
              Watch on YouTube
            </a>
            <a href="https://open.spotify.com/episode/532SOjJjGfKrY1NNhoRWHh?si=RVwFCiaTS7-k6VsfMJTsZA" class="platform-link spotify" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
              </svg>
              Listen on Spotify
            </a>
            <a href="https://podcasts.apple.com/us/podcast/beyond-the-laundry-list-an-aca-recovery-story/id1802646094?i=1000714110547" class="platform-link apple" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
              </svg>
              Listen on Apple
            </a>
          </div>
        </div>
      </article>

      <!-- Episode 2 -->
      <article class="episode-card">
        <div class="episode-card__image">
          <img src="https://is1-ssl.mzstatic.com/image/thumb/PodcastSource221/v4/71/a6/48/71a648a2-f936-a956-c1dd-5384a7cdfbb3/03cad242-9138-49b7-a512-d50765f2e194.png/470x470bb.jpg" alt="Para-Alcoholism & Reactive Living | ACA Traits #13 & 14 Explored" />
        </div>
        <div class="episode-card__content">
          <span class="episode-card__date">June 6, 2025</span>
          <h3 class="episode-card__title">Para-Alcoholism & Reactive Living | ACA Traits #13 & 14 Explored</h3>
          <p class="episode-card__description">
            In this final episode of our ACA Laundry List series, Billy and Stephen explore traits #13 and #14 - para-alcoholism and reactive vs. proactive living. Even without picking up a drink, adult children often inherit the emotional and behavioral patterns of addiction as a family disease.
          </p>
          <div class="episode-card__platforms">
            <a href="https://www.youtube.com/watch?v=JkfgOUPxLDc&list=PLxmZBzOL_rhFAsOsnBUsyogSIKdyqE7D-&index=2" class="platform-link youtube" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
              Watch on YouTube
            </a>
            <a href="https://open.spotify.com/episode/2wIsWbb6k7MsChJnNVKb9r?si=_qdUYlK2RZaU1iuG2FS03Q" class="platform-link spotify" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
              </svg>
              Listen on Spotify
            </a>
            <a href="https://podcasts.apple.com/us/podcast/para-alcoholism-reactive-living-aca-traits-13-14-explored/id1802646094?i=1000711787154" class="platform-link apple" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
              </svg>
              Listen on Apple
            </a>
          </div>
        </div>
      </article>
      <article class="episode-card">
        <div class="episode-card__image">
          <img src="https://is1-ssl.mzstatic.com/image/thumb/PodcastSource221/v4/73/fb/15/73fb1520-7b50-f7a8-1ab0-d863765172a6/6ea4f5f2-d7cc-4dfb-bff6-f35365c13233.png/600x600bb.webp" alt="Unpacking Shame in Addiction Recovery" />
        </div>
        <div class="episode-card__content">
          <span class="episode-card__date">May 22, 2025</span>
          <h3 class="episode-card__title">Unpacking Shame in Addiction Recovery</h3>
          <p class="episode-card__description">
            Shame can drive addiction, and addiction can deepen shame. In this podcast, Emma Land, LPC and Stephen Smith, LPC highlight the difference between shame and guilt, the cycle of shame in addiction, how childhood shapes lifelong shame, and much more.
          </p>
          <div class="episode-card__platforms">
            <a href="https://www.youtube.com/watch?v=ii02vK5aJbE&list=PLxmZBzOL_rhFAsOsnBUsyogSIKdyqE7D-&index=4" class="platform-link youtube" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
              Watch on YouTube
            </a>
            <a href="https://open.spotify.com/episode/7qZWC1cDXxhpZeQqglmvpi?si=-RYTjiDURbuQMlf8ACvbPw" class="platform-link spotify" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
              </svg>
              Listen on Spotify
            </a>
            <a href="https://podcasts.apple.com/us/podcast/unpacking-shame-in-addiction-recovery-williamsville/id1802646094?i=1000709939928" class="platform-link apple" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
              </svg>
              Listen on Apple
            </a>
          </div>
        </div>
      </article>

      <!-- Episode 4 -->

      <!-- Episode 3 -->
      <article class="episode-card">
        <div class="episode-card__image">
          <img src="https://is1-ssl.mzstatic.com/image/thumb/PodcastSource221/v4/10/8f/fc/108ffcbb-d223-0f60-02db-4dce789de46b/a455c301-e961-460d-b741-e6982e5140ca.png/470x470bb.jpg" alt="Harsh Self-Judgment & Low Self-Esteem | ACA Traits #11 & 12 Explored" loading="lazy" />
        </div>
        <div class="episode-card__content">
          <span class="episode-card__date">May 29, 2025</span>
          <h3 class="episode-card__title">Harsh Self-Judgment & Low Self-Esteem | ACA Traits #11 & 12 Explored</h3>
          <p class="episode-card__description">
            In this powerful episode of our ACA Traits Explored series, Billy and Stephen explore Traits #11 and #12 from the ACA Laundry List. Discover how harsh self-judgment and low self-esteem develop in dysfunctional family systems and learn practical strategies for healing.
          </p>
          <div class="episode-card__platforms">
            <a href="https://www.youtube.com/watch?v=eBYX_NtjCUA&list=PLxmZBzOL_rhFAsOsnBUsyogSIKdyqE7D-&index=3" class="platform-link youtube" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
              Watch on YouTube
            </a>
            <a href="https://open.spotify.com/episode/7tXLWEMLMrCSKExZ0mqsmS?si=EcXDLuKITv-9yBBjiUj8BA" class="platform-link spotify" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
              </svg>
              Listen on Spotify
            </a>
            <a href="https://podcasts.apple.com/us/podcast/harsh-self-judgment-low-self-esteem-aca-traits-11-12/id1802646094?i=1000710465171" class="platform-link apple" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
              </svg>
              Listen on Apple
            </a>
          </div>
        </div>
      </article>

      <!-- Episode 4 -->
      <article class="episode-card">
        <div class="episode-card__image">
          <img src="https://is1-ssl.mzstatic.com/image/thumb/PodcastSource221/v4/c6/e2/4f/c6e24f0c-b867-e433-75b4-e941538c4067/c95f282b-2483-4ec9-bb59-7aad458ab867.png/470x470bb.jpg" alt="Feeling Different & Struggling with Intimacy | ACA Traits #9 & 10 Explored" loading="lazy" />
        </div>
        <div class="episode-card__content">
          <span class="episode-card__date">May 15, 2025</span>
          <h3 class="episode-card__title">Feeling Different & Struggling with Intimacy | ACA Traits #9 & 10 Explored</h3>
          <p class="episode-card__description">
            In this episode of our ACA Traits Explored series, Stephen and Billy dive into Trait #9: "We became isolated and afraid of people and authority figures" and Trait #10: "We became addicted to excitement." These traits often develop in households affected by addiction or dysfunction.
          </p>
          <div class="episode-card__platforms">
            <a href="https://www.youtube.com/@WilliamsvilleTV/podcasts" class="platform-link youtube" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
              Watch on YouTube
            </a>
            <a href="https://open.spotify.com/show/0LgUxWnqwJqw0t0ar9MaJu?si=89a0617c03df4eb1" class="platform-link spotify" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
              </svg>
              Listen on Spotify
            </a>
            <a href="https://podcasts.apple.com/us/podcast/williamsville-wellness-podcast/id1802646094" class="platform-link apple" target="_blank" rel="noopener">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
              </svg>
              Listen on Apple
            </a>
          </div>
        </div>
      </article>
    </div>

    <div class="episodes-cta">
      <div id="viewAllEpisodes">
        <a href="https://podcasts.apple.com/us/podcast/williamsville-wellness-podcast/id1802646094" target="_blank" rel="noopener" class="btn btn--primary">View All Episodes</a>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="section section--alt">
  <div class="container">
    <div class="cta">
      <h2 class="cta__title">Ready to Begin Your Recovery Journey?</h2>
      <p class="cta__text">
        Whether you're seeking support for yourself or a loved one, our compassionate team is here to help you on your path to wellness. Contact us to learn more about our comprehensive treatment programs and how we can support your recovery journey.
      </p>
      <div class="cta__buttons">
        <a href="/contact/" class="btn btn--primary">Start Your Recovery Journey</a>
        <a href="tel:8046550094" class="btn btn--primary">(*************</a>
      </div>
    </div>
  </div>
</section>

<!-- Custom CSS for Podcast Page -->
<style>
  /* Platform Selector */
  .platform-selector {
    margin-top: 2rem;
    text-align: center;
  }

  .platform-selector__label {
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
  }

  .platform-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
  }

  .platform-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1rem;
    border: 2px solid rgba(168,118,62,0.4);
    background: white;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-dark);
  }

  .platform-btn:hover,
  .platform-btn.active {
    border-color: var(--accent);
    background: var(--accent);
    color: white;
  }

  .platform-btn svg {
    width: 16px;
    height: 16px;
  }

  /* Episodes Grid */
  .episodes-grid {
    display: grid;
    gap: 2rem;
    margin-top: 3rem;
  }

  .episode-card {
    background: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all 0.3s ease;
    display: grid;
    grid-template-columns: 200px 1fr;
    align-items: center;
    gap: 0;
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
  }

  .episode-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(6,80,42,0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: var(--radius-md);
  }

  .episode-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: rgba(6,80,42,0.1);
  }

  .episode-card:hover::before {
    opacity: 1;
  }

  .episode-card__image {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    width: 200px;
    height: 200px;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border: 3px solid rgba(255,255,255,0.8);
    margin-left: 0.75rem;
    margin-right: 3.75rem;
  }

  .episode-card__image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(6,80,42,0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .episode-card:hover .episode-card__image {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.25);
    border-color: rgba(168,118,62,0.3);
  }

  .episode-card:hover .episode-card__image::after {
    opacity: 1;
  }

  .episode-card__image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
    border-radius: 8px;
  }

  .episode-card:hover .episode-card__image img {
    transform: scale(1.05);
  }

  .episode-thumbnail {
    color: white;
    opacity: 0.8;
  }

  .episode-card__content {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .episode-card__date {
    font-size: 0.9rem;
    color: var(--accent);
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .episode-card__title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary);
    margin-bottom: 0.75rem;
    line-height: 1.3;
  }

  .episode-card__description {
    color: var(--text-light);
    margin-bottom: 1rem;
    line-height: 1.6;
    flex-grow: 1;
  }

  .episode-card__platforms {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
  }

  .platform-link {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid transparent;
  }

  .platform-link.youtube {
    background: #ff000010;
    color: #ff0000;
    border-color: #ff000020;
  }

  .platform-link.youtube:hover {
    background: #ff0000;
    color: white;
  }

  .platform-link.spotify {
    background: #1db95410;
    color: #1db954;
    border-color: #1db95420;
  }

  .platform-link.spotify:hover {
    background: #1db954;
    color: white;
  }

  .platform-link.apple {
    background: #a855f710;
    color: #a855f7;
    border-color: #a855f720;
  }

  .platform-link.apple:hover {
    background: #a855f7;
    color: white;
  }

  .platform-link svg {
    width: 14px;
    height: 14px;
  }

  .episodes-cta {
    text-align: center;
    margin-top: 3rem;
  }

  .view-all-platforms {
    text-align: center;
  }

  .view-all-label {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
  }

  .view-all-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
  }

  .view-all-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    padding: 0.8rem 1.2rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    min-width: 140px;
    justify-content: center;
  }

  .view-all-btn svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  /* Subscribe Section */
  .subscribe-section {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }

  .subscribe-section__title {
    color: var(--primary);
    font-size: clamp(1.8rem, 3vw, 2.3rem);
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .subscribe-section__description {
    margin-bottom: 2.5rem;
    line-height: 1.7;
  }

  .subscribe-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .subscribe-btn {
    display: flex;
    align-items: center;
    gap: 0.7rem;
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
  }

  .subscribe-btn.youtube {
    background: #ff0000;
    color: white;
  }

  .subscribe-btn.youtube:hover {
    background: #cc0000;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  .subscribe-btn.spotify {
    background: #1db954;
    color: white;
  }

  .subscribe-btn.spotify:hover {
    background: #1aa34a;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  .subscribe-btn.apple {
    background: #a855f7;
    color: white;
  }

  .subscribe-btn.apple:hover {
    background: #9333ea;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  /* CTA Section */
  .cta {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: var(--white);
    padding: 4rem 2rem;
    border-radius: var(--radius-md);
    text-align: center;
    box-shadow: var(--shadow-lg);
  }

  .cta__title {
    font-size: clamp(1.8rem, 3vw, 2.5rem);
    font-weight: 700;
    margin-bottom: 1rem;
    color: #fff;
  }

  .cta__text {
    max-width: 700px;
    margin: 0 auto 2rem;
    font-size: 1.1rem;
  }

  .cta__buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  /* Responsive Design */
  @media (max-width: 992px) {
    .episode-card {
      grid-template-columns: 150px 1fr;
    }

    .episode-card__image {
      width: 150px;
      height: 150px;
      border-radius: 10px;
      box-shadow: 0 6px 20px rgba(0,0,0,0.12);
      margin-left: 1rem;
      margin-right: 1.5rem;
    }

    .episode-card:hover .episode-card__image {
      box-shadow: 0 12px 30px rgba(0,0,0,0.2);
    }

    .episode-card__image img {
      border-radius: 7px;
    }

    .episode-card__platforms {
      justify-content: center;
    }
  }

  @media (max-width: 768px) {
    .episode-card {
      grid-template-columns: 1fr;
      text-align: center;
    }

    .episode-card__image {
      width: 100%;
      height: 250px;
      max-width: 250px;
      margin: 1.5rem auto 0;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.15);
      border: 4px solid rgba(255,255,255,0.9);
    }

    .episode-card:hover .episode-card__image {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0,0,0,0.25);
    }

    .episode-card__image img {
      border-radius: 12px;
    }

    .episode-card__content {
      padding: 1.5rem;
    }

    .platform-buttons {
      justify-content: center;
    }

    .subscribe-buttons {
      flex-direction: column;
      align-items: center;
    }

    .subscribe-btn {
      width: 100%;
      max-width: 300px;
      justify-content: center;
    }

    .view-all-platforms {
      text-align: center;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .view-all-label {
      text-align: center;
    }

    .view-all-buttons {
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      max-width: 300px;
      margin: 0 auto;
      text-align: center;
    }

    .view-all-btn {
      width: 100%;
      max-width: 280px;
      justify-content: center;
      padding: 1rem 1.5rem;
      font-size: 1rem;
      border-radius: 30px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .view-all-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
  }

  @media (max-width: 576px) {
    .episode-card__platforms {
      justify-content: center;
    }

    .cta__buttons {
      flex-direction: column;
      align-items: center;
      width: 100%;
    }

    .cta__buttons .btn {
      width: 100%;
      max-width: 300px;
    }

    .view-all-label {
      font-size: 0.95rem;
      margin-bottom: 1.25rem;
    }

    .view-all-btn {
      font-size: 0.95rem;
      padding: 0.9rem 1.2rem;
      max-width: 260px;
    }

    .episode-card__image {
      max-width: 220px;
      height: 220px;
      margin-top: 1.25rem;
    }
  }
</style>

<!-- JavaScript for Platform Filtering -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const platformButtons = document.querySelectorAll('.platform-btn');
    const episodeCards = document.querySelectorAll('.episode-card');
    const viewAllContainer = document.getElementById('viewAllEpisodes');

    function updateViewAllButton(selectedPlatform) {
      if (selectedPlatform === 'all') {
        // Show all platform buttons when "All Platforms" is selected
        viewAllContainer.innerHTML = `
                        <div class="view-all-platforms">
                            <p class="view-all-label">View all episodes on:</p>
                            <div class="view-all-buttons">
                                <a href="https://www.youtube.com/@WilliamsvilleTV/podcasts" target="_blank" rel="noopener" class="btn btn--secondary view-all-btn">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                                    </svg>
                                    YouTube
                                </a>
                                <a href="https://open.spotify.com/show/0LgUxWnqwJqw0t0ar9MaJu?si=89a0617c03df4eb1" target="_blank" rel="noopener" class="btn btn--secondary view-all-btn">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
                                    </svg>
                                    Spotify
                                </a>
                                <a href="https://podcasts.apple.com/us/podcast/williamsville-wellness-podcast/id1802646094" target="_blank" rel="noopener" class="btn btn--secondary view-all-btn">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                                    </svg>
                                    Apple Podcasts
                                </a>
                            </div>
                        </div>
                    `;
      } else {
        // Show single platform button
        let url, text, icon;

        switch(selectedPlatform) {
          case 'youtube':
            url = 'https://www.youtube.com/@WilliamsvilleTV/podcasts';
            text = 'View All on YouTube';
            icon = '<path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>';
            break;
          case 'spotify':
            url = 'https://open.spotify.com/show/0LgUxWnqwJqw0t0ar9MaJu?si=89a0617c03df4eb1';
            text = 'View All on Spotify';
            icon = '<path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>';
            break;
          case 'apple':
            url = 'https://podcasts.apple.com/us/podcast/williamsville-wellness-podcast/id1802646094';
            text = 'View All on Apple Podcasts';
            icon = '<path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>';
            break;
        }

        viewAllContainer.innerHTML = `
                        <a href="${url}" target="_blank" rel="noopener" class="btn btn--primary">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                                ${icon}
                            </svg>
                            ${text}
                        </a>
                    `;
      }
    }

    platformButtons.forEach(button => {
      button.addEventListener('click', () => {
        // Remove active class from all buttons
        platformButtons.forEach(btn => btn.classList.remove('active'));

        // Add active class to clicked button
        button.classList.add('active');

        // Get selected platform
        const selectedPlatform = button.getAttribute('data-platform');

        // Update the View All Episodes button
        updateViewAllButton(selectedPlatform);

        // Show/hide platform links based on selection
        if (selectedPlatform === 'all') {
          // Show all platform links
          document.querySelectorAll('.platform-link').forEach(link => {
            link.style.display = 'flex';
          });
        } else {
          // Hide all links first
          document.querySelectorAll('.platform-link').forEach(link => {
            link.style.display = 'none';
          });

          // Show only selected platform links
          document.querySelectorAll(`.platform-link.${selectedPlatform}`).forEach(link => {
            link.style.display = 'flex';
          });
        }
      });
    });

    // Initialize with "all" platform selected
    updateViewAllButton('all');
  });
</script>
</body>
</html>