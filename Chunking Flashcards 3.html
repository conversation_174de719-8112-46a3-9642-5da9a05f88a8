<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Chunking Flashcard System</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lobster&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html, body {
      width: 100%;
      max-width: 100%;
      overflow-x: hidden;
    }

    :root {
      --primary-color: #14b8a6;
      --secondary-color: #ec4899;
      --accent-color: #8b5cf6;
      --danger-color: #ef4444;
      --danger-bg: #fef2f2;
      --danger-hover: #fecaca;
      --warning-color: #f59e0b;
      --success-color: var(--primary-color);
      --bg-color: #f8fafc;
      --card-bg: #ffffff;
      --text-color: #1e293b;
      --border-color: #e2e8f0;
      --shadow: 0 4px 20px rgba(20, 184, 166, 0.1);
      --gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    }

    [data-theme="dark"] {
      --bg-color: #0f172a;
      --card-bg: #1e293b;
      --text-color: #ffffff;
      --border-color: #334155;
      --shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      --danger-bg: #2d1b1b;
      --danger-hover: #3d2525;
    }

    body {
      font-family: var(--card-font, 'Inter', sans-serif);
      background: var(--bg-color);
      color: var(--text-color);
      line-height: 1.6;
      transition: all 0.3s ease;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    /* Authentication Screen */
    #auth-screen {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background: var(--gradient);
    }

    .auth-container {
      background: var(--card-bg);
      padding: 40px;
      border-radius: 20px;
      box-shadow: var(--shadow);
      width: 100%;
      max-width: 400px;
      text-align: center;
    }

    .auth-tabs {
      display: flex;
      margin-bottom: 30px;
      border-radius: 10px;
      overflow: hidden;
      background: var(--bg-color);
    }

    .auth-tab {
      flex: 1;
      padding: 12px;
      background: transparent;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      color: var(--text-color);
    }

    .auth-tab.active {
      background: var(--primary-color);
      color: white;
    }

    .auth-form {
      display: none;
    }

    .auth-form.active {
      display: block;
    }

    .form-group {
      margin-bottom: 20px;
      text-align: left;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: var(--text-color);
    }

    .form-control {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid var(--border-color);
      border-radius: 10px;
      font-size: 16px;
      transition: all 0.3s ease;
      background: var(--card-bg);
      color: var(--text-color);
    }

    .form-control:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-block;
      text-align: center;
    }

    .btn-primary {
      background: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background: #0f9488;
      transform: translateY(-2px);
    }

    .btn-secondary {
      background: var(--border-color);
      color: var(--text-color);
    }

    .btn-secondary:hover {
      background: #cbd5e1;
    }

    .auth-error {
      background: var(--danger-bg);
      color: var(--danger-color);
      padding: 12px;
      border-radius: 8px;
      margin-bottom: 20px;
      display: none;
    }

    /* Main App */
    #main-app {
      display: none;
      min-height: 100vh;
    }

    .header {
      background: var(--card-bg);
      padding: 20px 0;
      box-shadow: var(--shadow);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    .logo {
      font-family: 'Lobster', cursive;
      font-size: 2em;
      background: var(--gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .nav-tabs {
      display: flex;
      gap: 10px;
      margin: 20px 0;
      flex-wrap: wrap;
    }

    .nav-tab {
      padding: 12px 20px;
      background: var(--card-bg);
      border: 2px solid var(--border-color);
      border-radius: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
    }

    .nav-tab.active {
      background: var(--primary-color);
      color: white;
      border-color: var(--primary-color);
    }

    .tab-content {
      display: none;
      padding: 20px 0;
    }

    .tab-content.active {
      display: block;
    }

    /* Cards and Decks */
    .deck-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .deck-card {
      background: var(--card-bg);
      padding: 25px;
      border-radius: 15px;
      box-shadow: var(--shadow);
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
    }

    .deck-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(20, 184, 166, 0.15);
    }

    .delete-btn {
      position: absolute;
      top: 15px;
      right: 15px;
      background: var(--danger-color);
      color: white;
      border: none;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      cursor: pointer;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    .delete-btn:hover {
      background: #dc2626;
      transform: scale(1.1);
    }

    /* Modal */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
      background-color: var(--card-bg);
      margin: 5% auto;
      padding: 30px;
      border-radius: 15px;
      width: 90%;
      max-width: 500px;
      position: relative;
      box-shadow: var(--shadow);
    }

    .close {
      position: absolute;
      right: 20px;
      top: 15px;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
      color: var(--text-color);
    }

    .close:hover {
      color: var(--danger-color);
    }

    /* Responsive */
    @media (max-width: 768px) {
      .container {
        padding: 10px;
      }
      
      .auth-container {
        padding: 20px;
        margin: 20px;
      }
      
      .deck-grid {
        grid-template-columns: 1fr;
      }
      
      .nav-tabs {
        justify-content: center;
      }
      
      .nav-tab {
        flex: 1;
        text-align: center;
        min-width: 120px;
      }
    }
  </style>
</head>
<body>

<!-- Authentication Screen -->
<div id="auth-screen">
  <div class="auth-container">
    <h1 class="logo">🧠 Flashcards</h1>
    <p style="margin-bottom: 30px; color: var(--text-color); opacity: 0.8;">Smart spaced repetition learning</p>
    
    <div id="auth-error" class="auth-error"></div>
    
    <div class="auth-tabs">
      <button class="auth-tab active" id="login-tab">Sign In</button>
      <button class="auth-tab" id="signup-tab">Sign Up</button>
    </div>

    <!-- Login Form -->
    <form id="login-form" class="auth-form active">
      <div class="form-group">
        <label for="login-email">Email:</label>
        <input type="email" id="login-email" class="form-control" required>
      </div>
      <div class="form-group">
        <label for="login-password">Password:</label>
        <input type="password" id="login-password" class="form-control" required>
      </div>
      <button type="submit" class="btn btn-primary" style="width: 100%; margin-bottom: 15px;">Sign In</button>
    </form>

    <!-- Signup Form -->
    <form id="signup-form" class="auth-form">
      <div class="form-group">
        <label for="signup-name">Name:</label>
        <input type="text" id="signup-name" class="form-control" required>
      </div>
      <div class="form-group">
        <label for="signup-email">Email:</label>
        <input type="email" id="signup-email" class="form-control" required>
      </div>
      <div class="form-group">
        <label for="signup-password">Password:</label>
        <input type="password" id="signup-password" class="form-control" required>
      </div>
      <div class="form-group">
        <label for="signup-confirm">Confirm Password:</label>
        <input type="password" id="signup-confirm" class="form-control" required>
      </div>
      <button type="submit" class="btn btn-primary" style="width: 100%; margin-bottom: 15px;">Create Account</button>
    </form>

    <button id="guest-btn" class="btn btn-secondary" style="width: 100%;">Continue as Guest</button>
  </div>
</div>

<!-- Main Application -->
<div id="main-app">
  <header class="header">
    <div class="header-content">
      <h1 class="logo">🧠 Flashcards</h1>
      <div class="user-info">
        <span id="user-name">User</span>
        <button id="sign-out-btn" class="btn btn-secondary">Sign Out</button>
      </div>
    </div>
  </header>

  <div class="container">
    <!-- Navigation Tabs -->
    <div class="nav-tabs">
      <button class="nav-tab active" data-tab="decks">My Decks</button>
      <button class="nav-tab" data-tab="study">Study</button>
      <button class="nav-tab" data-tab="exams">🎯 Mock Exams</button>
      <button class="nav-tab" data-tab="analytics">Analytics</button>
      <button class="nav-tab" data-tab="import">Import Cards</button>
      <button class="nav-tab" data-tab="manage">Manage Cards</button>
      <button class="nav-tab" data-tab="settings">Settings</button>
    </div>

    <!-- Decks Tab -->
    <div id="decks" class="tab-content active">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
        <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">📚 My Decks</h2>
        <button id="create-deck-btn" class="btn btn-primary">✨ Create Deck</button>
      </div>
      <div id="deck-grid" class="deck-grid">
        <p style="text-align: center; color: var(--text-color); opacity: 0.7; grid-column: 1 / -1;">No decks yet. Create your first deck to get started!</p>
      </div>
    </div>

    <!-- Other tabs will be added here -->
    <div id="study" class="tab-content">
      <h2>📚 Study Session</h2>
      <p>Study functionality will be implemented here.</p>
    </div>

    <div id="exams" class="tab-content">
      <h2>🎯 Mock Exams</h2>
      <p>Mock exam functionality will be implemented here.</p>
    </div>

    <div id="analytics" class="tab-content">
      <h2>📊 Analytics</h2>
      <p>Analytics will be shown here.</p>
    </div>

    <div id="import" class="tab-content">
      <h2>📥 Import Cards</h2>
      <p>Import functionality will be implemented here.</p>
    </div>

    <div id="manage" class="tab-content">
      <h2>🗂️ Manage Cards</h2>
      <p>Card management will be implemented here.</p>
    </div>

    <div id="settings" class="tab-content">
      <h2>⚙️ Settings</h2>
      <p>Settings will be implemented here.</p>
    </div>
  </div>
</div>

<!-- Create Deck Modal -->
<div id="create-deck-modal" class="modal">
  <div class="modal-content">
    <span class="close" id="close-modal">&times;</span>
    <h3 style="color: var(--primary-color); margin-bottom: 25px;">✨ Create New Deck</h3>
    <div class="form-group">
      <label for="deck-name">Deck Name:</label>
      <input type="text" id="deck-name" class="form-control" placeholder="e.g., Biology Chapter 5">
    </div>
    <div class="form-group">
      <label for="deck-description">Description (optional):</label>
      <textarea id="deck-description" class="form-control" rows="3" placeholder="Brief description of the deck content"></textarea>
    </div>
    <button id="create-deck-confirm-btn" class="btn btn-primary" style="width: 100%;">🚀 Create Deck</button>
  </div>
</div>

<!-- Firebase SDK -->
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>

<script>
// Firebase Configuration - Chunking Flashcards Project
const firebaseConfig = {
  apiKey: "AIzaSyDsIw2-nh1JrRI10e4AfMnd4dAWdQ33K1M",
  authDomain: "chunking-flashcards.firebaseapp.com",
  projectId: "chunking-flashcards",
  storageBucket: "chunking-flashcards.firebasestorage.app",
  messagingSenderId: "949621433092",
  appId: "1:949621433092:web:0fc00e67a712cad251a651",
  measurementId: "G-R68E9TNGLH"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const db = firebase.firestore();

// App State
const app = {
  currentUser: null,
  decks: [],
  cards: []
};

// Authentication Functions
function showAuthScreen() {
  document.getElementById('auth-screen').style.display = 'flex';
  document.getElementById('main-app').style.display = 'none';
}

function showMainApp() {
  document.getElementById('auth-screen').style.display = 'none';
  document.getElementById('main-app').style.display = 'block';
  document.getElementById('user-name').textContent = app.currentUser.displayName || app.currentUser.email || 'User';
  loadUserData();
}

function showAuthError(message) {
  const errorDiv = document.getElementById('auth-error');
  errorDiv.textContent = message;
  errorDiv.style.display = 'block';
  setTimeout(() => {
    errorDiv.style.display = 'none';
  }, 5000);
}

// Firebase Auth State Listener
auth.onAuthStateChanged(user => {
  if (user) {
    console.log('User signed in:', user.email);
    app.currentUser = user;
    showMainApp();
  } else {
    console.log('No user signed in');
    app.currentUser = null;
    showAuthScreen();
  }
});

// Authentication Event Handlers
document.getElementById('login-form').addEventListener('submit', async (e) => {
  e.preventDefault();
  const email = document.getElementById('login-email').value;
  const password = document.getElementById('login-password').value;

  try {
    await auth.signInWithEmailAndPassword(email, password);
  } catch (error) {
    console.error('Login error:', error);
    showAuthError(getAuthErrorMessage(error.code));
  }
});

document.getElementById('signup-form').addEventListener('submit', async (e) => {
  e.preventDefault();
  const name = document.getElementById('signup-name').value;
  const email = document.getElementById('signup-email').value;
  const password = document.getElementById('signup-password').value;
  const confirmPassword = document.getElementById('signup-confirm').value;

  if (password !== confirmPassword) {
    showAuthError('Passwords do not match');
    return;
  }

  if (password.length < 6) {
    showAuthError('Password must be at least 6 characters');
    return;
  }

  try {
    const userCredential = await auth.createUserWithEmailAndPassword(email, password);
    await userCredential.user.updateProfile({ displayName: name });

    // Create user document
    await db.collection('users').doc(userCredential.user.uid).set({
      name: name,
      email: email,
      createdAt: firebase.firestore.FieldValue.serverTimestamp()
    });
  } catch (error) {
    console.error('Signup error:', error);
    showAuthError(getAuthErrorMessage(error.code));
  }
});

document.getElementById('guest-btn').addEventListener('click', () => {
  app.currentUser = { uid: 'guest', displayName: 'Guest User', email: 'guest@local' };
  showMainApp();
});

document.getElementById('sign-out-btn').addEventListener('click', async () => {
  try {
    if (app.currentUser.uid !== 'guest') {
      await auth.signOut();
    } else {
      app.currentUser = null;
      showAuthScreen();
    }
  } catch (error) {
    console.error('Sign out error:', error);
  }
});

// Auth Tab Switching
document.getElementById('login-tab').addEventListener('click', () => {
  document.getElementById('login-tab').classList.add('active');
  document.getElementById('signup-tab').classList.remove('active');
  document.getElementById('login-form').classList.add('active');
  document.getElementById('signup-form').classList.remove('active');
});

document.getElementById('signup-tab').addEventListener('click', () => {
  document.getElementById('signup-tab').classList.add('active');
  document.getElementById('login-tab').classList.remove('active');
  document.getElementById('signup-form').classList.add('active');
  document.getElementById('login-form').classList.remove('active');
});

function getAuthErrorMessage(errorCode) {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'No account found with this email. Please sign up first.';
    case 'auth/wrong-password':
    case 'auth/invalid-login-credentials':
      return 'Invalid email or password. Please check your credentials.';
    case 'auth/email-already-in-use':
      return 'An account with this email already exists. Please sign in instead.';
    case 'auth/weak-password':
      return 'Password is too weak. Please choose a stronger password.';
    case 'auth/invalid-email':
      return 'Invalid email address. Please check your email.';
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later.';
    case 'auth/network-request-failed':
      return 'Network error. Please check your connection.';
    default:
      return 'Authentication failed. Please try again.';
  }
}

// Data Loading Functions
async function loadUserData() {
  console.log('Loading user data for:', app.currentUser.uid);

  if (app.currentUser.uid === 'guest') {
    loadLocalData();
  } else {
    await loadFirebaseData();
  }

  console.log('Final data loaded - Decks:', app.decks.length, 'Cards:', app.cards.length);
  renderDecks();
}

function loadLocalData() {
  // Use the same localStorage keys as your existing app
  app.decks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
  app.cards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
  console.log('Loaded from localStorage - Decks:', app.decks.length, 'Cards:', app.cards.length);
}

async function loadFirebaseData() {
  try {
    console.log('Loading Firebase data for user:', app.currentUser.uid);

    // Try to load decks - use the same collection names as your working app
    try {
      const decksSnapshot = await db.collection('chunking_decks')
        .where('userId', '==', app.currentUser.uid)
        .get();

      app.decks = [];
      decksSnapshot.forEach(doc => {
        app.decks.push({ id: doc.id, ...doc.data() });
      });
      console.log('Loaded decks:', app.decks.length);
    } catch (deckError) {
      console.warn('Error loading decks, trying fallback:', deckError);
      // Fallback to localStorage if Firestore fails
      app.decks = JSON.parse(localStorage.getItem('chunking_decks') || '[]')
        .filter(deck => deck.userId === app.currentUser.uid);
    }

    // Try to load cards
    try {
      const cardsSnapshot = await db.collection('chunking_cards')
        .where('userId', '==', app.currentUser.uid)
        .get();

      app.cards = [];
      cardsSnapshot.forEach(doc => {
        app.cards.push({ id: doc.id, ...doc.data() });
      });
      console.log('Loaded cards:', app.cards.length);
    } catch (cardError) {
      console.warn('Error loading cards, trying fallback:', cardError);
      // Fallback to localStorage if Firestore fails
      app.cards = JSON.parse(localStorage.getItem('chunking_cards') || '[]')
        .filter(card => card.userId === app.currentUser.uid);
    }
  } catch (error) {
    console.error('Error loading data:', error);
    // Complete fallback to localStorage
    loadLocalData();
  }
}

// Deck Management
function renderDecks() {
  const grid = document.getElementById('deck-grid');

  if (app.decks.length === 0) {
    grid.innerHTML = '<p style="text-align: center; color: var(--text-color); opacity: 0.7; grid-column: 1 / -1;">No decks yet. Create your first deck to get started!</p>';
    return;
  }

  grid.innerHTML = app.decks.map(deck => {
    const cardCount = app.cards.filter(card => card.deckId === deck.id).length;
    return `
      <div class="deck-card" data-deck-id="${deck.id}">
        <button class="delete-btn" onclick="deleteDeck('${deck.id}')" title="Delete deck">✕</button>
        <h3 style="color: var(--primary-color); margin-bottom: 10px; padding-right: 40px;">${deck.name}</h3>
        <p style="color: var(--text-color); margin-bottom: 20px; opacity: 0.8; min-height: 40px;">${deck.description || 'No description'}</p>
        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.9em; color: var(--text-color); opacity: 0.7;">
          <span>📚 ${cardCount} cards</span>
          <span>📅 Created ${new Date(deck.created?.toDate?.() || deck.created).toLocaleDateString()}</span>
        </div>
      </div>
    `;
  }).join('');
}

async function createDeck() {
  const name = document.getElementById('deck-name').value.trim();
  const description = document.getElementById('deck-description').value.trim();

  if (!name) {
    alert('Please enter a deck name');
    return;
  }

  const deckData = {
    name: name,
    description: description,
    created: new Date(),
    userId: app.currentUser.uid
  };

  try {
    if (app.currentUser.uid === 'guest') {
      // Save to localStorage
      deckData.id = 'local_' + Date.now();
      app.decks.push(deckData);
      localStorage.setItem('chunking_decks', JSON.stringify(app.decks));
    } else {
      // Try to save to Firebase first
      try {
        deckData.created = firebase.firestore.FieldValue.serverTimestamp();
        const docRef = await db.collection('chunking_decks').add(deckData);
        deckData.id = docRef.id;
        app.decks.push(deckData);
        console.log('Deck saved to Firebase:', deckData.id);
      } catch (firebaseError) {
        console.warn('Firebase save failed, using localStorage:', firebaseError);
        // Fallback to localStorage if Firebase fails
        deckData.id = 'local_' + Date.now();
        deckData.created = new Date().toISOString();
        app.decks.push(deckData);

        // Save to localStorage as backup
        const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
        localDecks.push(deckData);
        localStorage.setItem('chunking_decks', JSON.stringify(localDecks));
      }
    }

    // Clear form and close modal
    document.getElementById('deck-name').value = '';
    document.getElementById('deck-description').value = '';
    document.getElementById('create-deck-modal').style.display = 'none';

    renderDecks();
    alert('Deck created successfully! 🎉');
  } catch (error) {
    console.error('Error creating deck:', error);
    alert('Error creating deck. Please try again.');
  }
}

async function deleteDeck(deckId) {
  const deck = app.decks.find(d => d.id === deckId);
  if (!deck) return;

  const cardCount = app.cards.filter(card => card.deckId === deckId).length;
  const message = cardCount > 0
    ? `Are you sure you want to delete "${deck.name}"?\n\nThis will permanently delete the deck and all ${cardCount} cards in it.\n\nThis action cannot be undone.`
    : `Are you sure you want to delete "${deck.name}"?\n\nThis action cannot be undone.`;

  if (!confirm(message)) return;

  try {
    if (app.currentUser.uid === 'guest' || deckId.startsWith('local_')) {
      // Delete from localStorage
      app.decks = app.decks.filter(d => d.id !== deckId);
      app.cards = app.cards.filter(c => c.deckId !== deckId);
      localStorage.setItem('chunking_decks', JSON.stringify(app.decks));
      localStorage.setItem('chunking_cards', JSON.stringify(app.cards));
    } else {
      // Try to delete from Firebase
      try {
        await db.collection('chunking_decks').doc(deckId).delete();

        // Delete all cards in the deck
        const cardsToDelete = app.cards.filter(card => card.deckId === deckId);
        for (const card of cardsToDelete) {
          await db.collection('chunking_cards').doc(card.id).delete();
        }

        console.log('Deck deleted from Firebase:', deckId);
      } catch (firebaseError) {
        console.warn('Firebase delete failed, updating localStorage:', firebaseError);
        // Update localStorage as fallback
        const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
        const filteredDecks = localDecks.filter(d => d.id !== deckId);
        localStorage.setItem('chunking_decks', JSON.stringify(filteredDecks));

        const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
        const filteredCards = localCards.filter(c => c.deckId !== deckId);
        localStorage.setItem('chunking_cards', JSON.stringify(filteredCards));
      }

      // Update local arrays
      app.decks = app.decks.filter(d => d.id !== deckId);
      app.cards = app.cards.filter(c => c.deckId !== deckId);
    }

    renderDecks();
    alert('Deck deleted successfully!');
  } catch (error) {
    console.error('Error deleting deck:', error);
    alert('Error deleting deck. Please try again.');
  }
}

// Tab Navigation
document.querySelectorAll('.nav-tab').forEach(tab => {
  tab.addEventListener('click', () => {
    const tabName = tab.getAttribute('data-tab');

    // Update active tab
    document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
    tab.classList.add('active');

    // Update active content
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    document.getElementById(tabName).classList.add('active');
  });
});

// Modal Management
document.getElementById('create-deck-btn').addEventListener('click', () => {
  document.getElementById('create-deck-modal').style.display = 'block';
});

document.getElementById('close-modal').addEventListener('click', () => {
  document.getElementById('create-deck-modal').style.display = 'none';
});

document.getElementById('create-deck-confirm-btn').addEventListener('click', createDeck);

// Close modal when clicking outside
window.addEventListener('click', (e) => {
  const modal = document.getElementById('create-deck-modal');
  if (e.target === modal) {
    modal.style.display = 'none';
  }
});

// Debug function to check existing data
function checkExistingData() {
  console.log('=== CHECKING EXISTING DATA ===');
  console.log('chunking_decks:', localStorage.getItem('chunking_decks'));
  console.log('chunking_cards:', localStorage.getItem('chunking_cards'));
  console.log('chunking_users:', localStorage.getItem('chunking_users'));
  console.log('chunking_user:', localStorage.getItem('chunking_user'));

  // Check all localStorage keys
  const allKeys = [];
  for (let i = 0; i < localStorage.length; i++) {
    allKeys.push(localStorage.key(i));
  }
  console.log('All localStorage keys:', allKeys);
}

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
  console.log('🧠 Flashcard app initialized');
  checkExistingData();
});
</script>
</body>
</html>
