<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Chunking Flashcard System</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lobster&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html, body {
      width: 100%;
      max-width: 100%;
      overflow-x: hidden;
    }

    :root {
      --primary-color: #14b8a6;
      --secondary-color: #ec4899;
      --accent-color: #8b5cf6;
      --danger-color: #ef4444;
      --danger-bg: #fef2f2;
      --danger-hover: #fecaca;
      --warning-color: #f59e0b;
      --success-color: var(--primary-color);
      --bg-color: #f8fafc;
      --card-bg: #ffffff;
      --text-color: #1e293b;
      --border-color: #e2e8f0;
      --shadow: 0 4px 20px rgba(20, 184, 166, 0.1);
      --gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    }

    [data-theme="dark"] {
      --bg-color: #0f172a;
      --card-bg: #1e293b;
      --text-color: #ffffff;
      --border-color: #334155;
      --shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      --danger-bg: #2d1b1b;
      --danger-hover: #3d2525;
    }

    [data-theme="purple"] {
      --primary-color: #8b5cf6;
      --secondary-color: #ec4899;
      --accent-color: #14b8a6;
    }

    [data-theme="pink"] {
      --primary-color: #ec4899;
      --secondary-color: #8b5cf6;
      --accent-color: #14b8a6;
    }

    body {
      font-family: var(--card-font, 'Inter', sans-serif);
      background: var(--bg-color);
      color: var(--text-color);
      transition: all 0.3s ease;
      font-size: var(--font-size, 16px);
      line-height: 1.6;
      min-height: 100vh;
    }

    .auth-container {
      max-width: 450px;
      margin: 50px auto;
      padding: 40px;
      background: var(--card-bg);
      border-radius: 20px;
      box-shadow: var(--shadow);
      text-align: center;
    }

    .auth-container h1 {
      background: var(--gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 30px;
      font-size: 2.5em;
      font-weight: bold;
    }

    .auth-tabs {
      display: flex;
      margin-bottom: 25px;
      border-radius: 15px;
      overflow: hidden;
      border: 2px solid var(--border-color);
    }

    .auth-tab {
      flex: 1;
      padding: 15px;
      background: var(--bg-color);
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
      color: var(--text-color);
    }

    .auth-tab.active {
      background: var(--primary-color);
      color: white;
    }

    .auth-form input {
      width: 100%;
      padding: 15px;
      margin-bottom: 15px;
      border: 2px solid var(--border-color);
      border-radius: 15px;
      background: var(--bg-color);
      color: var(--text-color);
      font-size: 1em;
      transition: all 0.3s ease;
    }

    .auth-form input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
    }

    .auth-error {
      background: var(--danger-bg);
      color: var(--danger-color);
      padding: 12px;
      border-radius: 10px;
      margin-bottom: 15px;
      font-size: 0.9em;
      border-left: 4px solid var(--danger-color);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      text-align: center;
      margin-bottom: 30px;
      padding: 30px;
      background: var(--card-bg);
      border-radius: 20px;
      box-shadow: var(--shadow);
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 5px;
      background: var(--primary-color);
    }

    .header h1 {
      background: var(--gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-size: 2.5em;
      margin-bottom: 10px;
    }

    .user-info {
      position: absolute;
      top: 20px;
      right: 20px;
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 0.9em;
    }

    .nav-tabs {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-bottom: 30px;
      flex-wrap: wrap;
    }

    .nav-tab {
      padding: 15px 25px;
      background: var(--primary-color);
      color: white;
      border: none;
      border-radius: 50px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 15px rgba(20, 184, 166, 0.3);
    }

    .nav-tab:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 25px rgba(20, 184, 166, 0.4);
      opacity: 0.9;
    }

    .nav-tab.active {
      background: var(--accent-color);
      box-shadow: 0 6px 25px rgba(139, 92, 246, 0.4);
    }

    .tab-content {
      display: none;
      background: var(--card-bg);
      padding: 30px;
      border-radius: 20px;
      box-shadow: var(--shadow);
      border: 1px solid var(--border-color);
    }

    .tab-content.active {
      display: block;
    }

    .deck-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 25px;
      margin-top: 25px;
    }

    .deck-card {
      background: var(--card-bg);
      border: 2px solid transparent;
      border-radius: 20px;
      padding: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      box-shadow: var(--shadow);
      overflow: hidden;
    }

    .deck-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--primary-color);
    }

    .deck-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(20, 184, 166, 0.2);
      border-color: var(--primary-color);
    }

    .flashcard-container {
      max-width: 700px;
      margin: 0 auto;
      text-align: center;
    }

    .progress-bars {
      margin-bottom: 25px;
      background: var(--bg-color);
      padding: 20px;
      border-radius: 15px;
    }

    .progress-bar {
      background: var(--border-color);
      height: 12px;
      border-radius: 10px;
      margin-bottom: 15px;
      overflow: hidden;
      position: relative;
    }

    .progress-fill {
      height: 100%;
      background: var(--primary-color);
      transition: width 0.5s ease;
      border-radius: 10px;
    }

    .flashcard {
      background: var(--card-background, var(--card-bg));
      border: 3px solid var(--border-color);
      border-radius: 25px;
      padding: 50px;
      margin: 25px 0;
      min-height: var(--card-height, 250px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      font-size: var(--card-font-size, 1.3em);
      line-height: 1.6;
      color: var(--card-text-color, inherit);
      font-family: var(--card-font, inherit);
      box-shadow: var(--shadow);
    }

    .flashcard:hover {
      border-color: var(--primary-color);
      transform: translateY(-3px);
      box-shadow: 0 8px 30px rgba(20, 184, 166, 0.2);
    }

    .flashcard.flipped {
      background: #f0f9ff;
      border-color: var(--secondary-color);
    }

    [data-theme="dark"] .flashcard.flipped {
      background: #1e3a8a;
      border-color: var(--secondary-color);
    }

    .flashcard img, .flashcard video, .flashcard audio {
      max-width: 100%;
      max-height: 200px;
      margin: 10px 0;
      border-radius: 10px;
    }

    .confidence-indicator {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 3px solid #fff;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    }

    .confidence-high { background: var(--success-color); }
    .confidence-medium { background: var(--warning-color); }
    .confidence-low { background: var(--danger-color); }

    .btn {
      padding: 15px 30px;
      border: none;
      border-radius: 50px;
      cursor: pointer;
      font-size: 1.1em;
      transition: all 0.3s ease;
      font-weight: 600;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .btn-primary {
      background: var(--primary-color);
      color: white;
    }

    .btn-secondary {
      background: var(--secondary-color);
      color: white;
    }

    .btn-danger {
      background: var(--danger-bg);
      color: var(--danger-color);
      border: 1px solid var(--danger-color);
      font-size: 0.9em;
      padding: 8px 16px;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.2);
      opacity: 0.9;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: var(--primary-color);
    }

    .form-control {
      width: 100%;
      padding: 15px;
      border: 2px solid var(--border-color);
      border-radius: 15px;
      background: var(--card-bg);
      color: var(--text-color);
      font-size: 1em;
      transition: all 0.3s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.6);
      z-index: 1000;
      backdrop-filter: blur(5px);
    }

    .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: var(--card-bg);
      padding: 40px;
      border-radius: 25px;
      max-width: 700px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    }

    .close {
      position: absolute;
      top: 15px;
      right: 20px;
      font-size: 28px;
      cursor: pointer;
      color: #666;
      transition: color 0.3s ease;
    }

    .close:hover {
      color: var(--danger-color);
    }

    /* Card Editor Styles */
    #card-editor {
      background: var(--card-background, var(--card-bg));
      color: var(--card-text-color, var(--text-color));
      font-family: var(--card-font, inherit);
      font-size: var(--card-font-size, 1.3em);
      padding: 30px;
      border-radius: 25px;
      border: 3px solid var(--border-color);
      text-align: center;
      margin: 20px 0;
      min-height: var(--card-height, 250px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      transition: all 0.3s ease;
    }

    #card-text-editor {
      width: 90%;
      min-height: 100px;
      outline: none;
      text-align: center;
      border: 2px dashed transparent;
      padding: 10px;
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    #card-text-editor:focus {
      border-color: var(--primary-color);
      background: rgba(20, 184, 166, 0.05);
    }

    #card-text-editor:empty:before {
      content: attr(placeholder);
      color: #999;
      font-style: italic;
    }

    .editing-front #edit-front-btn {
      background: var(--accent-color);
    }

    .editing-back #edit-back-btn {
      background: var(--accent-color);
    }

    .media-preview {
      max-width: 200px;
      max-height: 150px;
      border-radius: 5px;
      margin-top: 10px;
    }

    .color-picker-group {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin-top: 10px;
    }

    .color-picker {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .color-picker input[type="color"] {
      width: 50px;
      height: 40px;
      border: none;
      border-radius: 10px;
      cursor: pointer;
    }

    .settings-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 25px;
    }

    .settings-card {
      background: var(--bg-color);
      padding: 25px;
      border-radius: 15px;
      border: 1px solid var(--border-color);
    }

    .settings-card h3 {
      color: var(--primary-color);
      margin-bottom: 20px;
      font-size: 1.3em;
    }

    .preview-card {
      background: var(--card-background, var(--card-bg));
      color: var(--card-text-color, var(--text-color));
      font-family: var(--card-font, inherit);
      font-size: var(--card-font-size, 1.3em);
      padding: 30px;
      border-radius: 15px;
      border: 2px solid var(--border-color);
      text-align: center;
      margin-top: 15px;
      min-height: var(--card-height, 150px);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .delete-btn {
      position: absolute;
      top: 15px;
      right: 15px;
      background: var(--danger-bg);
      border: 1px solid var(--danger-color);
      color: var(--danger-color);
      cursor: pointer;
      font-size: 1.1em;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      z-index: 10;
    }

    .delete-btn:hover {
      background: var(--danger-hover);
      transform: scale(1.1);
    }

    .deck-card-mini {
      background: var(--card-bg);
      border: 2px solid var(--border-color);
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      box-shadow: var(--shadow);
      min-height: 200px;
      display: flex;
      flex-direction: column;
    }

    .deck-card-mini:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 25px rgba(20, 184, 166, 0.2);
      border-color: var(--primary-color);
    }

    .deck-card-mini .confidence-indicator {
      position: absolute;
      top: 15px;
      right: 15px;
      width: 20px;
      height: 20px;
    }

    .deck-card-mini .card-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      font-size: var(--card-font-size, 1.1em);
      font-family: var(--card-font, inherit);
      color: var(--card-text-color, inherit);
      padding: 10px;
    }

    .deck-card-mini img, .deck-card-mini video, .deck-card-mini audio {
      max-width: 100%;
      max-height: 100px;
      margin: 8px 0;
      border-radius: 8px;
    }

    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin: 10px 0;
    }

    .tag {
      background: var(--primary-color);
      color: white;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.8em;
      font-weight: 500;
    }

    .tag.removable {
      cursor: pointer;
      padding-right: 20px;
      position: relative;
    }

    .tag.removable:after {
      content: '×';
      position: absolute;
      right: 6px;
      top: 50%;
      transform: translateY(-50%);
    }

    .stats-dashboard {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: var(--card-bg);
      padding: 20px;
      border-radius: 15px;
      border: 1px solid var(--border-color);
      text-align: center;
      box-shadow: var(--shadow);
    }

    .stat-value {
      font-size: 2em;
      font-weight: bold;
      color: var(--primary-color);
    }

    .stat-label {
      font-size: 0.9em;
      color: var(--text-color);
      opacity: 0.7;
      margin-top: 5px;
    }

    .chart-container {
      background: var(--card-bg);
      padding: 20px;
      border-radius: 15px;
      border: 1px solid var(--border-color);
      margin: 20px 0;
    }

    .spaced-repetition-indicator {
      position: absolute;
      top: 45px;
      right: 20px;
      background: var(--warning-color);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.7em;
      font-weight: bold;
    }

    .spaced-repetition-indicator.due {
      background: var(--danger-color);
    }

    .spaced-repetition-indicator.mastered {
      background: var(--success-color);
    }

    @media (max-width: 768px) {
      .container {
        padding: 15px;
      }

      .flashcard {
        padding: 25px;
        min-height: 200px;
        font-size: 1.1em;
      }

      .header {
        padding: 20px 15px;
      }

      .user-info {
        position: static;
        margin-top: 15px;
        justify-content: center;
      }

      .deck-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .modal-content {
        width: 95%;
        max-width: 95%;
        margin: 20px auto;
        padding: 20px;
      }

      .settings-grid {
        grid-template-columns: 1fr;
      }
    }

    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOut {
      from { transform: translateX(0); opacity: 1; }
      to { transform: translateX(100%); opacity: 0; }
    }
  </style>
</head>
<body>
<!-- Authentication Screen -->
<div id="auth-screen" class="auth-container">
  <h1>🧠 Enhanced Chunking Flashcards</h1>
  <p style="margin-bottom: 30px; color: var(--text-color); opacity: 0.8;">Master your material with advanced spaced repetition</p>

  <div class="auth-tabs">
    <button class="auth-tab active" id="login-tab">Login</button>
    <button class="auth-tab" id="signup-tab">Sign Up</button>
  </div>

  <div id="auth-error" class="auth-error" style="display: none;"></div>

  <form id="login-form" class="auth-form">
    <input type="email" id="login-email" placeholder="Email address" required>
    <input type="password" id="login-password" placeholder="Password" required>
    <button type="submit" class="btn btn-primary" style="width: 100%; margin-bottom: 15px;">
      🔐 Login
    </button>
  </form>

  <form id="signup-form" class="auth-form" style="display: none;">
    <input type="text" id="signup-name" placeholder="Display name" required>
    <input type="email" id="signup-email" placeholder="Email address" required>
    <input type="password" id="signup-password" placeholder="Password (6+ characters)" required>
    <input type="password" id="signup-confirm" placeholder="Confirm password" required>
    <button type="submit" class="btn btn-primary" style="width: 100%; margin-bottom: 15px;">
      ✨ Create Account
    </button>
  </form>

  <div style="text-align: center; margin-top: 20px;">
    <button id="guest-btn" class="btn btn-secondary" style="width: 100%;">
      👤 Continue as Guest
    </button>
  </div>

  <div id="auth-loading" style="display: none;">
    <div class="loading">Processing...</div>
  </div>
</div>

<!-- Main Application -->
<div id="main-app" style="display: none;">
  <div class="container">
    <div class="header">
      <div class="user-info">
        <span id="user-name">Guest User</span>
        <button id="sign-out-btn" class="btn btn-secondary" style="padding: 8px 15px; font-size: 0.9em;">Sign Out</button>
      </div>
      <h1>🧠 Enhanced Chunking Flashcard System</h1>
      <p style="color: var(--text-color); opacity: 0.8;">Master your material with spaced repetition and analytics</p>
    </div>

    <div class="nav-tabs">
      <button class="nav-tab active" data-tab="decks">My Decks</button>
      <button class="nav-tab" data-tab="study">Study</button>
      <button class="nav-tab" data-tab="exams">🎯 Mock Exams</button>
      <button class="nav-tab" data-tab="analytics">Analytics</button>
      <button class="nav-tab" data-tab="import">Import Cards</button>
      <button class="nav-tab" data-tab="manage">Manage Cards</button>
      <button class="nav-tab" data-tab="settings">Settings</button>
    </div>

    <!-- Decks Tab -->
    <div id="decks" class="tab-content active">
      <div id="deck-list-view">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; flex-wrap: wrap; gap: 15px;">
          <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">My Flashcard Decks</h2>
          <button id="create-deck-btn" class="btn btn-primary">✨ Create New Deck</button>
        </div>
        <div id="deck-grid" class="deck-grid">
          <div class="loading">Loading your decks...</div>
        </div>
      </div>

      <div id="deck-detail-view" style="display: none;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; flex-wrap: wrap; gap: 15px;">
          <button id="back-to-decks-btn" class="btn btn-secondary">← Back to Decks</button>
          <h2 id="deck-title" style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; flex: 1; text-align: center;"></h2>
          <button id="add-card-to-deck-btn" class="btn btn-primary">✏️ Add New Card</button>
        </div>

        <div id="deck-cards-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px;">
          <!-- Cards will be rendered here -->
        </div>
      </div>
    </div>

    <!-- Study Tab -->
    <div id="study" class="tab-content">
      <div id="study-setup" style="text-align: center;">
        <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 30px;">Smart Study Session</h2>

        <div class="stats-dashboard">
          <div class="stat-card">
            <div class="stat-value" id="cards-due">0</div>
            <div class="stat-label">Cards Due Today</div>
          </div>
          <div class="stat-card">
            <div class="stat-value" id="streak-count">0</div>
            <div class="stat-label">Day Streak</div>
          </div>
          <div class="stat-card">
            <div class="stat-value" id="mastered-cards">0</div>
            <div class="stat-label">Mastered Cards</div>
          </div>
        </div>

        <select id="study-deck-select" class="form-control" style="max-width: 400px; margin: 20px auto;">
          <option value="">Choose a deck...</option>
        </select>

        <div style="margin: 30px 0;">
          <h3 style="color: var(--primary-color); margin-bottom: 20px;">Study Mode</h3>
          <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
            <div class="study-mode-card" id="spaced-mode">
              <div style="font-size: 2em; margin-bottom: 10px;">🎯</div>
              <h4 style="color: var(--primary-color); margin-bottom: 10px;">Spaced Repetition</h4>
              <p style="color: var(--text-color); opacity: 0.8; font-size: 0.9em; margin-bottom: 15px;">Study cards based on optimal spacing intervals for maximum retention.</p>
              <button class="btn btn-primary" id="start-spaced-btn" style="width: 100%;">🚀 Start Smart Study</button>
            </div>

            <div class="study-mode-card" id="chunking-mode">
              <div style="font-size: 2em; margin-bottom: 10px;">🧠</div>
              <h4 style="color: var(--primary-color); margin-bottom: 10px;">Chunking Study</h4>
              <p style="color: var(--text-color); opacity: 0.8; font-size: 0.9em; margin-bottom: 15px;">Learn in small chunks of 3-7 cards. Perfect for new material.</p>
              <div style="margin: 15px 0;">
                <label style="color: var(--primary-color); font-weight: 600;">Chunk Size:</label>
                <input type="number" id="chunk-size" class="form-control" value="5" min="1" max="20" style="max-width: 80px; margin: 10px auto;">
              </div>
              <button class="btn btn-primary" id="start-chunking-btn" style="width: 100%;">🧠 Start Chunking</button>
            </div>

            <div class="study-mode-card" id="review-mode">
              <div style="font-size: 2em; margin-bottom: 10px;">📚</div>
              <h4 style="color: var(--secondary-color); margin-bottom: 10px;">Full Review</h4>
              <p style="color: var(--text-color); opacity: 0.8; font-size: 0.9em; margin-bottom: 15px;">Go through all cards continuously. Perfect for final reviews.</p>
              <div style="margin: 15px 0;">
                <label style="font-size: 0.9em; color: var(--text-color); opacity: 0.8;">
                  <input type="checkbox" id="shuffle-cards" style="margin-right: 8px;" checked>
                  Shuffle card order
                </label>
              </div>
              <button class="btn btn-secondary" id="start-review-btn" style="width: 100%;">📖 Start Review</button>
            </div>
          </div>
        </div>
      </div>

      <div id="study-session" style="display: none;">
        <div class="flashcard-container">
          <div class="chunk-info">
            <span id="session-info">Smart Study Session</span> |
            <span id="session-progress">Card 1 of 25</span>
          </div>

          <div class="progress-bars">
            <div class="progress-bar">
              <div id="session-progress-bar" class="progress-fill" style="width: 4%;"></div>
            </div>
          </div>

          <div id="flashcard" class="flashcard">
            <div class="confidence-indicator confidence-low"></div>
            <div class="spaced-repetition-indicator">New</div>
            <div id="card-content">Click to start studying! 🎯</div>
          </div>

          <div class="card-controls" style="display: flex; justify-content: center; gap: 20px; margin-top: 25px; flex-wrap: wrap;">
            <button id="again-btn" class="btn btn-danger" style="display: none;">Again</button>
            <button id="hard-btn" class="btn" style="background: #f59e0b; color: white; display: none;">Hard</button>
            <button id="good-btn" class="btn btn-primary" style="display: none;">Good</button>
            <button id="easy-btn" class="btn btn-primary" style="background: #10b981; display: none;">Easy</button>

            <button id="flip-btn" class="btn btn-primary">🔄 Flip Card (Space)</button>
            <button id="incorrect-btn" class="btn btn-danger" style="display: none;">❌ Incorrect (X)</button>
            <button id="correct-btn" class="btn btn-primary" style="display: none;">✅ Correct (C)</button>
          </div>

          <div style="margin-top: 25px; text-align: center;">
            <button id="end-study-btn" class="btn btn-secondary">End Session</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Mock Exams Tab -->
    <div id="exams" class="tab-content">
      <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">🎯 Mock Exams</h2>

      <!-- Exam Setup View -->
      <div id="exam-setup-view">
        <div class="exam-setup-card" style="background: var(--card-bg); border-radius: 20px; padding: 30px; margin-bottom: 25px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
          <h3 style="color: var(--primary-color); margin-bottom: 20px;">📝 Create Mock Exam</h3>

          <div class="form-group" style="margin-bottom: 20px;">
            <label for="exam-name" style="display: block; margin-bottom: 8px; font-weight: 600;">Exam Name:</label>
            <input type="text" id="exam-name" class="form-control" placeholder="e.g., Biology Midterm Practice" style="width: 100%;">
          </div>

          <div class="form-group" style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 8px; font-weight: 600;">Select Decks:</label>
            <div id="exam-deck-selection" style="max-height: 200px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: 10px; padding: 15px;">
              <!-- Deck checkboxes will be populated here -->
            </div>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
            <div class="form-group">
              <label for="exam-question-count" style="display: block; margin-bottom: 8px; font-weight: 600;">Number of Questions:</label>
              <select id="exam-question-count" class="form-control">
                <option value="10">10 Questions</option>
                <option value="20" selected>20 Questions</option>
                <option value="30">30 Questions</option>
                <option value="50">50 Questions</option>
                <option value="all">All Available Cards</option>
              </select>
            </div>

            <div class="form-group">
              <label for="exam-time-limit" style="display: block; margin-bottom: 8px; font-weight: 600;">Time Limit:</label>
              <select id="exam-time-limit" class="form-control">
                <option value="0">No Time Limit</option>
                <option value="15">15 Minutes</option>
                <option value="30" selected>30 Minutes</option>
                <option value="45">45 Minutes</option>
                <option value="60">60 Minutes</option>
                <option value="90">90 Minutes</option>
              </select>
            </div>
          </div>

          <div class="form-group" style="margin-bottom: 25px;">
            <label style="display: block; margin-bottom: 8px; font-weight: 600;">Question Types:</label>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
              <label style="display: flex; align-items: center; gap: 8px;">
                <input type="checkbox" id="exam-type-flashcard" checked> Flashcard Style (Front → Back)
              </label>
              <label style="display: flex; align-items: center; gap: 8px;">
                <input type="checkbox" id="exam-type-reverse"> Reverse (Back → Front)
              </label>
            </div>
          </div>

          <button id="start-exam-btn" class="btn btn-primary" style="width: 100%; padding: 15px; font-size: 1.1em;">🚀 Start Mock Exam</button>
        </div>

        <!-- Previous Exam Results -->
        <div class="exam-history-card" style="background: var(--card-bg); border-radius: 20px; padding: 30px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
          <h3 style="color: var(--primary-color); margin-bottom: 20px;">📊 Recent Exam Results</h3>
          <div id="exam-history-list">
            <p style="text-align: center; color: var(--text-color); opacity: 0.7;">No exams taken yet. Create your first mock exam above!</p>
          </div>
        </div>
      </div>

      <!-- Exam Taking View -->
      <div id="exam-taking-view" style="display: none;">
        <div class="exam-header" style="background: var(--card-bg); border-radius: 15px; padding: 20px; margin-bottom: 25px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
          <div>
            <h3 id="current-exam-name" style="color: var(--primary-color); margin: 0;">Mock Exam</h3>
            <p id="exam-progress" style="margin: 5px 0 0 0; color: var(--text-color); opacity: 0.8;">Question 1 of 20</p>
          </div>
          <div style="text-align: right;">
            <div id="exam-timer" style="font-size: 1.5em; font-weight: 600; color: var(--primary-color);">30:00</div>
            <button id="end-exam-btn" class="btn btn-secondary" style="margin-top: 10px;">End Exam</button>
          </div>
        </div>

        <div class="exam-question-card" style="background: var(--card-bg); border-radius: 20px; padding: 40px; text-align: center; box-shadow: 0 8px 32px rgba(0,0,0,0.1); min-height: 300px; display: flex; flex-direction: column; justify-content: center;">
          <div id="exam-question-content">
            <h4 style="color: var(--text-color); margin-bottom: 30px;">Loading question...</h4>
          </div>

          <div id="exam-answer-section" style="margin-top: 30px;">
            <textarea id="exam-answer-input" class="form-control" rows="4" placeholder="Type your answer here..." style="margin-bottom: 20px; font-size: 1.1em;"></textarea>
            <div style="display: flex; gap: 15px; justify-content: center;">
              <button id="exam-show-answer-btn" class="btn btn-secondary">Show Answer</button>
              <button id="exam-next-question-btn" class="btn btn-primary" style="display: none;">Next Question</button>
            </div>
          </div>
        </div>

        <div id="exam-answer-reveal" style="display: none; background: var(--card-bg); border-radius: 15px; padding: 25px; margin-top: 20px; border-left: 4px solid var(--primary-color);">
          <h5 style="color: var(--primary-color); margin-bottom: 15px;">Correct Answer:</h5>
          <div id="exam-correct-answer" style="margin-bottom: 20px; font-size: 1.1em;"></div>
          <div style="display: flex; gap: 15px; justify-content: center;">
            <button id="exam-mark-correct-btn" class="btn btn-primary" style="background: #10b981;">✅ I Got It Right</button>
            <button id="exam-mark-incorrect-btn" class="btn btn-danger">❌ I Got It Wrong</button>
          </div>
        </div>
      </div>

      <!-- Exam Results View -->
      <div id="exam-results-view" style="display: none;">
        <div class="exam-results-header" style="background: var(--card-bg); border-radius: 20px; padding: 30px; margin-bottom: 25px; text-align: center; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
          <h2 id="exam-final-score" style="color: var(--primary-color); margin-bottom: 15px;">85%</h2>
          <p id="exam-grade-text" style="font-size: 1.2em; margin-bottom: 10px;">Great Job!</p>
          <p id="exam-completion-time" style="color: var(--text-color); opacity: 0.8;">Completed in 25 minutes</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px; margin-bottom: 25px;">
          <div class="results-card" style="background: var(--card-bg); border-radius: 15px; padding: 25px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
            <h4 style="color: var(--primary-color); margin-bottom: 15px;">📊 Performance Breakdown</h4>
            <div id="exam-performance-stats"></div>
          </div>

          <div class="results-card" style="background: var(--card-bg); border-radius: 15px; padding: 25px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
            <h4 style="color: var(--primary-color); margin-bottom: 15px;">🎯 Areas to Focus</h4>
            <div id="exam-focus-areas"></div>
          </div>
        </div>

        <div class="results-actions" style="background: var(--card-bg); border-radius: 15px; padding: 25px; text-align: center; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
          <h4 style="color: var(--primary-color); margin-bottom: 20px;">📚 Recommended Actions</h4>
          <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
            <button id="create-focus-deck-btn" class="btn btn-primary">📝 Create Focus Deck</button>
            <button id="review-mistakes-btn" class="btn btn-secondary">🔍 Review Mistakes</button>
            <button id="retake-exam-btn" class="btn" style="background: var(--accent-color); color: white;">🔄 Retake Exam</button>
            <button id="new-exam-btn" class="btn btn-secondary">➕ New Exam</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Analytics Tab -->
    <div id="analytics" class="tab-content">
      <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">Study Analytics</h2>

      <div class="stats-dashboard">
        <div class="stat-card">
          <div class="stat-value" id="total-study-time">0h</div>
          <div class="stat-label">Total Study Time</div>
        </div>
        <div class="stat-card">
          <div class="stat-value" id="avg-accuracy">0%</div>
          <div class="stat-label">Average Accuracy</div>
        </div>
        <div class="stat-card">
          <div class="stat-value" id="cards-reviewed">0</div>
          <div class="stat-label">Cards Reviewed</div>
        </div>
        <div class="stat-card">
          <div class="stat-value" id="difficult-cards">0</div>
          <div class="stat-label">Difficult Cards</div>
        </div>
      </div>

      <div class="chart-container">
        <h3 style="color: var(--primary-color); margin-bottom: 15px;">Accuracy Trend (Last 30 Days)</h3>
        <canvas id="accuracy-chart" width="400" height="200"></canvas>
      </div>

      <div class="chart-container">
        <h3 style="color: var(--primary-color); margin-bottom: 15px;">Study Time (Last 7 Days)</h3>
        <canvas id="time-chart" width="400" height="200"></canvas>
      </div>

      <div style="background: var(--card-bg); padding: 20px; border-radius: 15px; border: 1px solid var(--border-color); margin-top: 20px;">
        <h3 style="color: var(--primary-color); margin-bottom: 15px;">Performance Insights</h3>
        <div id="performance-insights">
          <p>📊 Complete more study sessions to see detailed analytics</p>
        </div>
      </div>
    </div>

    <!-- Import Tab -->
    <div id="import" class="tab-content">
      <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">Import Cards from CSV</h2>
      <p style="color: var(--text-color); opacity: 0.8; margin-bottom: 25px;">Upload or paste CSV data to quickly add multiple cards to a deck.</p>

      <div class="form-group">
        <label for="import-deck">Target Deck:</label>
        <select id="import-deck" class="form-control">
          <option value="">Select a deck...</option>
        </select>
      </div>
      <div class="form-group">
        <label for="csv-file">CSV File (Front, Back, Tags format):</label>
        <input type="file" id="csv-file" class="form-control" accept=".csv">
      </div>
      <div class="form-group">
        <label for="csv-text">Or paste CSV text:</label>
        <textarea id="csv-text" class="form-control" rows="10" placeholder="Front,Back,Tags&#10;Question 1,Answer 1,math;algebra&#10;Question 2,Answer 2,science;physics"></textarea>
      </div>
      <button id="import-cards-btn" class="btn btn-primary">📥 Import Cards</button>
    </div>

    <!-- Manage Tab -->
    <div id="manage" class="tab-content">
      <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">Manage Cards</h2>
      <div style="display: flex; gap: 15px; margin-bottom: 25px; flex-wrap: wrap; align-items: center;">
        <select id="manage-deck" class="form-control" style="max-width: 200px;">
          <option value="">All Decks</option>
        </select>
        <input type="text" id="search-cards" class="form-control" placeholder="Search cards..." style="max-width: 250px;">
        <select id="confidence-filter" class="form-control" style="max-width: 180px;">
          <option value="">All Confidence</option>
          <option value="low">Low (0-33%)</option>
          <option value="medium">Medium (34-66%)</option>
          <option value="high">High (67-100%)</option>
        </select>
        <button id="filter-cards-btn" class="btn btn-secondary">🔍 Filter</button>
      </div>
      <div id="card-list" style="max-height: 500px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: 15px; background: var(--bg-color);">
        <div class="loading">Loading cards...</div>
      </div>
    </div>

    <!-- Settings Tab -->
    <div id="settings" class="tab-content">
      <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">Settings</h2>
      <div class="settings-grid">
        <div class="settings-card">
          <h3>🎨 Theme & Colors</h3>
          <div class="form-group">
            <label for="theme-select">Color Theme:</label>
            <select id="theme-select" class="form-control">
              <option value="teal">Teal (Default)</option>
              <option value="purple">Purple Primary</option>
              <option value="pink">Pink Primary</option>
              <option value="dark">Dark Mode</option>
            </select>
          </div>
        </div>

        <div class="settings-card">
          <h3>📝 Text Settings</h3>
          <div class="form-group">
            <label for="font-size">App Font Size:</label>
            <select id="font-size" class="form-control">
              <option value="14px">Small</option>
              <option value="16px" selected>Medium</option>
              <option value="18px">Large</option>
              <option value="20px">Extra Large</option>
            </select>
          </div>
        </div>

        <div class="settings-card">
          <h3>🎴 Flashcard Customization</h3>
          <div class="form-group">
            <label for="card-font">Card Font:</label>
            <select id="card-font" class="form-control">
              <option value="inherit">Default</option>
              <option value="'Avenir', 'Helvetica', sans-serif">Avenir</option>
              <option value="'Lobster', cursive">Lobster</option>
              <option value="'Georgia', serif">Georgia (Serif)</option>
              <option value="'Times New Roman', serif">Times New Roman</option>
              <option value="'Arial', sans-serif">Arial</option>
              <option value="'Comic Sans MS', cursive">Comic Sans</option>
              <option value="'Courier New', monospace">Courier New</option>
            </select>
          </div>

          <div class="form-group">
            <label for="card-font-size">Card Font Size:</label>
            <select id="card-font-size" class="form-control">
              <option value="1em">Small</option>
              <option value="1.3em" selected>Medium</option>
              <option value="1.6em">Large</option>
              <option value="2em">Extra Large</option>
              <option value="2.5em">Huge</option>
            </select>
          </div>

          <div class="form-group">
            <label for="card-height">Card Height:</label>
            <select id="card-height" class="form-control">
              <option value="200px">Short</option>
              <option value="250px" selected>Medium</option>
              <option value="300px">Tall</option>
              <option value="400px">Extra Tall</option>
            </select>
          </div>

          <div class="form-group">
            <label>Card Colors:</label>
            <div class="color-picker-group">
              <div class="color-picker">
                <label for="card-bg-color">Background:</label>
                <input type="color" id="card-bg-color" value="#ffffff">
              </div>
              <div class="color-picker">
                <label for="card-text-color">Text:</label>
                <input type="color" id="card-text-color" value="#1e293b">
              </div>
            </div>
          </div>

          <div class="preview-card">
            Sample flashcard preview text
          </div>
        </div>

        <div class="settings-card">
          <h3>⚙️ Study Settings</h3>
          <div class="form-group">
            <label for="spaced-repetition">Enable Spaced Repetition:</label>
            <select id="spaced-repetition" class="form-control">
              <option value="true" selected>Enabled</option>
              <option value="false">Disabled</option>
            </select>
          </div>
          <div class="form-group">
            <label for="daily-goal">Daily Study Goal (cards):</label>
            <input type="number" id="daily-goal" class="form-control" value="20" min="1" max="200">
          </div>
        </div>

        <div class="settings-card">
          <h3>💾 Data Management</h3>
          <button id="export-data-btn" class="btn btn-secondary" style="width: 100%; margin-bottom: 15px;">📤 Export All Data</button>
          <button id="clear-data-btn" class="btn btn-danger" style="width: 100%;">🗑️ Clear All Data</button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create Deck Modal -->
<div id="create-deck-modal" class="modal">
  <div class="modal-content">
    <span class="close" id="close-modal">&times;</span>
    <h3 style="color: var(--primary-color); margin-bottom: 25px;">✨ Create New Deck</h3>
    <div class="form-group">
      <label for="deck-name">Deck Name:</label>
      <input type="text" id="deck-name" class="form-control" placeholder="e.g., Biology Chapter 5">
    </div>
    <div class="form-group">
      <label for="deck-description">Description (optional):</label>
      <textarea id="deck-description" class="form-control" rows="3" placeholder="Brief description of the deck content"></textarea>
    </div>
    <button id="create-deck-confirm-btn" class="btn btn-primary" style="width: 100%;">🚀 Create Deck</button>
  </div>
</div>

<!-- Add Card Modal -->
<div id="add-card-modal" class="modal">
  <div class="modal-content" style="max-width: 700px;">
    <span class="close" id="close-card-modal">&times;</span>
    <h3 style="color: var(--primary-color); margin-bottom: 25px;">✏️ Create New Flashcard</h3>

    <div style="text-align: center; margin-bottom: 20px;">
      <button id="edit-front-btn" class="btn btn-primary" style="margin-right: 10px;">Edit Front</button>
      <button id="edit-back-btn" class="btn btn-secondary">Edit Back</button>
    </div>

    <!-- Visual Card Editor -->
    <div id="card-editor" class="flashcard" style="min-height: 300px; cursor: text; position: relative;">
      <div class="confidence-indicator confidence-low"></div>
      <div id="card-edit-content" style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;">
        <div contenteditable="true" id="card-text-editor" style="width: 90%; min-height: 100px; outline: none; text-align: center; border: 2px dashed transparent; padding: 10px; border-radius: 8px;" placeholder="Click here to add text..."></div>
        <div id="card-media-container" style="margin-top: 15px;">
          <!-- Media will be added here -->
        </div>
        <div style="margin-top: 15px;">
          <input type="file" id="card-media-input" accept="image/*,video/*,audio/*" style="display: none;">
          <button type="button" id="add-media-btn" class="btn btn-secondary" style="font-size: 0.9em; padding: 8px 16px;">📎 Add Media</button>
        </div>
      </div>
    </div>

    <div class="form-group" style="margin-top: 20px;">
      <label for="card-tags">Tags (optional):</label>
      <input type="text" id="card-tags" class="form-control" placeholder="e.g., math, algebra, equations (comma-separated)">
      <small style="color: var(--text-color); opacity: 0.7;">Use tags to organize and filter your cards</small>
    </div>

    <div style="text-align: center; margin-top: 25px;">
      <button id="save-card-btn" class="btn btn-primary" style="margin-right: 10px;">💾 Save Card</button>
      <button id="cancel-card-btn" class="btn btn-secondary">Cancel</button>
    </div>
  </div>
</div>

<!-- Firebase SDK -->
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>

<script type="module">
  // Enhanced Chunking Flashcard System with Firebase Integration and Fixed Functionality

  // Firebase Configuration
  const firebaseConfig = {
    apiKey: "AIzaSyByEky8SN50EA5XozKUURxpMAssJVF8dNU",
    authDomain: "football-fa84e.firebaseapp.com",
    projectId: "football-fa84e",
    storageBucket: "football-fa84e.appspot.com",
    messagingSenderId: "585689762025",
    appId: "1:585689762025:web:a4ce206e6b4541d499c878",
    measurementId: "G-JJYT9NZ6R0"
  };

  // Initialize Firebase with error handling for iframe environments
  let auth, db;
  let firebaseAvailable = false;

  try {
    firebase.initializeApp(firebaseConfig);
    auth = firebase.auth();
    db = firebase.firestore();
    firebaseAvailable = true;
    console.log('Firebase initialized successfully');
  } catch (error) {
    console.warn('Firebase initialization failed (likely due to iframe environment):', error);
    console.log('Falling back to localStorage-only mode');
    firebaseAvailable = false;
  }

  // Global App Object
  const EnhancedChunkingApp = {
    currentUser: null,
    allDecks: [],
    allCards: [],
    currentDeck: null,
    currentStudySession: null,
    studyStatistics: {
      totalStudyTime: 0,
      sessionsCompleted: 0,
      accuracyHistory: [],
      dailyStudyTime: {}
    },
    cardEditor: {
      currentSide: 'front',
      frontText: '',
      backText: '',
      frontMedia: null,
      backMedia: null,
      currentCard: null
    },

    init: function() {
      console.log('🧠 Initializing Chunking Flashcards...');
      this.debugLocalStorage();
      this.setupEventListeners();
      this.loadSettings();
      this.setupKeyboardShortcuts();
      this.setupFirebaseAuth();
    },

    debugLocalStorage: function() {
      console.log('🔍 Debugging localStorage...');
      console.log('📦 chunking_users:', localStorage.getItem('chunking_users'));
      console.log('👤 chunking_user:', localStorage.getItem('chunking_user'));
      console.log('🗂️ chunking_decks:', localStorage.getItem('chunking_decks'));
      console.log('📚 chunking_cards:', localStorage.getItem('chunking_cards'));

      // Check all localStorage keys
      const allKeys = [];
      for (let i = 0; i < localStorage.length; i++) {
        allKeys.push(localStorage.key(i));
      }
      console.log('🔑 All localStorage keys:', allKeys);
    },

    // Firebase Authentication Setup
    setupFirebaseAuth: function() {
      console.log('🔐 Setting up authentication...');

      // Always check localStorage first for existing session
      const savedUser = localStorage.getItem('chunking_user');
      if (savedUser) {
        try {
          this.currentUser = JSON.parse(savedUser);
          console.log('✅ Restored user session:', this.currentUser.email);
          this.showMainApp();
          return;
        } catch (e) {
          console.warn('❌ Failed to restore user session, clearing invalid data');
          localStorage.removeItem('chunking_user');
        }
      }

      // If no saved session, check Firebase (if available)
      if (firebaseAvailable) {
        auth.onAuthStateChanged((user) => {
          if (user) {
            console.log('User signed in via Firebase:', user.email);
            this.currentUser = user;
            // Save user info to localStorage as backup
            localStorage.setItem('chunking_user', JSON.stringify({
              uid: user.uid,
              email: user.email,
              displayName: user.displayName
            }));
            this.showMainApp();
          } else {
            console.log('No Firebase user signed in');
            // Don't remove localStorage user here - they might be using localStorage auth
            if (!this.currentUser) {
              this.showAuthScreen();
            }
          }
        });
      } else {
        console.log('Firebase not available, showing auth screen');
        this.showAuthScreen();
      }
    },

    // Authentication Methods
    async signInWithEmail(email, password) {
      console.log('🔐 Attempting sign in for:', email);

      // Always try localStorage first in iframe environments
      const storedUsers = JSON.parse(localStorage.getItem('chunking_users') || '{}');
      const userKey = email.toLowerCase();

      console.log('📦 All stored users:', Object.keys(storedUsers));
      console.log('🔍 Looking for user key:', userKey);
      console.log('🔍 User exists:', !!storedUsers[userKey]);

      if (storedUsers[userKey]) {
        console.log('👤 Found user data:', {
          uid: storedUsers[userKey].uid,
          name: storedUsers[userKey].name,
          email: storedUsers[userKey].email,
          passwordMatch: storedUsers[userKey].password === password
        });
      }

      // Check localStorage authentication first
      if (storedUsers[userKey] && storedUsers[userKey].password === password) {
        console.log('✅ LocalStorage authentication successful');
        this.currentUser = {
          uid: storedUsers[userKey].uid,
          email: email,
          displayName: storedUsers[userKey].name
        };

        // Save current session
        localStorage.setItem('chunking_user', JSON.stringify(this.currentUser));
        console.log('💾 Saved user session to localStorage');

        this.showMessage('Successfully signed in! 🎉', 'success');
        this.showMainApp();
        return;
      }

      console.log('❌ LocalStorage authentication failed');

      // Provide helpful error message
      if (storedUsers[userKey]) {
        this.showAuthError('Incorrect password. Please check your password and try again.');
      } else {
        this.showAuthError('Account not found. Please check your email or create a new account.');
      }
    },

    async signUpWithEmail(name, email, password, confirmPassword) {
      console.log('📝 Attempting signup for:', email);

      if (password !== confirmPassword) {
        this.showAuthError('Passwords do not match');
        return;
      }
      if (password.length < 6) {
        this.showAuthError('Password must be at least 6 characters');
        return;
      }

      // Always create localStorage account first in iframe environments
      const storedUsers = JSON.parse(localStorage.getItem('chunking_users') || '{}');
      const userKey = email.toLowerCase();

      console.log('📦 Existing users before signup:', Object.keys(storedUsers));

      if (storedUsers[userKey]) {
        this.showAuthError('An account with this email already exists');
        return;
      }

      const newUser = {
        uid: 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        name: name,
        email: email,
        password: password, // In a real app, this should be hashed
        createdAt: new Date().toISOString()
      };

      storedUsers[userKey] = newUser;
      localStorage.setItem('chunking_users', JSON.stringify(storedUsers));

      console.log('💾 User saved to localStorage:', userKey);
      console.log('👤 New user data:', newUser);
      console.log('📦 All users after signup:', Object.keys(storedUsers));

      this.currentUser = {
        uid: newUser.uid,
        email: email,
        displayName: name
      };

      // Save current session
      localStorage.setItem('chunking_user', JSON.stringify(this.currentUser));
      console.log('💾 Saved user session to localStorage');

      console.log('✅ Account created successfully, showing main app');
      this.showMessage('Account created successfully! 🎉', 'success');
      this.showMainApp();
    },

    showAuthError: function(message) {
      const errorDiv = document.getElementById('auth-error');
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';
      setTimeout(() => {
        errorDiv.style.display = 'none';
      }, 5000);
    },

    // Authentication
    showAuthScreen: function() {
      document.getElementById('auth-screen').style.display = 'block';
      document.getElementById('main-app').style.display = 'none';

      // Remove any existing auth info banners
      const existingAuthInfo = document.querySelector('.auth-info');
      if (existingAuthInfo) {
        existingAuthInfo.remove();
      }

      // Add debug info for troubleshooting
      this.addAuthDebugInfo();
    },

    addAuthDebugInfo: function() {
      const storedUsers = JSON.parse(localStorage.getItem('chunking_users') || '{}');
      const userCount = Object.keys(storedUsers).length;

      if (userCount > 0) {
        const debugInfo = document.createElement('div');
        debugInfo.style.cssText = `
          background: #e0f2fe;
          border: 1px solid #0284c7;
          color: #0c4a6e;
          padding: 10px;
          border-radius: 5px;
          margin-bottom: 15px;
          font-size: 0.9em;
          text-align: center;
        `;
        debugInfo.innerHTML = `
          <strong>📊 Debug Info</strong><br>
          ${userCount} account(s) stored locally<br>
          <button onclick="EnhancedChunkingApp.showStoredUsers()" style="margin-top: 5px; padding: 5px 10px; border: none; background: #0284c7; color: white; border-radius: 3px; cursor: pointer;">
            Show Stored Accounts
          </button>
        `;
      } else {
        // If no users stored, show a fix button
        const debugInfo = document.createElement('div');
        debugInfo.style.cssText = `
          background: #fef3c7;
          border: 1px solid #f59e0b;
          color: #92400e;
          padding: 10px;
          border-radius: 5px;
          margin-bottom: 15px;
          font-size: 0.9em;
          text-align: center;
        `;
        debugInfo.innerHTML = `
          <strong>⚠️ No accounts found in localStorage</strong><br>
          If you just created an account, click below to fix:<br>
          <button onclick="EnhancedChunkingApp.addTestAccount()" style="margin-top: 5px; padding: 5px 10px; border: none; background: #f59e0b; color: white; border-radius: 3px; cursor: pointer;">
            Add Your Account Manually
          </button>
          <button onclick="EnhancedChunkingApp.debugLocalStorage()" style="margin-top: 5px; margin-left: 5px; padding: 5px 10px; border: none; background: #6b7280; color: white; border-radius: 3px; cursor: pointer;">
            Debug Storage
          </button>
        `;

        const authContainer = document.querySelector('.auth-container');
        if (authContainer && !authContainer.querySelector('.debug-info')) {
          debugInfo.className = 'debug-info';
          authContainer.insertBefore(debugInfo, authContainer.firstChild);
        }

        const authContainer = document.querySelector('.auth-container');
        if (authContainer && !authContainer.querySelector('.debug-info')) {
          debugInfo.className = 'debug-info';
          authContainer.insertBefore(debugInfo, authContainer.firstChild);
        }
      }
    },

    showStoredUsers: function() {
      const storedUsers = JSON.parse(localStorage.getItem('chunking_users') || '{}');
      const userList = Object.keys(storedUsers).map(email => {
        const user = storedUsers[email];
        return `Email: ${user.email}\nName: ${user.name}\nUID: ${user.uid}\nCreated: ${user.createdAt}`;
      }).join('\n\n');

      if (userList) {
        alert(`Stored Accounts:\n\n${userList}`);
      } else {
        alert('No accounts stored locally.');
      }
    },

    addTestAccount: function() {
      const email = prompt('Enter your email address:');
      if (!email) return;

      const name = prompt('Enter your name:');
      if (!name) return;

      const password = prompt('Enter your password:');
      if (!password) return;

      console.log('🔧 Manually adding account:', email);

      const storedUsers = JSON.parse(localStorage.getItem('chunking_users') || '{}');
      const userKey = email.toLowerCase();

      const newUser = {
        uid: 'local_manual_' + Date.now(),
        name: name,
        email: email,
        password: password,
        createdAt: new Date().toISOString()
      };

      storedUsers[userKey] = newUser;
      localStorage.setItem('chunking_users', JSON.stringify(storedUsers));

      console.log('✅ Account added manually');
      console.log('📦 Updated stored users:', Object.keys(storedUsers));

      alert(`Account added successfully!\nEmail: ${email}\nYou can now try logging in.`);

      // Refresh the debug info
      this.addAuthDebugInfo();
    },

    continueAsGuest: function() {
      console.log('👤 Continuing as guest user');
      this.currentUser = { uid: 'guest', displayName: 'Guest User' };
      this.showMainApp();
    },

    async showMainApp() {
      document.getElementById('auth-screen').style.display = 'none';
      document.getElementById('main-app').style.display = 'block';
      document.getElementById('user-name').textContent =
              this.currentUser.displayName || this.currentUser.email || 'Guest User';

      await this.loadCards();
      await this.loadDecks();
      this.loadStudyStatistics();
      this.updateDashboard();
    },

    setupEventListeners: function() {
      // Auth tabs
      document.getElementById('login-tab').addEventListener('click', () => this.showLoginForm());
      document.getElementById('signup-tab').addEventListener('click', () => this.showSignupForm());

      // Auth forms
      document.getElementById('login-form').addEventListener('submit', (e) => this.handleLogin(e));
      document.getElementById('signup-form').addEventListener('submit', (e) => this.handleSignup(e));
      document.getElementById('guest-btn').addEventListener('click', () => this.continueAsGuest());
      document.getElementById('sign-out-btn').addEventListener('click', () => this.signOut());

      // Navigation
      document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.addEventListener('click', (e) => {
          const tabName = e.target.getAttribute('data-tab');
          this.showTab(tabName);
        });
      });

      // Deck management
      document.getElementById('create-deck-btn').addEventListener('click', () => this.showCreateDeckModal());
      document.getElementById('close-modal').addEventListener('click', () => this.closeModal());
      document.getElementById('create-deck-confirm-btn').addEventListener('click', (e) => {
        console.log('🔘 Create deck button clicked');
        e.preventDefault();
        this.createDeck();
      });

      // Add Enter key support for deck creation
      document.getElementById('deck-name').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          console.log('⌨️ Enter pressed in deck name field');
          this.createDeck();
        }
      });

      document.getElementById('deck-description').addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          console.log('⌨️ Enter pressed in deck description field');
          e.preventDefault();
          this.createDeck();
        }
      });
      document.getElementById('back-to-decks-btn').addEventListener('click', () => this.showDeckList());
      document.getElementById('add-card-to-deck-btn').addEventListener('click', () => this.showCardEditor());

      // Card editor
      document.getElementById('close-card-modal').addEventListener('click', () => this.closeCardEditor());
      document.getElementById('edit-front-btn').addEventListener('click', () => this.switchToFront());
      document.getElementById('edit-back-btn').addEventListener('click', () => this.switchToBack());
      document.getElementById('add-media-btn').addEventListener('click', () => this.triggerMediaUpload());
      document.getElementById('card-media-input').addEventListener('change', (e) => this.handleMediaUpload(e));
      document.getElementById('save-card-btn').addEventListener('click', () => this.saveCard());
      document.getElementById('cancel-card-btn').addEventListener('click', () => this.closeCardEditor());

      // Study modes
      document.getElementById('start-spaced-btn').addEventListener('click', () => this.startSpacedRepetition());
      document.getElementById('start-chunking-btn').addEventListener('click', () => this.startChunkingStudy());
      document.getElementById('start-review-btn').addEventListener('click', () => this.startFullReview());

      // Study controls
      document.getElementById('flip-btn').addEventListener('click', () => this.flipCard());
      document.getElementById('again-btn').addEventListener('click', () => this.markDifficulty('again'));
      document.getElementById('hard-btn').addEventListener('click', () => this.markDifficulty('hard'));
      document.getElementById('good-btn').addEventListener('click', () => this.markDifficulty('good'));
      document.getElementById('easy-btn').addEventListener('click', () => this.markDifficulty('easy'));
      document.getElementById('correct-btn').addEventListener('click', () => this.markCorrect());
      document.getElementById('incorrect-btn').addEventListener('click', () => this.markIncorrect());
      document.getElementById('end-study-btn').addEventListener('click', () => this.endStudySession());
      document.getElementById('flashcard').addEventListener('click', () => this.flipCard());

      // Settings
      document.getElementById('theme-select').addEventListener('change', () => this.changeTheme());
      document.getElementById('font-size').addEventListener('change', () => this.changeFontSize());
      document.getElementById('card-font').addEventListener('change', () => this.updateCardStyle());
      document.getElementById('card-font-size').addEventListener('change', () => this.updateCardStyle());
      document.getElementById('card-height').addEventListener('change', () => this.updateCardStyle());
      document.getElementById('card-bg-color').addEventListener('change', () => this.updateCardStyle());
      document.getElementById('card-text-color').addEventListener('change', () => this.updateCardStyle());

      // Import/Export
      document.getElementById('import-cards-btn').addEventListener('click', () => this.importCards());
      document.getElementById('filter-cards-btn').addEventListener('click', () => this.filterCards());
      document.getElementById('search-cards').addEventListener('input', () => this.filterCards());
      document.getElementById('confidence-filter').addEventListener('change', () => this.filterCards());
      document.getElementById('manage-deck').addEventListener('change', () => this.filterCards());
      document.getElementById('export-data-btn').addEventListener('click', () => this.exportData());
      document.getElementById('clear-data-btn').addEventListener('click', () => this.clearAllData());

      // Mock Exam event listeners
      document.getElementById('start-exam-btn').addEventListener('click', () => this.startMockExam());
      document.getElementById('end-exam-btn').addEventListener('click', () => this.endMockExam());
      document.getElementById('exam-show-answer-btn').addEventListener('click', () => this.showExamAnswer());
      document.getElementById('exam-next-question-btn').addEventListener('click', () => this.nextExamQuestion());
      document.getElementById('exam-mark-correct-btn').addEventListener('click', () => this.markExamAnswer(true));
      document.getElementById('exam-mark-incorrect-btn').addEventListener('click', () => this.markExamAnswer(false));
      document.getElementById('create-focus-deck-btn').addEventListener('click', () => this.createFocusDeck());
      document.getElementById('review-mistakes-btn').addEventListener('click', () => this.reviewMistakes());
      document.getElementById('retake-exam-btn').addEventListener('click', () => this.retakeExam());
      document.getElementById('new-exam-btn').addEventListener('click', () => this.showExamSetup());
    },

    // Authentication Form Handlers
    showLoginForm: function() {
      document.getElementById('login-tab').classList.add('active');
      document.getElementById('signup-tab').classList.remove('active');
      document.getElementById('login-form').style.display = 'block';
      document.getElementById('signup-form').style.display = 'none';
    },

    showSignupForm: function() {
      document.getElementById('signup-tab').classList.add('active');
      document.getElementById('login-tab').classList.remove('active');
      document.getElementById('signup-form').style.display = 'block';
      document.getElementById('login-form').style.display = 'none';
    },

    async handleLogin(e) {
      e.preventDefault();
      const email = document.getElementById('login-email').value.trim();
      const password = document.getElementById('login-password').value;

      console.log('Login attempt:', email);

      if (!email || !password) {
        this.showAuthError('Please enter both email and password');
        return;
      }

      await this.signInWithEmail(email, password);
    },

    async handleSignup(e) {
      e.preventDefault();
      const name = document.getElementById('signup-name').value.trim();
      const email = document.getElementById('signup-email').value.trim();
      const password = document.getElementById('signup-password').value;
      const confirmPassword = document.getElementById('signup-confirm').value;

      console.log('Signup attempt:', email);

      if (!name || !email || !password || !confirmPassword) {
        this.showAuthError('Please fill in all fields');
        return;
      }

      await this.signUpWithEmail(name, email, password, confirmPassword);
    },

    async signOut() {
      try {
        if (firebaseAvailable && auth.currentUser) {
          await auth.signOut();
        }

        // Clear localStorage data
        localStorage.removeItem('chunking_user');

        this.currentUser = null;
        this.currentDeck = null;
        this.allDecks = [];
        this.allCards = [];
        this.showAuthScreen();
        this.showMessage('Signed out successfully', 'success');
      } catch (error) {
        console.error('Sign out error:', error);
        // Even if Firebase sign out fails, clear local data
        localStorage.removeItem('chunking_user');
        this.currentUser = null;
        this.currentDeck = null;
        this.allDecks = [];
        this.allCards = [];
        this.showAuthScreen();
        this.showMessage('Signed out successfully', 'success');
      }
    },

    showTab: function(tabName) {
      document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
      document.querySelectorAll('.nav-tab').forEach(tab => tab.classList.remove('active'));

      document.getElementById(tabName).classList.add('active');
      document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

      if (tabName === 'decks') {
        this.showDeckList();
      } else if (tabName === 'exams') {
        this.showExamSetup();
      } else if (tabName === 'analytics') {
        this.updateAnalytics();
      } else if (tabName === 'manage') {
        this.filterCards();
      }
    },

    // Deck Management with Fixed Card Counting
    showCreateDeckModal: function() {
      document.getElementById('create-deck-modal').style.display = 'block';
    },

    closeModal: function() {
      document.getElementById('create-deck-modal').style.display = 'none';
    },

    async createDeck() {
      console.log('🏗️ Creating deck...');

      const name = document.getElementById('deck-name').value.trim();
      const description = document.getElementById('deck-description').value.trim();

      console.log('📝 Deck name:', name);
      console.log('📝 Deck description:', description);

      if (!name) {
        this.showMessage('Please enter a deck name', 'error');
        return;
      }

      // Check if we should use Firebase or localStorage
      const useFirebase = firebaseAvailable && this.currentUser.uid !== 'guest' && !this.currentUser.uid.startsWith('local_');

      console.log('🔥 Use Firebase:', useFirebase);
      console.log('👤 Current user UID:', this.currentUser.uid);

      const deckData = {
        name: name,
        description: description,
        created: useFirebase ? firebase.firestore.FieldValue.serverTimestamp() : new Date().toISOString(),
        cardCount: 0,
        userId: this.currentUser.uid
      };

      try {
        if (useFirebase) {
          console.log('💾 Saving to Firebase...');
          const docRef = await db.collection('decks').add(deckData);
          deckData.id = docRef.id;
        } else {
          console.log('💾 Saving to localStorage...');
          // Save to localStorage for guest users or local accounts
          deckData.id = 'local_' + Date.now();
          deckData.created = new Date().toISOString(); // Ensure it's a string for localStorage
          const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
          localDecks.push(deckData);
          localStorage.setItem('chunking_decks', JSON.stringify(localDecks));
        }

        console.log('✅ Deck created successfully:', deckData.id);

        this.closeModal();
        document.getElementById('deck-name').value = '';
        document.getElementById('deck-description').value = '';
        await this.loadDecks();
        this.updateDeckSelects();
        this.showMessage('Deck created successfully! 🎉', 'success');
      } catch (error) {
        console.error('❌ Error creating deck:', error);
        this.showMessage('Error creating deck: ' + error.message, 'error');
      }
    },

    async loadDecks() {
      try {
        if (firebaseAvailable && this.currentUser.uid !== 'guest' && !this.currentUser.uid.startsWith('local_')) {
          // Load from Firebase
          const snapshot = await db.collection('decks')
            .where('userId', '==', this.currentUser.uid)
            .get();

          this.allDecks = [];
          snapshot.forEach(doc => {
            const deckData = doc.data();
            deckData.id = doc.id;
            this.allDecks.push(deckData);
          });
        } else {
          // Load from localStorage for guest users or when Firebase is unavailable
          const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
          this.allDecks = localDecks.filter(deck => deck.userId === this.currentUser.uid);
        }

        // Update card counts for all decks
        for (const deck of this.allDecks) {
          const deckCards = this.allCards.filter(card => card.deckId === deck.id);
          const newCardCount = deckCards.length;

          // Update card count if it has changed
          if (deck.cardCount !== newCardCount) {
            deck.cardCount = newCardCount;

            // Save updated count to Firebase or localStorage
            if (this.currentUser.uid !== 'guest') {
              await db.collection('decks').doc(deck.id).update({ cardCount: newCardCount });
            } else {
              const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
              const deckIndex = localDecks.findIndex(d => d.id === deck.id);
              if (deckIndex !== -1) {
                localDecks[deckIndex].cardCount = newCardCount;
                localStorage.setItem('chunking_decks', JSON.stringify(localDecks));
              }
            }
          }
        }

        this.renderDecks();
      } catch (error) {
        console.error('Error loading decks:', error);
        this.showMessage('Error loading decks', 'error');
      }
    },

    renderDecks: function() {
      const grid = document.getElementById('deck-grid');
      if (this.allDecks.length === 0) {
        grid.innerHTML = `
                        <div style="grid-column: 1 / -1; text-align: center; padding: 60px 20px;">
                            <div style="font-size: 3em; margin-bottom: 20px;">📚</div>
                            <h3 style="color: var(--text-color); margin-bottom: 10px;">No decks yet!</h3>
                            <p style="color: var(--text-color); opacity: 0.7;">Create your first deck to get started.</p>
                        </div>
                    `;
        return;
      }

      grid.innerHTML = this.allDecks.map(deck => {
        const deckCards = this.allCards.filter(card => card.deckId === deck.id);
        const cardCount = deckCards.length;
        const avgConfidence = cardCount > 0 ?
                Math.round(deckCards.reduce((sum, card) => sum + (card.confidence || 0), 0) / cardCount) : 0;

        const dueCards = this.getDueCards(deck.id).length;

        return `
                        <div class="deck-card" data-deck-id="${deck.id}">
                            <button class="delete-btn deck-delete-btn" data-deck-id="${deck.id}" title="Delete deck">✕</button>
                            <h3 style="color: var(--primary-color); margin-bottom: 10px; padding-right: 40px;">${deck.name}</h3>
                            <p style="color: var(--text-color); margin-bottom: 20px; opacity: 0.8; min-height: 40px;">${deck.description || 'No description'}</p>
                            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.9em; color: var(--text-color); opacity: 0.7;">
                                <span>📚 ${cardCount} cards</span>
                                <span>🎯 ${avgConfidence}% avg</span>
                            </div>
                            ${dueCards > 0 ? `<div style="margin-top: 10px; color: var(--danger-color); font-size: 0.9em; font-weight: 600;">📅 ${dueCards} due today</div>` : ''}
                        </div>
                    `;
      }).join('');

      // Add event listeners for deck cards and delete buttons
      this.setupDeckEventListeners();
    },

    setupDeckEventListeners: function() {
      // Deck card click handlers (for opening deck)
      document.querySelectorAll('.deck-card').forEach(card => {
        card.addEventListener('click', (e) => {
          // Don't trigger if clicking delete button
          if (e.target.classList.contains('delete-btn')) return;

          const deckId = card.getAttribute('data-deck-id');
          this.showDeckDetail(deckId);
        });
      });

      // Delete button handlers with confirmation
      document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.stopPropagation(); // Prevent deck opening
          const deckId = btn.getAttribute('data-deck-id');
          this.confirmDeleteDeck(deckId);
        });
      });
    },

    confirmDeleteDeck: function(deckId) {
      const deck = this.allDecks.find(d => d.id === deckId);
      if (!deck) return;

      const cardCount = this.allCards.filter(card => card.deckId === deckId).length;
      const message = cardCount > 0
        ? `Are you sure you want to delete "${deck.name}"?\n\nThis will permanently delete the deck and all ${cardCount} cards in it.\n\nThis action cannot be undone.`
        : `Are you sure you want to delete "${deck.name}"?\n\nThis action cannot be undone.`;

      if (confirm(message)) {
        this.deleteDeck(deckId);
      }
    },

    async deleteDeck(deckId) {
      try {
        const deck = this.allDecks.find(d => d.id === deckId);
        if (!deck) return;

        // Delete all cards in the deck first
        const cardsToDelete = this.allCards.filter(card => card.deckId === deckId);
        for (const card of cardsToDelete) {
          await this.deleteCardById(card.id, false); // false = don't show individual confirmations
        }

        // Delete the deck
        if (this.currentUser.uid !== 'guest') {
          await db.collection('decks').doc(deckId).delete();
        } else {
          const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
          const filteredDecks = localDecks.filter(deck => deck.id !== deckId);
          localStorage.setItem('chunking_decks', JSON.stringify(filteredDecks));
        }

        // Update local arrays
        this.allDecks = this.allDecks.filter(deck => deck.id !== deckId);

        // If we're currently viewing this deck, go back to deck list
        if (this.currentDeck && this.currentDeck.id === deckId) {
          this.showDeckList();
        }

        // Refresh UI
        await this.loadDecks();
        await this.loadCards();
        this.updateDeckSelects();
        this.showMessage(`✅ Deck "${deck.name}" deleted successfully!`, 'success');
      } catch (error) {
        console.error('Error deleting deck:', error);
        this.showMessage('Error deleting deck', 'error');
      }
    },

    showDeckDetail: function(deckId) {
      this.currentDeck = this.allDecks.find(d => d.id === deckId);
      if (!this.currentDeck) return;

      document.getElementById('deck-list-view').style.display = 'none';
      document.getElementById('deck-detail-view').style.display = 'block';
      document.getElementById('deck-title').textContent = this.currentDeck.name;

      this.renderDeckCards();
    },

    showDeckList: function() {
      document.getElementById('deck-detail-view').style.display = 'none';
      document.getElementById('deck-list-view').style.display = 'block';
      this.currentDeck = null;
    },

    renderDeckCards: function() {
      const container = document.getElementById('deck-cards-grid');
      const deckCards = this.allCards.filter(card => card.deckId === this.currentDeck.id);

      if (deckCards.length === 0) {
        container.innerHTML = `
                        <div style="grid-column: 1 / -1; text-align: center; padding: 60px 20px;">
                            <div style="font-size: 3em; margin-bottom: 20px;">📝</div>
                            <h3 style="color: var(--text-color); margin-bottom: 10px;">No cards in this deck yet!</h3>
                            <p style="color: var(--text-color); opacity: 0.7;">Click "Add New Card" to create your first flashcard.</p>
                        </div>
                    `;
        return;
      }

      container.innerHTML = deckCards.map(card => {
        const confidence = card.confidence || 0;
        const confidenceClass = confidence >= 67 ? 'confidence-high' :
                confidence >= 34 ? 'confidence-medium' : 'confidence-low';

        const hasMedia = card.frontMedia || card.backMedia;
        const mediaIndicator = hasMedia ? ' 📎' : '';

        const tags = card.tags && card.tags.length > 0 ?
                `<div class="tags-container">${card.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}</div>` : '';

        const spacedRepetition = this.calculateSpacedRepetition(card);
        const srClass = spacedRepetition.status === 'due' ? 'due' :
                spacedRepetition.status === 'mastered' ? 'mastered' : '';

        return `
                        <div class="deck-card-mini" data-card-id="${card.id}">
                            <div class="confidence-indicator ${confidenceClass}"></div>
                            <div class="spaced-repetition-indicator ${srClass}">${spacedRepetition.label}</div>
                            <div class="card-content">
                                <div style="font-weight: 600; margin-bottom: 10px; color: var(--text-color);">${card.front}${mediaIndicator}</div>
                                ${card.frontMedia ? this.renderMediaPreview(card.frontMedia) : ''}
                                ${tags}
                            </div>
                            <div class="card-actions" style="margin-top: 15px; display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-size: 0.9em; color: var(--text-color);">🎯 ${confidence}%</span>
                                <button class="btn btn-danger delete-card-btn" data-card-id="${card.id}" style="padding: 6px 12px; font-size: 0.8em;">🗑️</button>
                            </div>
                        </div>
                    `;
      }).join('');

      // Add event listeners
      container.querySelectorAll('.delete-card-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.stopPropagation();
          const cardId = btn.getAttribute('data-card-id');
          this.deleteCard(cardId);
        });
      });

      container.querySelectorAll('.deck-card-mini').forEach(card => {
        card.addEventListener('click', (e) => {
          if (!e.target.classList.contains('delete-card-btn')) {
            const cardId = card.getAttribute('data-card-id');
            this.editCard(cardId);
          }
        });
      });
    },

    // Enhanced Card Editor with Visual Feedback
    showCardEditor: function(cardId = null) {
      this.resetCardEditor();

      if (cardId) {
        const card = this.allCards.find(c => c.id === cardId);
        if (card) {
          this.cardEditor.currentCard = card;
          this.cardEditor.frontText = card.front;
          this.cardEditor.backText = card.back;
          this.cardEditor.frontMedia = card.frontMedia || null;
          this.cardEditor.backMedia = card.backMedia || null;
          document.getElementById('card-tags').value = card.tags ? card.tags.join(', ') : '';
        }
      } else {
        this.cardEditor.currentCard = null;
        document.getElementById('card-tags').value = '';
      }

      this.switchToFront();
      document.getElementById('add-card-modal').style.display = 'block';
    },

    editCard: function(cardId) {
      this.showCardEditor(cardId);
    },

    closeCardEditor: function() {
      document.getElementById('add-card-modal').style.display = 'none';
      this.resetCardEditor();
    },

    resetCardEditor: function() {
      this.cardEditor = {
        currentSide: 'front',
        frontText: '',
        backText: '',
        frontMedia: null,
        backMedia: null,
        currentCard: null
      };
      document.getElementById('card-text-editor').textContent = '';
      document.getElementById('card-media-container').innerHTML = '';
      document.getElementById('add-card-modal').className = 'modal';
    },

    switchToFront: function() {
      this.saveCurrentSideContent();
      this.cardEditor.currentSide = 'front';
      this.updateCardEditor();
      document.getElementById('add-card-modal').className = 'modal editing-front';
    },

    switchToBack: function() {
      this.saveCurrentSideContent();
      this.cardEditor.currentSide = 'back';
      this.updateCardEditor();
      document.getElementById('add-card-modal').className = 'modal editing-back';
    },

    saveCurrentSideContent: function() {
      const text = document.getElementById('card-text-editor').textContent.trim();
      if (this.cardEditor.currentSide === 'front') {
        this.cardEditor.frontText = text;
      } else {
        this.cardEditor.backText = text;
      }
    },

    updateCardEditor: function() {
      const textEditor = document.getElementById('card-text-editor');
      const mediaContainer = document.getElementById('card-media-container');

      if (this.cardEditor.currentSide === 'front') {
        textEditor.textContent = this.cardEditor.frontText;
        this.displayMedia(mediaContainer, this.cardEditor.frontMedia);
      } else {
        textEditor.textContent = this.cardEditor.backText;
        this.displayMedia(mediaContainer, this.cardEditor.backMedia);
      }

      textEditor.focus();
    },

    displayMedia: function(container, media) {
      container.innerHTML = '';
      if (media && media.data) {
        const mediaElement = this.createMediaElement(media);
        if (mediaElement) {
          const wrapper = document.createElement('div');
          wrapper.style.position = 'relative';
          wrapper.style.display = 'inline-block';

          const deleteBtn = document.createElement('button');
          deleteBtn.innerHTML = '❌';
          deleteBtn.style.position = 'absolute';
          deleteBtn.style.top = '5px';
          deleteBtn.style.right = '5px';
          deleteBtn.style.background = 'rgba(255,255,255,0.8)';
          deleteBtn.style.border = 'none';
          deleteBtn.style.borderRadius = '50%';
          deleteBtn.style.width = '25px';
          deleteBtn.style.height = '25px';
          deleteBtn.style.cursor = 'pointer';
          deleteBtn.style.fontSize = '12px';

          deleteBtn.addEventListener('click', () => this.removeMedia());

          wrapper.appendChild(mediaElement);
          wrapper.appendChild(deleteBtn);
          container.appendChild(wrapper);
        }
      }
    },

    triggerMediaUpload: function() {
      document.getElementById('card-media-input').click();
    },

    handleMediaUpload: function(event) {
      const file = event.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const media = {
            type: file.type,
            data: e.target.result,
            name: file.name
          };

          if (this.cardEditor.currentSide === 'front') {
            this.cardEditor.frontMedia = media;
          } else {
            this.cardEditor.backMedia = media;
          }

          this.updateCardEditor();
        };
        reader.readAsDataURL(file);
      }
      event.target.value = '';
    },

    removeMedia: function() {
      if (this.cardEditor.currentSide === 'front') {
        this.cardEditor.frontMedia = null;
      } else {
        this.cardEditor.backMedia = null;
      }
      this.updateCardEditor();
    },

    createMediaElement: function(media) {
      if (!media || !media.data) return null;

      const fileType = media.type.split('/')[0];
      let element;

      if (fileType === 'image') {
        element = document.createElement('img');
        element.src = media.data;
        element.alt = media.name || 'Card image';
        element.className = 'media-preview';
      } else if (fileType === 'video') {
        element = document.createElement('video');
        element.src = media.data;
        element.controls = true;
        element.className = 'media-preview';
      } else if (fileType === 'audio') {
        element = document.createElement('audio');
        element.src = media.data;
        element.controls = true;
        element.style.width = '200px';
      }

      return element;
    },

    renderMediaPreview: function(media) {
      if (!media || !media.data) return '';

      const fileType = media.type.split('/')[0];
      if (fileType === 'image') {
        return `<img src="${media.data}" alt="${media.name || 'Card image'}" style="max-width: 100%; max-height: 80px; margin: 5px 0; border-radius: 5px;">`;
      } else if (fileType === 'video') {
        return `<video src="${media.data}" style="max-width: 100%; max-height: 80px; margin: 5px 0; border-radius: 5px;"></video>`;
      } else if (fileType === 'audio') {
        return `<div style="margin: 5px 0; font-size: 0.8em; color: #666;">🔊 Audio</div>`;
      }
      return '';
    },

    async saveCard() {
      try {
        this.saveCurrentSideContent();

        if (!this.currentDeck) {
          this.showMessage('No deck selected', 'error');
          return;
        }

        if (!this.cardEditor.frontText || !this.cardEditor.backText) {
          this.showMessage('Please add text to both front and back of the card', 'error');
          return;
        }

        const tagsText = document.getElementById('card-tags').value.trim();
        const tags = tagsText ? tagsText.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

        const cardData = {
          deckId: this.currentDeck.id,
          front: this.cardEditor.frontText,
          back: this.cardEditor.backText,
          frontMedia: this.cardEditor.frontMedia,
          backMedia: this.cardEditor.backMedia,
          tags: tags,
          userId: this.currentUser.uid,
          spacedRepetition: {
            interval: 1,
            easeFactor: 2.5,
            dueDate: firebase.firestore.Timestamp.now(),
            reviewCount: 0
          }
        };

        if (this.cardEditor.currentCard) {
          // Update existing card
          cardData.confidence = this.cardEditor.currentCard.confidence;
          cardData.correct = this.cardEditor.currentCard.correct;
          cardData.incorrect = this.cardEditor.currentCard.incorrect;
          cardData.created = this.cardEditor.currentCard.created;
          cardData.spacedRepetition = this.cardEditor.currentCard.spacedRepetition || cardData.spacedRepetition;

          if (this.currentUser.uid !== 'guest') {
            await db.collection('cards').doc(this.cardEditor.currentCard.id).update(cardData);
          } else {
            const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
            const cardIndex = localCards.findIndex(c => c.id === this.cardEditor.currentCard.id);
            if (cardIndex >= 0) {
              localCards[cardIndex] = { ...cardData, id: this.cardEditor.currentCard.id };
              localStorage.setItem('chunking_cards', JSON.stringify(localCards));
            }
          }

          this.showMessage('Card updated successfully! ✨', 'success');
        } else {
          // Create new card
          cardData.confidence = 0;
          cardData.correct = 0;
          cardData.incorrect = 0;
          cardData.created = firebase.firestore.FieldValue.serverTimestamp();

          if (this.currentUser.uid !== 'guest') {
            const docRef = await db.collection('cards').add(cardData);
            cardData.id = docRef.id;
          } else {
            cardData.id = 'local_' + Date.now() + Math.random();
            cardData.created = new Date();
            const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
            localCards.push(cardData);
            localStorage.setItem('chunking_cards', JSON.stringify(localCards));
          }

          // Update deck card count
          await this.updateDeckCardCount(this.currentDeck.id, 1);
          this.showMessage('Card created successfully! ✨', 'success');
        }

        await this.loadCards();
        await this.loadDecks();
        this.renderDeckCards();
        this.closeCardEditor();
      } catch (error) {
        console.error('Error saving card:', error);
        this.showMessage('Error saving card', 'error');
      }
      this.closeCardEditor();
    },

    async updateDeckCardCount(deckId, change) {
      const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
      const deckIndex = localDecks.findIndex(d => d.id === deckId);
      if (deckIndex >= 0) {
        localDecks[deckIndex].cardCount = Math.max(0, (localDecks[deckIndex].cardCount || 0) + change);
        localStorage.setItem('chunking_decks', JSON.stringify(localDecks));
      }
    },

    async loadCards() {
      try {
        if (firebaseAvailable && this.currentUser.uid !== 'guest' && !this.currentUser.uid.startsWith('local_')) {
          // Load from Firebase
          const snapshot = await db.collection('cards')
            .where('userId', '==', this.currentUser.uid)
            .get();

          this.allCards = [];
          snapshot.forEach(doc => {
            const cardData = doc.data();
            cardData.id = doc.id;
            this.allCards.push(cardData);
          });
        } else {
          // Load from localStorage for guest users or when Firebase is unavailable
          const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
          this.allCards = localCards.filter(card => card.userId === this.currentUser.uid);
        }

        this.updateDeckSelects();
      } catch (error) {
        console.error('Error loading cards:', error);
        this.showMessage('Error loading cards', 'error');
      }
    },

    async deleteCardById(cardId, showConfirmation = true) {
      try {
        const card = this.allCards.find(c => c.id === cardId);
        if (!card) return;

        if (showConfirmation) {
          const message = `Are you sure you want to delete this card?\n\nFront: ${card.front?.substring(0, 50)}${card.front?.length > 50 ? '...' : ''}\n\nThis action cannot be undone.`;
          if (!confirm(message)) return;
        }

        // Delete from Firebase or localStorage
        if (this.currentUser.uid !== 'guest') {
          await db.collection('cards').doc(cardId).delete();
        } else {
          const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
          const filteredCards = localCards.filter(card => card.id !== cardId);
          localStorage.setItem('chunking_cards', JSON.stringify(filteredCards));
        }

        // Update local array
        this.allCards = this.allCards.filter(card => card.id !== cardId);

        // Update deck card count
        if (card.deckId) {
          await this.updateDeckCardCount(card.deckId, -1);
        }

        if (showConfirmation) {
          this.showMessage('Card deleted successfully', 'success');
        }
      } catch (error) {
        console.error('Error deleting card:', error);
        this.showMessage('Error deleting card', 'error');
      }
    },

    async deleteCard(cardId) {
      const card = this.allCards.find(c => c.id === cardId);
      if (!card) return;

      const message = `Are you sure you want to delete this card?\n\nFront: ${card.front?.substring(0, 50)}${card.front?.length > 50 ? '...' : ''}\n\nThis action cannot be undone.`;
      if (!confirm(message)) return;

      try {
        // Delete from Firebase or localStorage
        if (this.currentUser.uid !== 'guest') {
          await db.collection('cards').doc(cardId).delete();
        } else {
          const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
          const filteredCards = localCards.filter(card => card.id !== cardId);
          localStorage.setItem('chunking_cards', JSON.stringify(filteredCards));
        }

        // Update deck card count
        if (card.deckId) {
          await this.updateDeckCardCount(card.deckId, -1);
        }

        // Update local array
        this.allCards = this.allCards.filter(c => c.id !== cardId);

        // Refresh UI
        await this.loadCards();
        await this.loadDecks();

        if (this.currentDeck) {
          this.renderDeckCards();
        } else {
          this.filterCards();
        }

        this.showMessage('Card deleted successfully! 🗑️', 'success');
      } catch (error) {
        console.error('Error deleting card:', error);
        this.showMessage('Error deleting card', 'error');
      }
    },

    // Spaced Repetition Algorithm
    calculateSpacedRepetition: function(card) {
      if (!card.spacedRepetition) {
        return { status: 'new', label: 'New' };
      }

      const now = new Date();
      const dueDate = new Date(card.spacedRepetition.dueDate);

      if (card.spacedRepetition.reviewCount >= 5 && card.spacedRepetition.easeFactor >= 2.5) {
        return { status: 'mastered', label: 'Mastered' };
      }

      if (dueDate <= now) {
        return { status: 'due', label: 'Due' };
      }

      const daysUntilDue = Math.ceil((dueDate - now) / (1000 * 60 * 60 * 24));
      if (daysUntilDue <= 1) {
        return { status: 'soon', label: 'Soon' };
      }

      return { status: 'learning', label: `${daysUntilDue}d` };
    },

    updateSpacedRepetition: function(card, difficulty) {
      if (!card.spacedRepetition) {
        card.spacedRepetition = {
          interval: 1,
          easeFactor: 2.5,
          dueDate: new Date(),
          reviewCount: 0
        };
      }

      const sr = card.spacedRepetition;
      sr.reviewCount++;

      let newInterval = sr.interval;
      let newEaseFactor = sr.easeFactor;

      switch (difficulty) {
        case 'again':
          newInterval = 1;
          newEaseFactor = Math.max(1.3, sr.easeFactor - 0.2);
          break;
        case 'hard':
          newInterval = Math.max(1, Math.floor(sr.interval * 1.2));
          newEaseFactor = Math.max(1.3, sr.easeFactor - 0.15);
          break;
        case 'good':
          if (sr.reviewCount === 1) {
            newInterval = 6;
          } else {
            newInterval = Math.floor(sr.interval * sr.easeFactor);
          }
          break;
        case 'easy':
          newInterval = Math.floor(sr.interval * sr.easeFactor * 1.3);
          newEaseFactor = sr.easeFactor + 0.15;
          break;
      }

      sr.interval = newInterval;
      sr.easeFactor = newEaseFactor;

      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + newInterval);
      sr.dueDate = dueDate;

      return sr;
    },

    getDueCards: function(deckId = null) {
      const now = new Date();
      let cards = this.allCards;

      if (deckId) {
        cards = cards.filter(card => card.deckId === deckId);
      }

      return cards.filter(card => {
        if (!card.spacedRepetition) return true; // New cards are due
        const dueDate = new Date(card.spacedRepetition.dueDate);
        return dueDate <= now;
      });
    },

    // Study Sessions with Spaced Repetition
    startSpacedRepetition: function() {
      const deckId = document.getElementById('study-deck-select').value;

      if (!deckId) {
        this.showMessage('Please select a deck', 'error');
        return;
      }

      const dueCards = this.getDueCards(deckId);
      if (dueCards.length === 0) {
        this.showMessage('No cards are due for review in this deck! 🎉', 'success');
        return;
      }

      this.currentStudySession = {
        deckId: deckId,
        cards: [...dueCards],
        currentCardIndex: 0,
        isFlipped: false,
        mode: 'spaced',
        startTime: Date.now(),
        correctAnswers: 0
      };

      this.shuffleArray(this.currentStudySession.cards);
      this.startStudyInterface();
    },

    startChunkingStudy: function() {
      const deckId = document.getElementById('study-deck-select').value;
      const chunkSize = parseInt(document.getElementById('chunk-size').value);

      if (!deckId) {
        this.showMessage('Please select a deck', 'error');
        return;
      }

      const deckCards = this.allCards.filter(card => card.deckId === deckId);
      if (deckCards.length === 0) {
        this.showMessage('This deck has no cards', 'error');
        return;
      }

      this.currentStudySession = {
        deckId: deckId,
        cards: [...deckCards],
        chunkSize: chunkSize,
        currentChunk: 0,
        currentCardIndex: 0,
        isFlipped: false,
        mode: 'chunking',
        startTime: Date.now(),
        correctAnswers: 0
      };

      this.shuffleArray(this.currentStudySession.cards);
      this.startStudyInterface();
    },

    startFullReview: function() {
      const deckId = document.getElementById('study-deck-select').value;
      const shouldShuffle = document.getElementById('shuffle-cards').checked;

      if (!deckId) {
        this.showMessage('Please select a deck', 'error');
        return;
      }

      const deckCards = this.allCards.filter(card => card.deckId === deckId);
      if (deckCards.length === 0) {
        this.showMessage('This deck has no cards', 'error');
        return;
      }

      this.currentStudySession = {
        deckId: deckId,
        cards: [...deckCards],
        currentCardIndex: 0,
        isFlipped: false,
        mode: 'review',
        startTime: Date.now(),
        correctAnswers: 0
      };

      if (shouldShuffle) {
        this.shuffleArray(this.currentStudySession.cards);
      }

      this.startStudyInterface();
    },

    startStudyInterface: function() {
      document.getElementById('study-setup').style.display = 'none';
      document.getElementById('study-session').style.display = 'block';
      this.loadNextCard();
    },

    loadNextCard: function() {
      const session = this.currentStudySession;
      if (!session) return;

      if (session.currentCardIndex >= session.cards.length) {
        this.completeStudySession();
        return;
      }

      const card = session.cards[session.currentCardIndex];
      session.isFlipped = false;

      this.updateProgressBars();
      this.displayStudyCard(card);
    },

    displayStudyCard: function(card) {
      const cardElement = document.getElementById('card-content');
      const confidenceIndicator = document.querySelector('.confidence-indicator');
      const spacedIndicator = document.querySelector('.spaced-repetition-indicator');

      // Clear previous content
      cardElement.innerHTML = '';

      // Add text content
      const textElement = document.createElement('div');
      textElement.textContent = card.front;
      textElement.style.marginBottom = '10px';
      cardElement.appendChild(textElement);

      // Add front media if exists
      if (card.frontMedia) {
        const mediaElement = this.createMediaElement(card.frontMedia);
        if (mediaElement) {
          cardElement.appendChild(mediaElement);
        }
      }

      // Update indicators
      const confidence = card.confidence || 0;
      confidenceIndicator.className = 'confidence-indicator ' +
              (confidence >= 67 ? 'confidence-high' :
                      confidence >= 34 ? 'confidence-medium' : 'confidence-low');

      const spacedRepetition = this.calculateSpacedRepetition(card);
      spacedIndicator.className = `spaced-repetition-indicator ${spacedRepetition.status === 'due' ? 'due' : spacedRepetition.status === 'mastered' ? 'mastered' : ''}`;
      spacedIndicator.textContent = spacedRepetition.label;

      // Show appropriate controls
      if (this.currentStudySession.mode === 'spaced') {
        document.getElementById('flip-btn').style.display = 'block';
        document.getElementById('again-btn').style.display = 'none';
        document.getElementById('hard-btn').style.display = 'none';
        document.getElementById('good-btn').style.display = 'none';
        document.getElementById('easy-btn').style.display = 'none';
        document.getElementById('correct-btn').style.display = 'none';
        document.getElementById('incorrect-btn').style.display = 'none';
      } else {
        document.getElementById('flip-btn').style.display = 'block';
        document.getElementById('correct-btn').style.display = 'none';
        document.getElementById('incorrect-btn').style.display = 'none';
        document.getElementById('again-btn').style.display = 'none';
        document.getElementById('hard-btn').style.display = 'none';
        document.getElementById('good-btn').style.display = 'none';
        document.getElementById('easy-btn').style.display = 'none';
      }

      document.getElementById('flashcard').classList.remove('flipped');
    },

    flipCard: function() {
      const session = this.currentStudySession;
      if (!session || session.isFlipped) return;

      let card = session.cards[session.currentCardIndex];
      const cardElement = document.getElementById('card-content');

      // Clear previous content
      cardElement.innerHTML = '';

      // Add back text content
      const textElement = document.createElement('div');
      textElement.textContent = card.back;
      textElement.style.marginBottom = '10px';
      cardElement.appendChild(textElement);

      // Add back media if exists
      if (card.backMedia) {
        const mediaElement = this.createMediaElement(card.backMedia);
        if (mediaElement) {
          cardElement.appendChild(mediaElement);
        }
      }

      document.getElementById('flashcard').classList.add('flipped');
      document.getElementById('flip-btn').style.display = 'none';

      if (session.mode === 'spaced') {
        document.getElementById('again-btn').style.display = 'block';
        document.getElementById('hard-btn').style.display = 'block';
        document.getElementById('good-btn').style.display = 'block';
        document.getElementById('easy-btn').style.display = 'block';
      } else {
        document.getElementById('correct-btn').style.display = 'block';
        document.getElementById('incorrect-btn').style.display = 'block';
      }

      session.isFlipped = true;
    },

    markDifficulty: function(difficulty) {
      const session = this.currentStudySession;
      if (!session) return;

      const card = session.cards[session.currentCardIndex];

      // Update spaced repetition
      this.updateSpacedRepetition(card, difficulty);

      // Update confidence based on difficulty
      if (difficulty === 'again') {
        card.incorrect = (card.incorrect || 0) + 1;
        card.confidence = card.correct ? Math.round((card.correct / (card.correct + card.incorrect)) * 100) : 0;
      } else {
        card.correct = (card.correct || 0) + 1;
        card.confidence = Math.round((card.correct / (card.correct + (card.incorrect || 0))) * 100);
        session.correctAnswers++;
      }

      this.updateCardInStorage(card);
      session.currentCardIndex++;
      this.loadNextCard();
    },

    markCorrect: function() {
      const session = this.currentStudySession;
      if (!session) return;

      const card = session.cards[session.currentCardIndex];
      card.correct = (card.correct || 0) + 1;
      card.confidence = Math.round((card.correct / (card.correct + (card.incorrect || 0))) * 100);
      session.correctAnswers++;

      this.updateCardInStorage(card);
      session.currentCardIndex++;
      this.loadNextCard();
    },

    markIncorrect: function() {
      const session = this.currentStudySession;
      if (!session) return;

      const card = session.cards[session.currentCardIndex];
      card.incorrect = (card.incorrect || 0) + 1;
      card.confidence = card.correct ? Math.round((card.correct / (card.correct + card.incorrect)) * 100) : 0;

      this.updateCardInStorage(card);
      session.currentCardIndex++;
      this.loadNextCard();
    },

    async updateCardInStorage(card) {
      const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
      const cardIndex = localCards.findIndex(c => c.id === card.id);
      if (cardIndex >= 0) {
        localCards[cardIndex] = { ...localCards[cardIndex], ...card };
        localStorage.setItem('chunking_cards', JSON.stringify(localCards));
      }
    },

    updateProgressBars: function() {
      const session = this.currentStudySession;
      if (!session) return;

      const progress = ((session.currentCardIndex + 1) / session.cards.length) * 100;
      document.getElementById('session-progress-bar').style.width = progress + '%';

      let sessionInfo = '';
      if (session.mode === 'spaced') {
        sessionInfo = 'Smart Study Session';
      } else if (session.mode === 'chunking') {
        sessionInfo = `Chunk ${Math.floor(session.currentCardIndex / session.chunkSize) + 1}`;
      } else {
        sessionInfo = 'Full Review';
      }

      document.getElementById('session-info').textContent = sessionInfo;
      document.getElementById('session-progress').textContent =
              `Card ${session.currentCardIndex + 1} of ${session.cards.length}`;
    },

    completeStudySession: function() {
      const session = this.currentStudySession;
      const studyTime = (Date.now() - session.startTime) / 1000 / 60; // minutes
      const accuracy = session.cards.length > 0 ? (session.correctAnswers / session.cards.length) * 100 : 0;

      // Update statistics
      this.studyStatistics.totalStudyTime += studyTime;
      this.studyStatistics.sessionsCompleted++;
      this.studyStatistics.accuracyHistory.push({
        date: new Date(),
        accuracy: accuracy,
        cardsStudied: session.cards.length,
        studyTime: studyTime
      });

      // Update daily study time
      const today = new Date().toDateString();
      this.studyStatistics.dailyStudyTime[today] = (this.studyStatistics.dailyStudyTime[today] || 0) + studyTime;

      this.saveStudyStatistics();

      this.showMessage(`🎉 Session completed! Studied ${session.cards.length} cards with ${Math.round(accuracy)}% accuracy in ${Math.round(studyTime)} minutes`, 'success');
      this.endStudySession();
    },

    endStudySession: function() {
      this.currentStudySession = null;
      document.getElementById('study-setup').style.display = 'block';
      document.getElementById('study-session').style.display = 'none';
      this.updateDashboard();
    },

    // Statistics and Analytics
    loadStudyStatistics: function() {
      const saved = localStorage.getItem('chunking_statistics');
      if (saved) {
        this.studyStatistics = { ...this.studyStatistics, ...JSON.parse(saved) };
      }
    },

    saveStudyStatistics: function() {
      localStorage.setItem('chunking_statistics', JSON.stringify(this.studyStatistics));
    },

    updateDashboard: function() {
      const dueCards = this.getDueCards().length;
      const masteredCards = this.allCards.filter(card => {
        const sr = this.calculateSpacedRepetition(card);
        return sr.status === 'mastered';
      }).length;

      // Calculate streak
      const today = new Date().toDateString();
      let streak = 0;
      let currentDate = new Date();
      while (this.studyStatistics.dailyStudyTime[currentDate.toDateString()]) {
        streak++;
        currentDate.setDate(currentDate.getDate() - 1);
      }

      document.getElementById('cards-due').textContent = dueCards;
      document.getElementById('streak-count').textContent = streak;
      document.getElementById('mastered-cards').textContent = masteredCards;
    },

    updateAnalytics: function() {
      const stats = this.studyStatistics;

      document.getElementById('total-study-time').textContent = `${Math.round(stats.totalStudyTime)}m`;

      const avgAccuracy = stats.accuracyHistory.length > 0 ?
              Math.round(stats.accuracyHistory.reduce((sum, entry) => sum + entry.accuracy, 0) / stats.accuracyHistory.length) : 0;
      document.getElementById('avg-accuracy').textContent = `${avgAccuracy}%`;

      const totalCardsReviewed = stats.accuracyHistory.reduce((sum, entry) => sum + entry.cardsStudied, 0);
      document.getElementById('cards-reviewed').textContent = totalCardsReviewed;

      const difficultCards = this.allCards.filter(card => (card.confidence || 0) < 34).length;
      document.getElementById('difficult-cards').textContent = difficultCards;

      this.updatePerformanceInsights();
      this.drawCharts();
    },

    updatePerformanceInsights: function() {
      const insights = [];
      const stats = this.studyStatistics;

      if (stats.accuracyHistory.length >= 5) {
        const recent = stats.accuracyHistory.slice(-5);
        const avgRecent = recent.reduce((sum, entry) => sum + entry.accuracy, 0) / recent.length;
        const older = stats.accuracyHistory.slice(-10, -5);
        if (older.length > 0) {
          const avgOlder = older.reduce((sum, entry) => sum + entry.accuracy, 0) / older.length;
          if (avgRecent > avgOlder + 5) {
            insights.push('📈 Your accuracy is improving! Keep up the great work.');
          } else if (avgRecent < avgOlder - 5) {
            insights.push('📉 Consider reviewing difficult cards more frequently.');
          }
        }
      }

      const difficultCards = this.allCards.filter(card => (card.confidence || 0) < 34);
      if (difficultCards.length > 0) {
        insights.push(`🎯 You have ${difficultCards.length} cards that need extra attention.`);
      }

      const masteredCards = this.allCards.filter(card => {
        const sr = this.calculateSpacedRepetition(card);
        return sr.status === 'mastered';
      });
      if (masteredCards.length > 0) {
        insights.push(`🏆 Excellent! You've mastered ${masteredCards.length} cards.`);
      }

      if (insights.length === 0) {
        insights.push('📊 Complete more study sessions to see detailed insights.');
      }

      document.getElementById('performance-insights').innerHTML = insights.map(insight => `<p>${insight}</p>`).join('');
    },

    drawCharts: function() {
      // Simple text-based charts since we don't have Chart.js
      const accuracyChart = document.getElementById('accuracy-chart');
      const timeChart = document.getElementById('time-chart');

      if (accuracyChart) {
        accuracyChart.style.display = 'none';
        const parent = accuracyChart.parentNode;
        const chartText = document.createElement('div');
        chartText.innerHTML = '<p>📈 Accuracy trend visualization would appear here with Chart.js integration</p>';
        parent.appendChild(chartText);
      }

      if (timeChart) {
        timeChart.style.display = 'none';
        const parent = timeChart.parentNode;
        const chartText = document.createElement('div');
        chartText.innerHTML = '<p>⏱️ Study time visualization would appear here with Chart.js integration</p>';
        parent.appendChild(chartText);
      }
    },

    // Import/Export and Utilities
    updateDeckSelects: function() {
      const selects = ['study-deck-select', 'import-deck', 'manage-deck'];
      selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (!select) return;

        const currentValue = select.value;

        if (selectId === 'manage-deck') {
          select.innerHTML = '<option value="">All Decks</option>';
        } else {
          select.innerHTML = '<option value="">Choose a deck...</option>';
        }

        this.allDecks.forEach(deck => {
          const option = document.createElement('option');
          option.value = deck.id;
          option.textContent = deck.name;
          select.appendChild(option);
        });

        select.value = currentValue;
      });
    },

    importCards: function() {
      const deckId = document.getElementById('import-deck').value;
      if (!deckId) {
        this.showMessage('Please select a deck first', 'error');
        return;
      }

      const csvText = document.getElementById('csv-text').value;
      const fileInput = document.getElementById('csv-file');

      if (fileInput.files[0] && !csvText) {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.processCSVContent(e.target.result, deckId);
        };
        reader.readAsText(fileInput.files[0]);
      } else if (csvText) {
        this.processCSVContent(csvText, deckId);
      } else {
        this.showMessage('Please provide CSV content either by file or text', 'error');
      }
    },

    processCSVContent: function(csvContent, deckId) {
      try {
        const lines = csvContent.split('\n').filter(line => line.trim());
        const cards = [];

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          if (!line) continue;

          const parts = this.parseCSVLine(line);
          const [front, back, tagsStr] = parts;

          if (front && back) {
            const tags = tagsStr ? tagsStr.split(';').map(tag => tag.trim()).filter(tag => tag) : [];

            cards.push({
              id: 'local_' + Date.now() + Math.random(),
              deckId: deckId,
              front: front.trim(),
              back: back.trim(),
              tags: tags,
              confidence: 0,
              correct: 0,
              incorrect: 0,
              created: new Date(),
              userId: this.currentUser.uid,
              spacedRepetition: {
                interval: 1,
                easeFactor: 2.5,
                dueDate: new Date(),
                reviewCount: 0
              }
            });
          }
        }

        if (cards.length === 0) {
          this.showMessage('No valid cards found in CSV. Format: Front,Back,Tags', 'error');
          return;
        }

        const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
        localCards.push(...cards);
        localStorage.setItem('chunking_cards', JSON.stringify(localCards));

        this.updateDeckCardCount(deckId, cards.length);
        this.loadCards();
        this.loadDecks();
        document.getElementById('csv-text').value = '';
        document.getElementById('csv-file').value = '';
        this.showMessage(`Successfully imported ${cards.length} cards! 🎉`, 'success');

      } catch (error) {
        console.error('Error importing cards:', error);
        this.showMessage('Error importing cards. Please check the CSV format.', 'error');
      }
    },

    parseCSVLine: function(line) {
      const result = [];
      let current = '';
      let inQuotes = false;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          result.push(current);
          current = '';
        } else {
          current += char;
        }
      }
      result.push(current);

      return result.map(item => item.replace(/^"|"$/g, ''));
    },

    filterCards: function() {
      const deckFilter = document.getElementById('manage-deck').value;
      const searchText = document.getElementById('search-cards').value.toLowerCase();
      const confidenceFilter = document.getElementById('confidence-filter').value;

      let filteredCards = this.allCards;

      if (deckFilter) {
        filteredCards = filteredCards.filter(card => card.deckId === deckFilter);
      }

      if (searchText) {
        filteredCards = filteredCards.filter(card =>
                card.front.toLowerCase().includes(searchText) ||
                card.back.toLowerCase().includes(searchText) ||
                (card.tags && card.tags.some(tag => tag.toLowerCase().includes(searchText)))
        );
      }

      if (confidenceFilter) {
        filteredCards = filteredCards.filter(card => {
          const confidence = card.confidence || 0;
          switch (confidenceFilter) {
            case 'low': return confidence <= 33;
            case 'medium': return confidence > 33 && confidence <= 66;
            case 'high': return confidence > 66;
            default: return true;
          }
        });
      }

      this.renderCardList(filteredCards);
    },

    renderCardList: function(cards) {
      const listElement = document.getElementById('card-list');

      if (cards.length === 0) {
        listElement.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: var(--text-color); opacity: 0.8;">
                            <div style="font-size: 2em; margin-bottom: 15px;">🔍</div>
                            <h3 style="color: var(--text-color); margin-bottom: 10px;">No cards found</h3>
                            <p style="color: var(--text-color); opacity: 0.7;">Try adjusting your filters.</p>
                        </div>
                    `;
        return;
      }

      listElement.innerHTML = cards.map(card => {
        const deck = this.allDecks.find(d => d.id === card.deckId);
        const confidence = card.confidence || 0;
        const confidenceClass = confidence >= 67 ? 'confidence-high' :
                confidence >= 34 ? 'confidence-medium' : 'confidence-low';

        const hasMedia = card.frontMedia || card.backMedia;
        const mediaIndicator = hasMedia ? ' 📎' : '';

        const tags = card.tags && card.tags.length > 0 ?
                `<div class="tags-container">${card.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}</div>` : '';

        return `
                        <div class="card-item" style="padding: 20px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--primary-color); margin-bottom: 5px;">${card.front}${mediaIndicator}</div>
                                <div style="color: var(--text-color); opacity: 0.8; margin-bottom: 8px;">${card.back}</div>
                                ${tags}
                                <div style="font-size: 0.85em; color: var(--text-color); opacity: 0.7;">
                                    📚 ${deck ? deck.name : 'Unknown Deck'} | 🎯 ${confidence}% confidence
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <span class="confidence-indicator ${confidenceClass}" style="position: relative; top: 0; right: 0;"></span>
                                <button class="btn btn-danger delete-card-btn" data-card-id="${card.id}" style="padding: 8px 15px; font-size: 0.9em;">
                                    🗑️ Delete
                                </button>
                            </div>
                        </div>
                    `;
      }).join('');

      listElement.querySelectorAll('.delete-card-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          const cardId = btn.getAttribute('data-card-id');
          this.deleteCard(cardId);
        });
      });
    },

    // Settings
    loadSettings: function() {
      const theme = localStorage.getItem('chunking_theme') || 'teal';
      const fontSize = localStorage.getItem('chunking_fontSize') || '16px';
      const cardFont = localStorage.getItem('chunking_cardFont') || 'inherit';
      const cardFontSize = localStorage.getItem('chunking_cardFontSize') || '1.3em';
      const cardHeight = localStorage.getItem('chunking_cardHeight') || '250px';
      const cardBgColor = localStorage.getItem('chunking_cardBgColor') || '#ffffff';
      const cardTextColor = localStorage.getItem('chunking_cardTextColor') || '#1e293b';

      document.getElementById('theme-select').value = theme;
      document.getElementById('font-size').value = fontSize;
      document.getElementById('card-font').value = cardFont;
      document.getElementById('card-font-size').value = cardFontSize;
      document.getElementById('card-height').value = cardHeight;
      document.getElementById('card-bg-color').value = cardBgColor;
      document.getElementById('card-text-color').value = cardTextColor;

      this.changeTheme();
      this.changeFontSize();
      this.updateCardStyle();
    },

    changeTheme: function() {
      const theme = document.getElementById('theme-select').value;
      document.body.setAttribute('data-theme', theme);
      localStorage.setItem('chunking_theme', theme);
    },

    changeFontSize: function() {
      const fontSize = document.getElementById('font-size').value;
      document.body.style.fontSize = fontSize;
      localStorage.setItem('chunking_fontSize', fontSize);
    },

    updateCardStyle: function() {
      const cardFont = document.getElementById('card-font').value;
      const cardFontSize = document.getElementById('card-font-size').value;
      const cardHeight = document.getElementById('card-height').value;
      const cardBgColor = document.getElementById('card-bg-color').value;
      const cardTextColor = document.getElementById('card-text-color').value;

      document.documentElement.style.setProperty('--card-font', cardFont);
      document.documentElement.style.setProperty('--card-font-size', cardFontSize);
      document.documentElement.style.setProperty('--card-height', cardHeight);
      document.documentElement.style.setProperty('--card-background', cardBgColor);
      document.documentElement.style.setProperty('--card-text-color', cardTextColor);

      localStorage.setItem('chunking_cardFont', cardFont);
      localStorage.setItem('chunking_cardFontSize', cardFontSize);
      localStorage.setItem('chunking_cardHeight', cardHeight);
      localStorage.setItem('chunking_cardBgColor', cardBgColor);
      localStorage.setItem('chunking_cardTextColor', cardTextColor);
    },

    exportData: function() {
      try {
        const data = {
          decks: this.allDecks,
          cards: this.allCards,
          statistics: this.studyStatistics,
          settings: {
            theme: localStorage.getItem('chunking_theme'),
            fontSize: localStorage.getItem('chunking_fontSize'),
            cardFont: localStorage.getItem('chunking_cardFont'),
            cardFontSize: localStorage.getItem('chunking_cardFontSize'),
            cardHeight: localStorage.getItem('chunking_cardHeight'),
            cardBgColor: localStorage.getItem('chunking_cardBgColor'),
            cardTextColor: localStorage.getItem('chunking_cardTextColor')
          },
          version: '2.0',
          exported: new Date().toISOString(),
          user: this.currentUser.displayName || this.currentUser.email || 'Guest User'
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `enhanced-flashcards-backup-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        this.showMessage('Data exported successfully! 📤', 'success');
      } catch (error) {
        console.error('Error exporting data:', error);
        this.showMessage('Error exporting data. Please try again.', 'error');
      }
    },

    clearAllData: function() {
      if (!confirm('⚠️ WARNING: This will delete ALL your decks, cards, and statistics permanently!')) return;
      if (!confirm('Are you absolutely sure? This action cannot be undone!')) return;
      if (!confirm('Final confirmation: Delete everything?')) return;

      try {
        localStorage.removeItem('chunking_decks');
        localStorage.removeItem('chunking_cards');
        localStorage.removeItem('chunking_statistics');

        this.allDecks = [];
        this.allCards = [];
        this.studyStatistics = {
          totalStudyTime: 0,
          sessionsCompleted: 0,
          accuracyHistory: [],
          dailyStudyTime: {}
        };
        this.currentStudySession = null;

        this.loadDecks();
        this.loadCards();
        this.updateDeckSelects();
        this.updateDashboard();

        if (document.getElementById('study-session').style.display !== 'none') {
          this.endStudySession();
        }

        this.showMessage('All data cleared successfully! 🗑️', 'success');
      } catch (error) {
        console.error('Error clearing data:', error);
        this.showMessage('Error clearing data. Please try again.', 'error');
      }
    },

    // ===== MOCK EXAM SYSTEM =====

    currentExam: null,
    examTimer: null,
    examStartTime: null,

    showExamSetup: function() {
      console.log('🎯 Showing exam setup');
      document.getElementById('exam-setup-view').style.display = 'block';
      document.getElementById('exam-taking-view').style.display = 'none';
      document.getElementById('exam-results-view').style.display = 'none';

      this.populateExamDeckSelection();
      this.loadExamHistory();
    },

    populateExamDeckSelection: function() {
      const container = document.getElementById('exam-deck-selection');
      if (this.allDecks.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: var(--text-color); opacity: 0.7;">No decks available. Create some decks first!</p>';
        return;
      }

      container.innerHTML = this.allDecks.map(deck => {
        const cardCount = this.allCards.filter(card => card.deckId === deck.id).length;
        return `
          <label style="display: flex; align-items: center; gap: 10px; padding: 10px; border-radius: 8px; cursor: pointer; transition: background 0.2s;"
                 onmouseover="this.style.background='var(--hover-color)'"
                 onmouseout="this.style.background='transparent'">
            <input type="checkbox" class="exam-deck-checkbox" value="${deck.id}" ${cardCount > 0 ? '' : 'disabled'}>
            <div style="flex: 1;">
              <div style="font-weight: 600; color: var(--text-color);">${deck.name}</div>
              <div style="font-size: 0.9em; color: var(--text-color); opacity: 0.7;">${cardCount} cards available</div>
            </div>
          </label>
        `;
      }).join('');
    },

    startMockExam: function() {
      console.log('🚀 Starting mock exam');

      const examName = document.getElementById('exam-name').value.trim() || 'Mock Exam';
      const selectedDecks = Array.from(document.querySelectorAll('.exam-deck-checkbox:checked')).map(cb => cb.value);
      const questionCount = document.getElementById('exam-question-count').value;
      const timeLimit = parseInt(document.getElementById('exam-time-limit').value);
      const includeFlashcard = document.getElementById('exam-type-flashcard').checked;
      const includeReverse = document.getElementById('exam-type-reverse').checked;

      if (selectedDecks.length === 0) {
        this.showMessage('Please select at least one deck', 'error');
        return;
      }

      if (!includeFlashcard && !includeReverse) {
        this.showMessage('Please select at least one question type', 'error');
        return;
      }

      // Get cards from selected decks
      let availableCards = this.allCards.filter(card => selectedDecks.includes(card.deckId));

      if (availableCards.length === 0) {
        this.showMessage('No cards available in selected decks', 'error');
        return;
      }

      // Create question pool
      let questions = [];

      if (includeFlashcard) {
        questions.push(...availableCards.map(card => ({
          id: card.id,
          type: 'flashcard',
          question: card.front,
          answer: card.back,
          deckId: card.deckId,
          tags: card.tags || []
        })));
      }

      if (includeReverse) {
        questions.push(...availableCards.map(card => ({
          id: card.id + '_reverse',
          type: 'reverse',
          question: card.back,
          answer: card.front,
          deckId: card.deckId,
          tags: card.tags || []
        })));
      }

      // Shuffle and limit questions
      this.shuffleArray(questions);
      const finalQuestionCount = questionCount === 'all' ? questions.length : Math.min(parseInt(questionCount), questions.length);
      questions = questions.slice(0, finalQuestionCount);

      // Initialize exam
      this.currentExam = {
        name: examName,
        questions: questions,
        currentQuestionIndex: 0,
        answers: [],
        startTime: new Date(),
        timeLimit: timeLimit,
        timeRemaining: timeLimit * 60, // Convert to seconds
        selectedDecks: selectedDecks
      };

      this.examStartTime = Date.now();

      // Start timer if time limit is set
      if (timeLimit > 0) {
        this.startExamTimer();
      }

      // Show exam interface
      document.getElementById('exam-setup-view').style.display = 'none';
      document.getElementById('exam-taking-view').style.display = 'block';
      document.getElementById('current-exam-name').textContent = examName;

      this.showCurrentExamQuestion();
    },

    startExamTimer: function() {
      const timerDisplay = document.getElementById('exam-timer');

      this.examTimer = setInterval(() => {
        this.currentExam.timeRemaining--;

        const minutes = Math.floor(this.currentExam.timeRemaining / 60);
        const seconds = this.currentExam.timeRemaining % 60;
        timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

        // Change color when time is running low
        if (this.currentExam.timeRemaining <= 300) { // 5 minutes
          timerDisplay.style.color = '#ef4444';
        } else if (this.currentExam.timeRemaining <= 600) { // 10 minutes
          timerDisplay.style.color = '#f59e0b';
        }

        if (this.currentExam.timeRemaining <= 0) {
          this.endMockExam();
        }
      }, 1000);
    },

    showCurrentExamQuestion: function() {
      const question = this.currentExam.questions[this.currentExam.currentQuestionIndex];
      const progress = `Question ${this.currentExam.currentQuestionIndex + 1} of ${this.currentExam.questions.length}`;

      document.getElementById('exam-progress').textContent = progress;
      document.getElementById('exam-question-content').innerHTML = `
        <div style="margin-bottom: 15px; font-size: 0.9em; color: var(--text-color); opacity: 0.7;">
          ${question.type === 'flashcard' ? 'Front → Back' : 'Back → Front'}
        </div>
        <h4 style="color: var(--text-color); margin-bottom: 30px; line-height: 1.4;">${question.question}</h4>
      `;

      // Reset answer section
      document.getElementById('exam-answer-input').value = '';
      document.getElementById('exam-answer-reveal').style.display = 'none';
      document.getElementById('exam-show-answer-btn').style.display = 'inline-block';
      document.getElementById('exam-next-question-btn').style.display = 'none';
    },

    showExamAnswer: function() {
      const question = this.currentExam.questions[this.currentExam.currentQuestionIndex];
      const userAnswer = document.getElementById('exam-answer-input').value.trim();

      // Store user's answer
      this.currentExam.answers[this.currentExam.currentQuestionIndex] = {
        questionId: question.id,
        userAnswer: userAnswer,
        correctAnswer: question.answer,
        question: question.question,
        type: question.type,
        deckId: question.deckId,
        tags: question.tags
      };

      // Show correct answer
      document.getElementById('exam-correct-answer').innerHTML = question.answer;
      document.getElementById('exam-answer-reveal').style.display = 'block';
      document.getElementById('exam-show-answer-btn').style.display = 'none';
    },

    markExamAnswer: function(isCorrect) {
      // Update the answer with correctness
      this.currentExam.answers[this.currentExam.currentQuestionIndex].isCorrect = isCorrect;

      // Show next question button
      document.getElementById('exam-next-question-btn').style.display = 'inline-block';
      document.getElementById('exam-mark-correct-btn').style.display = 'none';
      document.getElementById('exam-mark-incorrect-btn').style.display = 'none';
    },

    nextExamQuestion: function() {
      this.currentExam.currentQuestionIndex++;

      if (this.currentExam.currentQuestionIndex >= this.currentExam.questions.length) {
        this.endMockExam();
      } else {
        this.showCurrentExamQuestion();
      }
    },

    endMockExam: function() {
      if (this.examTimer) {
        clearInterval(this.examTimer);
        this.examTimer = null;
      }

      // Calculate results
      const results = this.calculateExamResults();

      // Save exam to history
      this.saveExamToHistory(results);

      // Show results
      this.showExamResults(results);
    },

    calculateExamResults: function() {
      const totalQuestions = this.currentExam.questions.length;
      const answeredQuestions = this.currentExam.answers.filter(a => a.isCorrect !== undefined).length;
      const correctAnswers = this.currentExam.answers.filter(a => a.isCorrect === true).length;
      const incorrectAnswers = this.currentExam.answers.filter(a => a.isCorrect === false).length;

      const score = answeredQuestions > 0 ? Math.round((correctAnswers / answeredQuestions) * 100) : 0;
      const completionTime = Math.round((Date.now() - this.examStartTime) / 1000 / 60); // minutes

      // Analyze weak areas
      const weakAreas = this.analyzeWeakAreas();

      return {
        examName: this.currentExam.name,
        totalQuestions,
        answeredQuestions,
        correctAnswers,
        incorrectAnswers,
        score,
        completionTime,
        weakAreas,
        answers: this.currentExam.answers,
        selectedDecks: this.currentExam.selectedDecks,
        date: new Date()
      };
    },

    analyzeWeakAreas: function() {
      const incorrectAnswers = this.currentExam.answers.filter(a => a.isCorrect === false);
      const deckAnalysis = {};
      const tagAnalysis = {};

      incorrectAnswers.forEach(answer => {
        // Analyze by deck
        const deck = this.allDecks.find(d => d.id === answer.deckId);
        if (deck) {
          if (!deckAnalysis[deck.name]) {
            deckAnalysis[deck.name] = { count: 0, deckId: deck.id };
          }
          deckAnalysis[deck.name].count++;
        }

        // Analyze by tags
        if (answer.tags && answer.tags.length > 0) {
          answer.tags.forEach(tag => {
            if (!tagAnalysis[tag]) {
              tagAnalysis[tag] = 0;
            }
            tagAnalysis[tag]++;
          });
        }
      });

      return {
        decks: Object.entries(deckAnalysis)
          .sort(([,a], [,b]) => b.count - a.count)
          .slice(0, 5), // Top 5 problematic decks
        tags: Object.entries(tagAnalysis)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 5) // Top 5 problematic tags
      };
    },

    saveExamToHistory: function(results) {
      const examHistory = JSON.parse(localStorage.getItem('chunking_exam_history') || '[]');
      examHistory.unshift(results); // Add to beginning

      // Keep only last 20 exams
      if (examHistory.length > 20) {
        examHistory.splice(20);
      }

      localStorage.setItem('chunking_exam_history', JSON.stringify(examHistory));
    },

    showExamResults: function(results) {
      document.getElementById('exam-taking-view').style.display = 'none';
      document.getElementById('exam-results-view').style.display = 'block';

      // Show score and grade
      document.getElementById('exam-final-score').textContent = `${results.score}%`;
      document.getElementById('exam-grade-text').textContent = this.getGradeText(results.score);
      document.getElementById('exam-completion-time').textContent = `Completed in ${results.completionTime} minutes`;

      // Show performance stats
      document.getElementById('exam-performance-stats').innerHTML = `
        <div style="margin-bottom: 10px;">
          <strong>Questions Answered:</strong> ${results.answeredQuestions} / ${results.totalQuestions}
        </div>
        <div style="margin-bottom: 10px;">
          <strong>Correct:</strong> <span style="color: #10b981;">${results.correctAnswers}</span>
        </div>
        <div style="margin-bottom: 10px;">
          <strong>Incorrect:</strong> <span style="color: #ef4444;">${results.incorrectAnswers}</span>
        </div>
        <div>
          <strong>Accuracy:</strong> ${results.score}%
        </div>
      `;

      // Show focus areas
      this.showFocusAreas(results.weakAreas);
    },

    shuffleArray: function(array) {
      for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
      }
    },

    getGradeText: function(score) {
      if (score >= 90) return '🏆 Excellent!';
      if (score >= 80) return '🎉 Great Job!';
      if (score >= 70) return '👍 Good Work!';
      if (score >= 60) return '📚 Keep Studying!';
      return '💪 More Practice Needed!';
    },

    showFocusAreas: function(weakAreas) {
      const container = document.getElementById('exam-focus-areas');

      if (weakAreas.decks.length === 0 && weakAreas.tags.length === 0) {
        container.innerHTML = '<p style="color: #10b981;">🎯 Great job! No major weak areas identified.</p>';
        return;
      }

      let html = '';

      if (weakAreas.decks.length > 0) {
        html += '<div style="margin-bottom: 15px;"><strong>Challenging Decks:</strong></div>';
        weakAreas.decks.forEach(([deckName, data]) => {
          html += `<div style="margin-bottom: 8px; padding: 8px; background: rgba(239, 68, 68, 0.1); border-radius: 6px;">
            📚 ${deckName} (${data.count} mistakes)
          </div>`;
        });
      }

      if (weakAreas.tags.length > 0) {
        html += '<div style="margin-bottom: 15px; margin-top: 20px;"><strong>Challenging Topics:</strong></div>';
        weakAreas.tags.forEach(([tag, count]) => {
          html += `<div style="margin-bottom: 8px; padding: 8px; background: rgba(245, 158, 11, 0.1); border-radius: 6px;">
            🏷️ ${tag} (${count} mistakes)
          </div>`;
        });
      }

      container.innerHTML = html;
    },

    createFocusDeck: function() {
      if (!this.currentExam || !this.currentExam.answers) {
        this.showMessage('No exam data available', 'error');
        return;
      }

      const incorrectAnswers = this.currentExam.answers.filter(a => a.isCorrect === false);

      if (incorrectAnswers.length === 0) {
        this.showMessage('No incorrect answers to create focus deck from!', 'error');
        return;
      }

      // Get unique card IDs from incorrect answers
      const cardIds = [...new Set(incorrectAnswers.map(a => a.questionId.replace('_reverse', '')))];
      const focusCards = this.allCards.filter(card => cardIds.includes(card.id));

      if (focusCards.length === 0) {
        this.showMessage('Unable to find cards for focus deck', 'error');
        return;
      }

      // Create focus deck
      const focusDeckName = `${this.currentExam.name} - Focus Areas`;
      const focusDeckData = {
        name: focusDeckName,
        description: `Auto-generated focus deck from exam mistakes (${focusCards.length} cards)`,
        created: new Date().toISOString(),
        cardCount: 0,
        userId: this.currentUser.uid,
        id: 'local_focus_' + Date.now()
      };

      // Save focus deck
      const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
      localDecks.push(focusDeckData);
      localStorage.setItem('chunking_decks', JSON.stringify(localDecks));

      // Create copies of focus cards for the new deck
      const focusDeckCards = focusCards.map(card => ({
        ...card,
        id: 'local_focus_card_' + Date.now() + '_' + Math.random(),
        deckId: focusDeckData.id,
        created: new Date().toISOString(),
        confidence: 0, // Reset confidence for focused study
        correct: 0,
        incorrect: 0
      }));

      // Save focus cards
      const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
      localCards.push(...focusDeckCards);
      localStorage.setItem('chunking_cards', JSON.stringify(localCards));

      // Update deck card count
      focusDeckData.cardCount = focusDeckCards.length;
      localStorage.setItem('chunking_decks', JSON.stringify(localDecks));

      // Refresh data
      this.loadDecks();
      this.loadCards();

      this.showMessage(`Focus deck "${focusDeckName}" created with ${focusDeckCards.length} cards! 📚`, 'success');
    },

    reviewMistakes: function() {
      if (!this.currentExam || !this.currentExam.answers) {
        this.showMessage('No exam data available', 'error');
        return;
      }

      const incorrectAnswers = this.currentExam.answers.filter(a => a.isCorrect === false);

      if (incorrectAnswers.length === 0) {
        this.showMessage('No mistakes to review - perfect score!', 'success');
        return;
      }

      // Create a simple review modal
      const reviewHtml = incorrectAnswers.map((answer, index) => `
        <div style="margin-bottom: 20px; padding: 20px; border: 1px solid var(--border-color); border-radius: 10px;">
          <div style="font-weight: 600; color: var(--primary-color); margin-bottom: 10px;">
            Question ${index + 1}: ${answer.type === 'flashcard' ? 'Front → Back' : 'Back → Front'}
          </div>
          <div style="margin-bottom: 10px;">
            <strong>Question:</strong> ${answer.question}
          </div>
          <div style="margin-bottom: 10px;">
            <strong>Your Answer:</strong> <span style="color: #ef4444;">${answer.userAnswer || '(No answer provided)'}</span>
          </div>
          <div>
            <strong>Correct Answer:</strong> <span style="color: #10b981;">${answer.correctAnswer}</span>
          </div>
        </div>
      `).join('');

      // Show in a simple alert for now (could be enhanced with a proper modal)
      const reviewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
      reviewWindow.document.write(`
        <html>
          <head><title>Exam Review - ${this.currentExam.name}</title></head>
          <body style="font-family: Arial, sans-serif; padding: 20px; line-height: 1.6;">
            <h2>📝 Exam Review: ${this.currentExam.name}</h2>
            <p><strong>Mistakes to Review:</strong> ${incorrectAnswers.length}</p>
            ${reviewHtml}
          </body>
        </html>
      `);
    },

    retakeExam: function() {
      // Reset to exam setup with same settings
      this.showExamSetup();

      // Pre-fill the form with previous exam settings
      if (this.currentExam) {
        document.getElementById('exam-name').value = this.currentExam.name + ' (Retake)';

        // Check the same decks
        this.currentExam.selectedDecks.forEach(deckId => {
          const checkbox = document.querySelector(`.exam-deck-checkbox[value="${deckId}"]`);
          if (checkbox) checkbox.checked = true;
        });
      }
    },

    loadExamHistory: function() {
      const examHistory = JSON.parse(localStorage.getItem('chunking_exam_history') || '[]');
      const container = document.getElementById('exam-history-list');

      if (examHistory.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: var(--text-color); opacity: 0.7;">No exams taken yet. Create your first mock exam above!</p>';
        return;
      }

      container.innerHTML = examHistory.slice(0, 5).map(exam => {
        const date = new Date(exam.date).toLocaleDateString();
        const scoreColor = exam.score >= 80 ? '#10b981' : exam.score >= 60 ? '#f59e0b' : '#ef4444';

        return `
          <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; border: 1px solid var(--border-color); border-radius: 10px; margin-bottom: 10px;">
            <div>
              <div style="font-weight: 600; color: var(--text-color);">${exam.examName}</div>
              <div style="font-size: 0.9em; color: var(--text-color); opacity: 0.7;">${date} • ${exam.completionTime} min</div>
            </div>
            <div style="text-align: right;">
              <div style="font-size: 1.2em; font-weight: 600; color: ${scoreColor};">${exam.score}%</div>
              <div style="font-size: 0.8em; color: var(--text-color); opacity: 0.7;">${exam.correctAnswers}/${exam.totalQuestions}</div>
            </div>
          </div>
        `;
      }).join('');
    },

    showMessage: function(message, type = 'success') {
      const messageDiv = document.createElement('div');
      messageDiv.className = type === 'success' ? 'success' : 'error';
      messageDiv.textContent = message;
      messageDiv.style.position = 'fixed';
      messageDiv.style.top = '25px';
      messageDiv.style.right = '25px';
      messageDiv.style.zIndex = '1001';
      messageDiv.style.padding = '15px 25px';
      messageDiv.style.borderRadius = '15px';
      messageDiv.style.maxWidth = '350px';
      messageDiv.style.boxShadow = '0 10px 30px rgba(0,0,0,0.2)';
      messageDiv.style.animation = 'slideIn 0.3s ease';

      document.body.appendChild(messageDiv);

      setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
          if (document.body.contains(messageDiv)) {
            document.body.removeChild(messageDiv);
          }
        }, 300);
      }, 3000);
    },

    setupKeyboardShortcuts: function() {
      document.addEventListener('keydown', (e) => {
        if (!this.currentStudySession) return;

        switch (e.key.toLowerCase()) {
          case ' ':
            e.preventDefault();
            if (document.getElementById('flip-btn').style.display !== 'none') {
              this.flipCard();
            }
            break;
          case '1':
            if (document.getElementById('again-btn').style.display !== 'none') {
              this.markDifficulty('again');
            }
            break;
          case '2':
            if (document.getElementById('hard-btn').style.display !== 'none') {
              this.markDifficulty('hard');
            }
            break;
          case '3':
            if (document.getElementById('good-btn').style.display !== 'none') {
              this.markDifficulty('good');
            }
            break;
          case '4':
            if (document.getElementById('easy-btn').style.display !== 'none') {
              this.markDifficulty('easy');
            }
            break;
          case 'c':
            if (document.getElementById('correct-btn').style.display !== 'none') {
              this.markCorrect();
            }
            break;
          case 'x':
            if (document.getElementById('incorrect-btn').style.display !== 'none') {
              this.markIncorrect();
            }
            break;
        }
      });
    }
  };

  // Initialize the app when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    EnhancedChunkingApp.init();
  });
<<!DOCTYPE html>
  <html lang="en">
          <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
          <title>Chunking Flashcard System</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lobster&display=swap" rel="stylesheet">
    <style>
      * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

      html, body {
      width: 100%;
      max-width: 100%;
      overflow-x: hidden;
    }

      :root {
            --primary-color: #14b8a6;
      --secondary-color: #ec4899;
      --accent-color: #8b5cf6;
      --danger-color: #ef4444;
      --danger-bg: #fef2f2;
      --danger-hover: #fecaca;
      --warning-color: #f59e0b;
      --success-color: var(--primary-color);
      --bg-color: #f8fafc;
      --card-bg: #ffffff;
      --text-color: #1e293b;
      --border-color: #e2e8f0;
      --shadow: 0 4px 20px rgba(20, 184, 166, 0.1);
      --gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    }

      [data-theme="dark"] {
            --bg-color: #0f172a;
      --card-bg: #1e293b;
      --text-color: #ffffff;
      --border-color: #334155;
      --shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      --danger-bg: #2d1b1b;
      --danger-hover: #3d2525;
    }

      [data-theme="purple"] {
            --primary-color: #8b5cf6;
      --secondary-color: #ec4899;
      --accent-color: #14b8a6;
    }

      [data-theme="pink"] {
            --primary-color: #ec4899;
      --secondary-color: #8b5cf6;
      --accent-color: #14b8a6;
    }

      body {
            font-family: var(--card-font, 'Inter', sans-serif);
      background: var(--bg-color);
      color: var(--text-color);
      transition: all 0.3s ease;
      font-size: var(--font-size, 16px);
      line-height: 1.6;
      min-height: 100vh;
    }

      .auth-container {
            max-width: 450px;
      margin: 50px auto;
      padding: 40px;
      background: var(--card-bg);
      border-radius: 20px;
      box-shadow: var(--shadow);
      text-align: center;
    }

      .auth-container h1 {
      background: var(--gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 30px;
      font-size: 2.5em;
      font-weight: bold;
    }

      .auth-tabs {
      display: flex;
      margin-bottom: 25px;
      border-radius: 15px;
      overflow: hidden;
      border: 2px solid var(--border-color);
    }

      .auth-tab {
      flex: 1;
      padding: 15px;
      background: var(--bg-color);
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
      color: var(--text-color);
    }

      .auth-tab.active {
      background: var(--primary-color);
      color: white;
    }

      .auth-form input {
      width: 100%;
      padding: 15px;
      margin-bottom: 15px;
      border: 2px solid var(--border-color);
      border-radius: 15px;
      background: var(--bg-color);
      color: var(--text-color);
      font-size: 1em;
      transition: all 0.3s ease;
    }

      .auth-form input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
    }

      .auth-error {
      background: var(--danger-bg);
      color: var(--danger-color);
      padding: 12px;
      border-radius: 10px;
      margin-bottom: 15px;
      font-size: 0.9em;
      border-left: 4px solid var(--danger-color);
    }

      .container {
            max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

      .header {
            text-align: center;
      margin-bottom: 30px;
      padding: 30px;
      background: var(--card-bg);
      border-radius: 20px;
      box-shadow: var(--shadow);
      position: relative;
      overflow: hidden;
    }

      .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 5px;
      background: var(--primary-color);
    }

      .header h1 {
      background: var(--gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-size: 2.5em;
      margin-bottom: 10px;
    }

      .user-info {
      position: absolute;
      top: 20px;
      right: 20px;
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 0.9em;
    }

      .nav-tabs {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-bottom: 30px;
      flex-wrap: wrap;
    }

      .nav-tab {
      padding: 15px 25px;
      background: var(--primary-color);
      color: white;
      border: none;
      border-radius: 50px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 15px rgba(20, 184, 166, 0.3);
    }

      .nav-tab:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 25px rgba(20, 184, 166, 0.4);
      opacity: 0.9;
    }

      .nav-tab.active {
      background: var(--accent-color);
      box-shadow: 0 6px 25px rgba(139, 92, 246, 0.4);
    }

      .tab-content {
      display: none;
      background: var(--card-bg);
      padding: 30px;
      border-radius: 20px;
      box-shadow: var(--shadow);
      border: 1px solid var(--border-color);
    }

      .tab-content.active {
      display: block;
    }

      .deck-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 25px;
      margin-top: 25px;
    }

      .deck-card {
      background: var(--card-bg);
      border: 2px solid transparent;
      border-radius: 20px;
      padding: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      box-shadow: var(--shadow);
      overflow: hidden;
    }

      .deck-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--primary-color);
    }

      .deck-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(20, 184, 166, 0.2);
      border-color: var(--primary-color);
    }

      .flashcard-container {
            max-width: 700px;
      margin: 0 auto;
      text-align: center;
    }

      .progress-bars {
            margin-bottom: 25px;
      background: var(--bg-color);
      padding: 20px;
      border-radius: 15px;
    }

      .progress-bar {
      background: var(--border-color);
      height: 12px;
      border-radius: 10px;
      margin-bottom: 15px;
      overflow: hidden;
      position: relative;
    }

      .progress-fill {
      height: 100%;
      background: var(--primary-color);
      transition: width 0.5s ease;
      border-radius: 10px;
    }

      .flashcard {
      background: var(--card-background, var(--card-bg));
      border: 3px solid var(--border-color);
      border-radius: 25px;
      padding: 50px;
      margin: 25px 0;
      min-height: var(--card-height, 250px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      font-size: var(--card-font-size, 1.3em);
      line-height: 1.6;
      color: var(--card-text-color, inherit);
      font-family: var(--card-font, inherit);
      box-shadow: var(--shadow);
    }

      .flashcard:hover {
            border-color: var(--primary-color);
      transform: translateY(-3px);
      box-shadow: 0 8px 30px rgba(20, 184, 166, 0.2);
    }

      .flashcard.flipped {
      background: #f0f9ff;
      border-color: var(--secondary-color);
    }

      [data-theme="dark"] .flashcard.flipped {
      background: #1e3a8a;
      border-color: var(--secondary-color);
    }

      .flashcard img, .flashcard video, .flashcard audio {
            max-width: 100%;
      max-height: 200px;
      margin: 10px 0;
      border-radius: 10px;
    }

      .confidence-indicator {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 3px solid #fff;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    }

      .confidence-high { background: var(--success-color); }
      .confidence-medium { background: var(--warning-color); }
      .confidence-low { background: var(--danger-color); }

      .btn {
      padding: 15px 30px;
      border: none;
      border-radius: 50px;
      cursor: pointer;
      font-size: 1.1em;
      transition: all 0.3s ease;
      font-weight: 600;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

      .btn-primary {
      background: var(--primary-color);
      color: white;
    }

      .btn-secondary {
      background: var(--secondary-color);
      color: white;
    }

      .btn-danger {
      background: var(--danger-bg);
      color: var(--danger-color);
      border: 1px solid var(--danger-color);
      font-size: 0.9em;
      padding: 8px 16px;
    }

      .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0,0,0,0.2);
      opacity: 0.9;
    }

      .form-group {
            margin-bottom: 20px;
    }

      .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: var(--primary-color);
    }

      .form-control {
      width: 100%;
      padding: 15px;
      border: 2px solid var(--border-color);
      border-radius: 15px;
      background: var(--card-bg);
      color: var(--text-color);
      font-size: 1em;
      transition: all 0.3s ease;
    }

      .form-control:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
    }

      .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.6);
      z-index: 1000;
      backdrop-filter: blur(5px);
    }

      .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: var(--card-bg);
      padding: 40px;
      border-radius: 25px;
      max-width: 700px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    }

      .close {
      position: absolute;
      top: 15px;
      right: 20px;
      font-size: 28px;
      cursor: pointer;
      color: #666;
      transition: color 0.3s ease;
    }

      .close:hover {
      color: var(--danger-color);
    }

      /* Card Editor Styles */
      #card-editor {
      background: var(--card-background, var(--card-bg));
      color: var(--card-text-color, var(--text-color));
      font-family: var(--card-font, inherit);
      font-size: var(--card-font-size, 1.3em);
      padding: 30px;
      border-radius: 25px;
      border: 3px solid var(--border-color);
      text-align: center;
      margin: 20px 0;
      min-height: var(--card-height, 250px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      transition: all 0.3s ease;
    }

      #card-text-editor {
      width: 90%;
      min-height: 100px;
      outline: none;
      text-align: center;
      border: 2px dashed transparent;
      padding: 10px;
      border-radius: 8px;
      transition: all 0.3s ease;
    }

      #card-text-editor:focus {
            border-color: var(--primary-color);
      background: rgba(20, 184, 166, 0.05);
    }

      #card-text-editor:empty:before {
      content: attr(placeholder);
      color: #999;
      font-style: italic;
    }

      .editing-front #edit-front-btn {
      background: var(--accent-color);
    }

      .editing-back #edit-back-btn {
      background: var(--accent-color);
    }

      .media-preview {
            max-width: 200px;
      max-height: 150px;
      border-radius: 5px;
      margin-top: 10px;
    }

      .color-picker-group {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin-top: 10px;
    }

      .color-picker {
      display: flex;
      align-items: center;
      gap: 10px;
    }

      .color-picker input[type="color"] {
      width: 50px;
      height: 40px;
      border: none;
      border-radius: 10px;
      cursor: pointer;
    }

      .settings-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 25px;
    }

      .settings-card {
      background: var(--bg-color);
      padding: 25px;
      border-radius: 15px;
      border: 1px solid var(--border-color);
    }

      .settings-card h3 {
      color: var(--primary-color);
      margin-bottom: 20px;
      font-size: 1.3em;
    }

      .preview-card {
      background: var(--card-background, var(--card-bg));
      color: var(--card-text-color, var(--text-color));
      font-family: var(--card-font, inherit);
      font-size: var(--card-font-size, 1.3em);
      padding: 30px;
      border-radius: 15px;
      border: 2px solid var(--border-color);
      text-align: center;
      margin-top: 15px;
      min-height: var(--card-height, 150px);
      display: flex;
      align-items: center;
      justify-content: center;
    }

      .delete-btn {
      position: absolute;
      top: 15px;
      right: 15px;
      background: var(--danger-bg);
      border: 1px solid var(--danger-color);
      color: var(--danger-color);
      cursor: pointer;
      font-size: 1.1em;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      z-index: 10;
    }

      .delete-btn:hover {
      background: var(--danger-hover);
      transform: scale(1.1);
    }

      .deck-card-mini {
      background: var(--card-bg);
      border: 2px solid var(--border-color);
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      box-shadow: var(--shadow);
      min-height: 200px;
      display: flex;
      flex-direction: column;
    }

      .deck-card-mini:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 25px rgba(20, 184, 166, 0.2);
      border-color: var(--primary-color);
    }

      .deck-card-mini .confidence-indicator {
      position: absolute;
      top: 15px;
      right: 15px;
      width: 20px;
      height: 20px;
    }

      .deck-card-mini .card-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      font-size: var(--card-font-size, 1.1em);
      font-family: var(--card-font, inherit);
      color: var(--card-text-color, inherit);
      padding: 10px;
    }

      .deck-card-mini img, .deck-card-mini video, .deck-card-mini audio {
            max-width: 100%;
      max-height: 100px;
      margin: 8px 0;
      border-radius: 8px;
    }

      .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin: 10px 0;
    }

      .tag {
      background: var(--primary-color);
      color: white;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.8em;
      font-weight: 500;
    }

      .tag.removable {
      cursor: pointer;
      padding-right: 20px;
      position: relative;
    }

      .tag.removable:after {
      content: '×';
      position: absolute;
      right: 6px;
      top: 50%;
      transform: translateY(-50%);
    }

      .stats-dashboard {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

      .stat-card {
      background: var(--card-bg);
      padding: 20px;
      border-radius: 15px;
      border: 1px solid var(--border-color);
      text-align: center;
      box-shadow: var(--shadow);
    }

      .stat-value {
            font-size: 2em;
      font-weight: bold;
      color: var(--primary-color);
    }

      .stat-label {
            font-size: 0.9em;
      color: var(--text-color);
      opacity: 0.7;
      margin-top: 5px;
    }

      .chart-container {
      background: var(--card-bg);
      padding: 20px;
      border-radius: 15px;
      border: 1px solid var(--border-color);
      margin: 20px 0;
    }

      .spaced-repetition-indicator {
      position: absolute;
      top: 45px;
      right: 20px;
      background: var(--warning-color);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.7em;
      font-weight: bold;
    }

      .spaced-repetition-indicator.due {
      background: var(--danger-color);
    }

      .spaced-repetition-indicator.mastered {
      background: var(--success-color);
    }

      @media (max-width: 768px) {
      .container {
      padding: 15px;
    }

      .flashcard {
      padding: 25px;
      min-height: 200px;
      font-size: 1.1em;
    }

      .header {
      padding: 20px 15px;
    }

      .user-info {
      position: static;
      margin-top: 15px;
      justify-content: center;
    }

      .deck-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }

      .modal-content {
      width: 95%;
      max-width: 95%;
      margin: 20px auto;
      padding: 20px;
    }

      .settings-grid {
      grid-template-columns: 1fr;
    }
    }

      @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }

      @keyframes slideOut {
      from { transform: translateX(0); opacity: 1; }
      to { transform: translateX(100%); opacity: 0; }
    }
    </style>
  </head>
  <body>
  <!-- Authentication Screen -->
  <div id="auth-screen" class="auth-container">
    <h1>🧠 Enhanced Chunking Flashcards</h1>
    <p style="margin-bottom: 30px; color: var(--text-color); opacity: 0.8;">Master your material with advanced spaced repetition</p>

    <div class="auth-tabs">
      <button class="auth-tab active" id="login-tab">Login</button>
      <button class="auth-tab" id="signup-tab">Sign Up</button>
    </div>

    <div id="auth-error" class="auth-error" style="display: none;"></div>

    <form id="login-form" class="auth-form">
      <input type="email" id="login-email" placeholder="Email address" required>
        <input type="password" id="login-password" placeholder="Password" required>
          <button type="submit" class="btn btn-primary" style="width: 100%; margin-bottom: 15px;">
            🔐 Login
          </button>
    </form>

    <form id="signup-form" class="auth-form" style="display: none;">
      <input type="text" id="signup-name" placeholder="Display name" required>
        <input type="email" id="signup-email" placeholder="Email address" required>
          <input type="password" id="signup-password" placeholder="Password (6+ characters)" required>
            <input type="password" id="signup-confirm" placeholder="Confirm password" required>
              <button type="submit" class="btn btn-primary" style="width: 100%; margin-bottom: 15px;">
                ✨ Create Account
              </button>
    </form>

    <div style="text-align: center; margin-top: 20px;">
      <button id="guest-btn" class="btn btn-secondary" style="width: 100%;">
        👤 Continue as Guest
      </button>
    </div>

    <div id="auth-loading" style="display: none;">
      <div class="loading">Processing...</div>
    </div>
  </div>

  <!-- Main Application -->
  <div id="main-app" style="display: none;">
    <div class="container">
      <div class="header">
        <div class="user-info">
          <span id="user-name">Guest User</span>
          <button id="sign-out-btn" class="btn btn-secondary" style="padding: 8px 15px; font-size: 0.9em;">Sign Out</button>
        </div>
        <h1>🧠 Enhanced Chunking Flashcard System</h1>
        <p style="color: var(--text-color); opacity: 0.8;">Master your material with spaced repetition and analytics</p>
      </div>

      <div class="nav-tabs">
        <button class="nav-tab active" data-tab="decks">My Decks</button>
        <button class="nav-tab" data-tab="study">Study</button>
        <button class="nav-tab" data-tab="exams">🎯 Mock Exams</button>
        <button class="nav-tab" data-tab="analytics">Analytics</button>
        <button class="nav-tab" data-tab="import">Import Cards</button>
        <button class="nav-tab" data-tab="manage">Manage Cards</button>
        <button class="nav-tab" data-tab="settings">Settings</button>
      </div>

      <!-- Decks Tab -->
      <div id="decks" class="tab-content active">
        <div id="deck-list-view">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; flex-wrap: wrap; gap: 15px;">
            <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">My Flashcard Decks</h2>
            <button id="create-deck-btn" class="btn btn-primary">✨ Create New Deck</button>
          </div>
          <div id="deck-grid" class="deck-grid">
            <div class="loading">Loading your decks...</div>
          </div>
        </div>

        <div id="deck-detail-view" style="display: none;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; flex-wrap: wrap; gap: 15px;">
            <button id="back-to-decks-btn" class="btn btn-secondary">← Back to Decks</button>
            <h2 id="deck-title" style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; flex: 1; text-align: center;"></h2>
            <button id="add-card-to-deck-btn" class="btn btn-primary">✏️ Add New Card</button>
          </div>

          <div id="deck-cards-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px;">
            <!-- Cards will be rendered here -->
          </div>
        </div>
      </div>

      <!-- Study Tab -->
      <div id="study" class="tab-content">
        <div id="study-setup" style="text-align: center;">
          <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 30px;">Smart Study Session</h2>

          <div class="stats-dashboard">
            <div class="stat-card">
              <div class="stat-value" id="cards-due">0</div>
              <div class="stat-label">Cards Due Today</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="streak-count">0</div>
              <div class="stat-label">Day Streak</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="mastered-cards">0</div>
              <div class="stat-label">Mastered Cards</div>
            </div>
          </div>

          <select id="study-deck-select" class="form-control" style="max-width: 400px; margin: 20px auto;">
            <option value="">Choose a deck...</option>
          </select>

          <div style="margin: 30px 0;">
            <h3 style="color: var(--primary-color); margin-bottom: 20px;">Study Mode</h3>
            <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
              <div class="study-mode-card" id="spaced-mode">
                <div style="font-size: 2em; margin-bottom: 10px;">🎯</div>
                <h4 style="color: var(--primary-color); margin-bottom: 10px;">Spaced Repetition</h4>
                <p style="color: var(--text-color); opacity: 0.8; font-size: 0.9em; margin-bottom: 15px;">Study cards based on optimal spacing intervals for maximum retention.</p>
                <button class="btn btn-primary" id="start-spaced-btn" style="width: 100%;">🚀 Start Smart Study</button>
              </div>

              <div class="study-mode-card" id="chunking-mode">
                <div style="font-size: 2em; margin-bottom: 10px;">🧠</div>
                <h4 style="color: var(--primary-color); margin-bottom: 10px;">Chunking Study</h4>
                <p style="color: var(--text-color); opacity: 0.8; font-size: 0.9em; margin-bottom: 15px;">Learn in small chunks of 3-7 cards. Perfect for new material.</p>
                <div style="margin: 15px 0;">
                  <label style="color: var(--primary-color); font-weight: 600;">Chunk Size:</label>
                  <input type="number" id="chunk-size" class="form-control" value="5" min="1" max="20" style="max-width: 80px; margin: 10px auto;">
                </div>
                <button class="btn btn-primary" id="start-chunking-btn" style="width: 100%;">🧠 Start Chunking</button>
              </div>

              <div class="study-mode-card" id="review-mode">
                <div style="font-size: 2em; margin-bottom: 10px;">📚</div>
                <h4 style="color: var(--secondary-color); margin-bottom: 10px;">Full Review</h4>
                <p style="color: var(--text-color); opacity: 0.8; font-size: 0.9em; margin-bottom: 15px;">Go through all cards continuously. Perfect for final reviews.</p>
                <div style="margin: 15px 0;">
                  <label style="font-size: 0.9em; color: var(--text-color); opacity: 0.8;">
                    <input type="checkbox" id="shuffle-cards" style="margin-right: 8px;" checked>
                      Shuffle card order
                  </label>
                </div>
                <button class="btn btn-secondary" id="start-review-btn" style="width: 100%;">📖 Start Review</button>
              </div>
            </div>
          </div>
        </div>

        <div id="study-session" style="display: none;">
          <div class="flashcard-container">
            <div class="chunk-info">
              <span id="session-info">Smart Study Session</span> |
              <span id="session-progress">Card 1 of 25</span>
            </div>

            <div class="progress-bars">
              <div class="progress-bar">
                <div id="session-progress-bar" class="progress-fill" style="width: 4%;"></div>
              </div>
            </div>

            <div id="flashcard" class="flashcard">
              <div class="confidence-indicator confidence-low"></div>
              <div class="spaced-repetition-indicator">New</div>
              <div id="card-content">Click to start studying! 🎯</div>
            </div>

            <div class="card-controls" style="display: flex; justify-content: center; gap: 20px; margin-top: 25px; flex-wrap: wrap;">
              <button id="again-btn" class="btn btn-danger" style="display: none;">Again</button>
              <button id="hard-btn" class="btn" style="background: #f59e0b; color: white; display: none;">Hard</button>
              <button id="good-btn" class="btn btn-primary" style="display: none;">Good</button>
              <button id="easy-btn" class="btn btn-primary" style="background: #10b981; display: none;">Easy</button>

              <button id="flip-btn" class="btn btn-primary">🔄 Flip Card (Space)</button>
              <button id="incorrect-btn" class="btn btn-danger" style="display: none;">❌ Incorrect (X)</button>
              <button id="correct-btn" class="btn btn-primary" style="display: none;">✅ Correct (C)</button>
            </div>

            <div style="margin-top: 25px; text-align: center;">
              <button id="end-study-btn" class="btn btn-secondary">End Session</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Mock Exams Tab -->
      <div id="exams" class="tab-content">
        <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">🎯 Mock Exams</h2>

        <!-- Exam Setup View -->
        <div id="exam-setup-view">
          <div class="exam-setup-card" style="background: var(--card-bg); border-radius: 20px; padding: 30px; margin-bottom: 25px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
            <h3 style="color: var(--primary-color); margin-bottom: 20px;">📝 Create Mock Exam</h3>

            <div class="form-group" style="margin-bottom: 20px;">
              <label for="exam-name" style="display: block; margin-bottom: 8px; font-weight: 600;">Exam Name:</label>
              <input type="text" id="exam-name" class="form-control" placeholder="e.g., Biology Midterm Practice" style="width: 100%;">
            </div>

            <div class="form-group" style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 8px; font-weight: 600;">Select Decks:</label>
              <div id="exam-deck-selection" style="max-height: 200px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: 10px; padding: 15px;">
                <!-- Deck checkboxes will be populated here -->
              </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
              <div class="form-group">
                <label for="exam-question-count" style="display: block; margin-bottom: 8px; font-weight: 600;">Number of Questions:</label>
                <select id="exam-question-count" class="form-control">
                  <option value="10">10 Questions</option>
                  <option value="20" selected>20 Questions</option>
                  <option value="30">30 Questions</option>
                  <option value="50">50 Questions</option>
                  <option value="all">All Available Cards</option>
                </select>
              </div>

              <div class="form-group">
                <label for="exam-time-limit" style="display: block; margin-bottom: 8px; font-weight: 600;">Time Limit:</label>
                <select id="exam-time-limit" class="form-control">
                  <option value="0">No Time Limit</option>
                  <option value="15">15 Minutes</option>
                  <option value="30" selected>30 Minutes</option>
                  <option value="45">45 Minutes</option>
                  <option value="60">60 Minutes</option>
                  <option value="90">90 Minutes</option>
                </select>
              </div>
            </div>

            <div class="form-group" style="margin-bottom: 25px;">
              <label style="display: block; margin-bottom: 8px; font-weight: 600;">Question Types:</label>
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                <label style="display: flex; align-items: center; gap: 8px;">
                  <input type="checkbox" id="exam-type-flashcard" checked> Flashcard Style (Front → Back)
                </label>
                <label style="display: flex; align-items: center; gap: 8px;">
                  <input type="checkbox" id="exam-type-reverse"> Reverse (Back → Front)
                </label>
              </div>
            </div>

            <button id="start-exam-btn" class="btn btn-primary" style="width: 100%; padding: 15px; font-size: 1.1em;">🚀 Start Mock Exam</button>
          </div>

          <!-- Previous Exam Results -->
          <div class="exam-history-card" style="background: var(--card-bg); border-radius: 20px; padding: 30px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
            <h3 style="color: var(--primary-color); margin-bottom: 20px;">📊 Recent Exam Results</h3>
            <div id="exam-history-list">
              <p style="text-align: center; color: var(--text-color); opacity: 0.7;">No exams taken yet. Create your first mock exam above!</p>
            </div>
          </div>
        </div>

        <!-- Exam Taking View -->
        <div id="exam-taking-view" style="display: none;">
          <div class="exam-header" style="background: var(--card-bg); border-radius: 15px; padding: 20px; margin-bottom: 25px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
            <div>
              <h3 id="current-exam-name" style="color: var(--primary-color); margin: 0;">Mock Exam</h3>
              <p id="exam-progress" style="margin: 5px 0 0 0; color: var(--text-color); opacity: 0.8;">Question 1 of 20</p>
            </div>
            <div style="text-align: right;">
              <div id="exam-timer" style="font-size: 1.5em; font-weight: 600; color: var(--primary-color);">30:00</div>
              <button id="end-exam-btn" class="btn btn-secondary" style="margin-top: 10px;">End Exam</button>
            </div>
          </div>

          <div class="exam-question-card" style="background: var(--card-bg); border-radius: 20px; padding: 40px; text-align: center; box-shadow: 0 8px 32px rgba(0,0,0,0.1); min-height: 300px; display: flex; flex-direction: column; justify-content: center;">
            <div id="exam-question-content">
              <h4 style="color: var(--text-color); margin-bottom: 30px;">Loading question...</h4>
            </div>

            <div id="exam-answer-section" style="margin-top: 30px;">
              <textarea id="exam-answer-input" class="form-control" rows="4" placeholder="Type your answer here..." style="margin-bottom: 20px; font-size: 1.1em;"></textarea>
              <div style="display: flex; gap: 15px; justify-content: center;">
                <button id="exam-show-answer-btn" class="btn btn-secondary">Show Answer</button>
                <button id="exam-next-question-btn" class="btn btn-primary" style="display: none;">Next Question</button>
              </div>
            </div>
          </div>

          <div id="exam-answer-reveal" style="display: none; background: var(--card-bg); border-radius: 15px; padding: 25px; margin-top: 20px; border-left: 4px solid var(--primary-color);">
            <h5 style="color: var(--primary-color); margin-bottom: 15px;">Correct Answer:</h5>
            <div id="exam-correct-answer" style="margin-bottom: 20px; font-size: 1.1em;"></div>
            <div style="display: flex; gap: 15px; justify-content: center;">
              <button id="exam-mark-correct-btn" class="btn btn-primary" style="background: #10b981;">✅ I Got It Right</button>
              <button id="exam-mark-incorrect-btn" class="btn btn-danger">❌ I Got It Wrong</button>
            </div>
          </div>
        </div>

        <!-- Exam Results View -->
        <div id="exam-results-view" style="display: none;">
          <div class="exam-results-header" style="background: var(--card-bg); border-radius: 20px; padding: 30px; margin-bottom: 25px; text-align: center; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
            <h2 id="exam-final-score" style="color: var(--primary-color); margin-bottom: 15px;">85%</h2>
            <p id="exam-grade-text" style="font-size: 1.2em; margin-bottom: 10px;">Great Job!</p>
            <p id="exam-completion-time" style="color: var(--text-color); opacity: 0.8;">Completed in 25 minutes</p>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px; margin-bottom: 25px;">
            <div class="results-card" style="background: var(--card-bg); border-radius: 15px; padding: 25px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
              <h4 style="color: var(--primary-color); margin-bottom: 15px;">📊 Performance Breakdown</h4>
              <div id="exam-performance-stats"></div>
            </div>

            <div class="results-card" style="background: var(--card-bg); border-radius: 15px; padding: 25px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
              <h4 style="color: var(--primary-color); margin-bottom: 15px;">🎯 Areas to Focus</h4>
              <div id="exam-focus-areas"></div>
            </div>
          </div>

          <div class="results-actions" style="background: var(--card-bg); border-radius: 15px; padding: 25px; text-align: center; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
            <h4 style="color: var(--primary-color); margin-bottom: 20px;">📚 Recommended Actions</h4>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
              <button id="create-focus-deck-btn" class="btn btn-primary">📝 Create Focus Deck</button>
              <button id="review-mistakes-btn" class="btn btn-secondary">🔍 Review Mistakes</button>
              <button id="retake-exam-btn" class="btn" style="background: var(--accent-color); color: white;">🔄 Retake Exam</button>
              <button id="new-exam-btn" class="btn btn-secondary">➕ New Exam</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Analytics Tab -->
      <div id="analytics" class="tab-content">
        <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">Study Analytics</h2>

        <div class="stats-dashboard">
          <div class="stat-card">
            <div class="stat-value" id="total-study-time">0h</div>
            <div class="stat-label">Total Study Time</div>
          </div>
          <div class="stat-card">
            <div class="stat-value" id="avg-accuracy">0%</div>
            <div class="stat-label">Average Accuracy</div>
          </div>
          <div class="stat-card">
            <div class="stat-value" id="cards-reviewed">0</div>
            <div class="stat-label">Cards Reviewed</div>
          </div>
          <div class="stat-card">
            <div class="stat-value" id="difficult-cards">0</div>
            <div class="stat-label">Difficult Cards</div>
          </div>
        </div>

        <div class="chart-container">
          <h3 style="color: var(--primary-color); margin-bottom: 15px;">Accuracy Trend (Last 30 Days)</h3>
          <canvas id="accuracy-chart" width="400" height="200"></canvas>
        </div>

        <div class="chart-container">
          <h3 style="color: var(--primary-color); margin-bottom: 15px;">Study Time (Last 7 Days)</h3>
          <canvas id="time-chart" width="400" height="200"></canvas>
        </div>

        <div style="background: var(--card-bg); padding: 20px; border-radius: 15px; border: 1px solid var(--border-color); margin-top: 20px;">
          <h3 style="color: var(--primary-color); margin-bottom: 15px;">Performance Insights</h3>
          <div id="performance-insights">
            <p>📊 Complete more study sessions to see detailed analytics</p>
          </div>
        </div>
      </div>

      <!-- Import Tab -->
      <div id="import" class="tab-content">
        <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">Import Cards from CSV</h2>
        <p style="color: var(--text-color); opacity: 0.8; margin-bottom: 25px;">Upload or paste CSV data to quickly add multiple cards to a deck.</p>

        <div class="form-group">
          <label for="import-deck">Target Deck:</label>
          <select id="import-deck" class="form-control">
            <option value="">Select a deck...</option>
          </select>
        </div>
        <div class="form-group">
          <label for="csv-file">CSV File (Front, Back, Tags format):</label>
          <input type="file" id="csv-file" class="form-control" accept=".csv">
        </div>
        <div class="form-group">
          <label for="csv-text">Or paste CSV text:</label>
          <textarea id="csv-text" class="form-control" rows="10" placeholder="Front,Back,Tags&#10;Question 1,Answer 1,math;algebra&#10;Question 2,Answer 2,science;physics"></textarea>
        </div>
        <button id="import-cards-btn" class="btn btn-primary">📥 Import Cards</button>
      </div>

      <!-- Manage Tab -->
      <div id="manage" class="tab-content">
        <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">Manage Cards</h2>
        <div style="display: flex; gap: 15px; margin-bottom: 25px; flex-wrap: wrap; align-items: center;">
          <select id="manage-deck" class="form-control" style="max-width: 200px;">
            <option value="">All Decks</option>
          </select>
          <input type="text" id="search-cards" class="form-control" placeholder="Search cards..." style="max-width: 250px;">
            <select id="confidence-filter" class="form-control" style="max-width: 180px;">
              <option value="">All Confidence</option>
              <option value="low">Low (0-33%)</option>
              <option value="medium">Medium (34-66%)</option>
              <option value="high">High (67-100%)</option>
            </select>
            <button id="filter-cards-btn" class="btn btn-secondary">🔍 Filter</button>
        </div>
        <div id="card-list" style="max-height: 500px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: 15px; background: var(--bg-color);">
          <div class="loading">Loading cards...</div>
        </div>
      </div>

      <!-- Settings Tab -->
      <div id="settings" class="tab-content">
        <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">Settings</h2>
        <div class="settings-grid">
          <div class="settings-card">
            <h3>🎨 Theme & Colors</h3>
            <div class="form-group">
              <label for="theme-select">Color Theme:</label>
              <select id="theme-select" class="form-control">
                <option value="teal">Teal (Default)</option>
                <option value="purple">Purple Primary</option>
                <option value="pink">Pink Primary</option>
                <option value="dark">Dark Mode</option>
              </select>
            </div>
          </div>

          <div class="settings-card">
            <h3>📝 Text Settings</h3>
            <div class="form-group">
              <label for="font-size">App Font Size:</label>
              <select id="font-size" class="form-control">
                <option value="14px">Small</option>
                <option value="16px" selected>Medium</option>
                <option value="18px">Large</option>
                <option value="20px">Extra Large</option>
              </select>
            </div>
          </div>

          <div class="settings-card">
            <h3>🎴 Flashcard Customization</h3>
            <div class="form-group">
              <label for="card-font">Card Font:</label>
              <select id="card-font" class="form-control">
                <option value="inherit">Default</option>
                <option value="'Avenir', 'Helvetica', sans-serif">Avenir</option>
                <option value="'Lobster', cursive">Lobster</option>
                <option value="'Georgia', serif">Georgia (Serif)</option>
                <option value="'Times New Roman', serif">Times New Roman</option>
                <option value="'Arial', sans-serif">Arial</option>
                <option value="'Comic Sans MS', cursive">Comic Sans</option>
                <option value="'Courier New', monospace">Courier New</option>
              </select>
            </div>

            <div class="form-group">
              <label for="card-font-size">Card Font Size:</label>
              <select id="card-font-size" class="form-control">
                <option value="1em">Small</option>
                <option value="1.3em" selected>Medium</option>
                <option value="1.6em">Large</option>
                <option value="2em">Extra Large</option>
                <option value="2.5em">Huge</option>
              </select>
            </div>

            <div class="form-group">
              <label for="card-height">Card Height:</label>
              <select id="card-height" class="form-control">
                <option value="200px">Short</option>
                <option value="250px" selected>Medium</option>
                <option value="300px">Tall</option>
                <option value="400px">Extra Tall</option>
              </select>
            </div>

            <div class="form-group">
              <label>Card Colors:</label>
              <div class="color-picker-group">
                <div class="color-picker">
                  <label for="card-bg-color">Background:</label>
                  <input type="color" id="card-bg-color" value="#ffffff">
                </div>
                <div class="color-picker">
                  <label for="card-text-color">Text:</label>
                  <input type="color" id="card-text-color" value="#1e293b">
                </div>
              </div>
            </div>

            <div class="preview-card">
              Sample flashcard preview text
            </div>
          </div>

          <div class="settings-card">
            <h3>⚙️ Study Settings</h3>
            <div class="form-group">
              <label for="spaced-repetition">Enable Spaced Repetition:</label>
              <select id="spaced-repetition" class="form-control">
                <option value="true" selected>Enabled</option>
                <option value="false">Disabled</option>
              </select>
            </div>
            <div class="form-group">
              <label for="daily-goal">Daily Study Goal (cards):</label>
              <input type="number" id="daily-goal" class="form-control" value="20" min="1" max="200">
            </div>
          </div>

          <div class="settings-card">
            <h3>💾 Data Management</h3>
            <button id="export-data-btn" class="btn btn-secondary" style="width: 100%; margin-bottom: 15px;">📤 Export All Data</button>
            <button id="clear-data-btn" class="btn btn-danger" style="width: 100%;">🗑️ Clear All Data</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Create Deck Modal -->
  <div id="create-deck-modal" class="modal">
    <div class="modal-content">
      <span class="close" id="close-modal">&times;</span>
      <h3 style="color: var(--primary-color); margin-bottom: 25px;">✨ Create New Deck</h3>
      <div class="form-group">
        <label for="deck-name">Deck Name:</label>
        <input type="text" id="deck-name" class="form-control" placeholder="e.g., Biology Chapter 5">
      </div>
      <div class="form-group">
        <label for="deck-description">Description (optional):</label>
        <textarea id="deck-description" class="form-control" rows="3" placeholder="Brief description of the deck content"></textarea>
      </div>
      <button id="create-deck-confirm-btn" class="btn btn-primary" style="width: 100%;">🚀 Create Deck</button>
    </div>
  </div>

  <!-- Add Card Modal -->
  <div id="add-card-modal" class="modal">
    <div class="modal-content" style="max-width: 700px;">
      <span class="close" id="close-card-modal">&times;</span>
      <h3 style="color: var(--primary-color); margin-bottom: 25px;">✏️ Create New Flashcard</h3>

      <div style="text-align: center; margin-bottom: 20px;">
        <button id="edit-front-btn" class="btn btn-primary" style="margin-right: 10px;">Edit Front</button>
        <button id="edit-back-btn" class="btn btn-secondary">Edit Back</button>
      </div>

      <!-- Visual Card Editor -->
      <div id="card-editor" class="flashcard" style="min-height: 300px; cursor: text; position: relative;">
        <div class="confidence-indicator confidence-low"></div>
        <div id="card-edit-content" style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;">
          <div contenteditable="true" id="card-text-editor" style="width: 90%; min-height: 100px; outline: none; text-align: center; border: 2px dashed transparent; padding: 10px; border-radius: 8px;" placeholder="Click here to add text..."></div>
          <div id="card-media-container" style="margin-top: 15px;">
            <!-- Media will be added here -->
          </div>
          <div style="margin-top: 15px;">
            <input type="file" id="card-media-input" accept="image/*,video/*,audio/*" style="display: none;">
              <button type="button" id="add-media-btn" class="btn btn-secondary" style="font-size: 0.9em; padding: 8px 16px;">📎 Add Media</button>
          </div>
        </div>
      </div>

      <div class="form-group" style="margin-top: 20px;">
        <label for="card-tags">Tags (optional):</label>
        <input type="text" id="card-tags" class="form-control" placeholder="e.g., math, algebra, equations (comma-separated)">
          <small style="color: var(--text-color); opacity: 0.7;">Use tags to organize and filter your cards</small>
      </div>

      <div style="text-align: center; margin-top: 25px;">
        <button id="save-card-btn" class="btn btn-primary" style="margin-right: 10px;">💾 Save Card</button>
        <button id="cancel-card-btn" class="btn btn-secondary">Cancel</button>
      </div>
    </div>
  </div>

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>

<script type="module">
  // Enhanced Chunking Flashcard System with Firebase Integration and Fixed Functionality

  // Firebase Configuration
  const firebaseConfig = {
    apiKey: "AIzaSyByEky8SN50EA5XozKUURxpMAssJVF8dNU",
    authDomain: "football-fa84e.firebaseapp.com",
    projectId: "football-fa84e",
    storageBucket: "football-fa84e.appspot.com",
    messagingSenderId: "585689762025",
    appId: "1:585689762025:web:a4ce206e6b4541d499c878",
    measurementId: "G-JJYT9NZ6R0"
  };

  // Initialize Firebase with error handling for iframe environments
  let auth, db;
  let firebaseAvailable = false;

  try {
    firebase.initializeApp(firebaseConfig);
    auth = firebase.auth();
    db = firebase.firestore();
    firebaseAvailable = true;
    console.log('Firebase initialized successfully');
  } catch (error) {
    console.warn('Firebase initialization failed (likely due to iframe environment):', error);
    console.log('Falling back to localStorage-only mode');
    firebaseAvailable = false;
  }

  // Global App Object
  const EnhancedChunkingApp = {
    currentUser: null,
    allDecks: [],
    allCards: [],
    currentDeck: null,
    currentStudySession: null,
    studyStatistics: {
      totalStudyTime: 0,
      sessionsCompleted: 0,
      accuracyHistory: [],
      dailyStudyTime: {}
    },
    cardEditor: {
      currentSide: 'front',
      frontText: '',
      backText: '',
      frontMedia: null,
      backMedia: null,
      currentCard: null
    },

    init: function() {
      console.log('🧠 Initializing Chunking Flashcards...');
      this.setupEventListeners();
      this.loadSettings();
      this.setupKeyboardShortcuts();
      this.setupFirebaseAuth();
    },

    // Firebase Authentication Setup
    setupFirebaseAuth: function() {
      console.log('🔐 Setting up authentication...');

      // Always check localStorage first for existing session
      const savedUser = localStorage.getItem('chunking_user');
      if (savedUser) {
        try {
          this.currentUser = JSON.parse(savedUser);
          console.log('✅ Restored user session:', this.currentUser.email);
          this.showMainApp();
          return;
        } catch (e) {
          console.warn('❌ Failed to restore user session, clearing invalid data');
          localStorage.removeItem('chunking_user');
        }
      }

      // If no saved session, check Firebase (if available)
      if (firebaseAvailable) {
        auth.onAuthStateChanged((user) => {
          if (user) {
            console.log('User signed in via Firebase:', user.email);
            this.currentUser = user;
            // Save user info to localStorage as backup
            localStorage.setItem('chunking_user', JSON.stringify({
              uid: user.uid,
              email: user.email,
              displayName: user.displayName
            }));
            this.showMainApp();
          } else {
            console.log('No Firebase user signed in');
            // Don't remove localStorage user here - they might be using localStorage auth
            if (!this.currentUser) {
              this.showAuthScreen();
            }
          }
        });
      } else {
        console.log('Firebase not available, showing auth screen');
        this.showAuthScreen();
      }
    },

    // Authentication Methods
    async signInWithEmail(email, password) {
      console.log('🔐 Attempting sign in for:', email);

      // Always try localStorage first in iframe environments
      const storedUsers = JSON.parse(localStorage.getItem('chunking_users') || '{}');
      const userKey = email.toLowerCase();

      console.log('📦 All stored users:', Object.keys(storedUsers));
      console.log('🔍 Looking for user key:', userKey);
      console.log('🔍 User exists:', !!storedUsers[userKey]);

      if (storedUsers[userKey]) {
        console.log('👤 Found user data:', {
          uid: storedUsers[userKey].uid,
          name: storedUsers[userKey].name,
          email: storedUsers[userKey].email,
          passwordMatch: storedUsers[userKey].password === password
        });
      }

      // Check localStorage authentication first
      if (storedUsers[userKey] && storedUsers[userKey].password === password) {
        console.log('✅ LocalStorage authentication successful');
        this.currentUser = {
          uid: storedUsers[userKey].uid,
          email: email,
          displayName: storedUsers[userKey].name
        };

        // Save current session
        localStorage.setItem('chunking_user', JSON.stringify(this.currentUser));
        console.log('💾 Saved user session to localStorage');

        this.showMessage('Successfully signed in! 🎉', 'success');
        this.showMainApp();
        return;
      }

      console.log('❌ LocalStorage authentication failed');

      // Provide helpful error message
      if (storedUsers[userKey]) {
        this.showAuthError('Incorrect password. Please check your password and try again.');
      } else {
        this.showAuthError('Account not found. Please check your email or create a new account.');
      }
    },

    async signUpWithEmail(name, email, password, confirmPassword) {
      console.log('📝 Attempting signup for:', email);

      if (password !== confirmPassword) {
        this.showAuthError('Passwords do not match');
        return;
      }
      if (password.length < 6) {
        this.showAuthError('Password must be at least 6 characters');
        return;
      }

      // Always create localStorage account first in iframe environments
      const storedUsers = JSON.parse(localStorage.getItem('chunking_users') || '{}');
      const userKey = email.toLowerCase();

      console.log('📦 Existing users before signup:', Object.keys(storedUsers));

      if (storedUsers[userKey]) {
        this.showAuthError('An account with this email already exists');
        return;
      }

      const newUser = {
        uid: 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        name: name,
        email: email,
        password: password, // In a real app, this should be hashed
        createdAt: new Date().toISOString()
      };

      storedUsers[userKey] = newUser;
      localStorage.setItem('chunking_users', JSON.stringify(storedUsers));

      console.log('💾 User saved to localStorage:', userKey);
      console.log('👤 New user data:', newUser);
      console.log('📦 All users after signup:', Object.keys(storedUsers));

      this.currentUser = {
        uid: newUser.uid,
        email: email,
        displayName: name
      };

      // Save current session
      localStorage.setItem('chunking_user', JSON.stringify(this.currentUser));
      console.log('💾 Saved user session to localStorage');

      console.log('✅ Account created successfully, showing main app');
      this.showMessage('Account created successfully! 🎉', 'success');
      this.showMainApp();
    },

    showAuthError: function(message) {
      const errorDiv = document.getElementById('auth-error');
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';
      setTimeout(() => {
        errorDiv.style.display = 'none';
      }, 5000);
    },

    // Authentication
    showAuthScreen: function() {
      document.getElementById('auth-screen').style.display = 'block';
      document.getElementById('main-app').style.display = 'none';

      // Remove any existing auth info banners
      const existingAuthInfo = document.querySelector('.auth-info');
      if (existingAuthInfo) {
        existingAuthInfo.remove();
      }

      // Add debug info for troubleshooting
      this.addAuthDebugInfo();
    },

    addAuthDebugInfo: function() {
      const storedUsers = JSON.parse(localStorage.getItem('chunking_users') || '{}');
      const userCount = Object.keys(storedUsers).length;

      if (userCount > 0) {
        const debugInfo = document.createElement('div');
        debugInfo.style.cssText = `
          background: #e0f2fe;
          border: 1px solid #0284c7;
          color: #0c4a6e;
          padding: 10px;
          border-radius: 5px;
          margin-bottom: 15px;
          font-size: 0.9em;
          text-align: center;
        `;
        debugInfo.innerHTML = `
          <strong>📊 Debug Info</strong><br>
          ${userCount} account(s) stored locally<br>
          <button onclick="EnhancedChunkingApp.showStoredUsers()" style="margin-top: 5px; padding: 5px 10px; border: none; background: #0284c7; color: white; border-radius: 3px; cursor: pointer;">
            Show Stored Accounts
          </button>
        `;

        const authContainer = document.querySelector('.auth-container');
        if (authContainer && !authContainer.querySelector('.debug-info')) {
          debugInfo.className = 'debug-info';
          authContainer.insertBefore(debugInfo, authContainer.firstChild);
        }
      }
    },

    showStoredUsers: function() {
      const storedUsers = JSON.parse(localStorage.getItem('chunking_users') || '{}');
      const userList = Object.keys(storedUsers).map(email => {
        const user = storedUsers[email];
        return `Email: ${user.email}\nName: ${user.name}\nUID: ${user.uid}\nCreated: ${user.createdAt}`;
      }).join('\n\n');

      if (userList) {
        alert(`Stored Accounts:\n\n${userList}`);
      } else {
        alert('No accounts stored locally.');
      }
    },

    continueAsGuest: function() {
      console.log('👤 Continuing as guest user');
      this.currentUser = { uid: 'guest', displayName: 'Guest User' };
      this.showMainApp();
    },

    async showMainApp() {
      document.getElementById('auth-screen').style.display = 'none';
      document.getElementById('main-app').style.display = 'block';
      document.getElementById('user-name').textContent =
              this.currentUser.displayName || this.currentUser.email || 'Guest User';

      await this.loadCards();
      await this.loadDecks();
      this.loadStudyStatistics();
      this.updateDashboard();
    },

    setupEventListeners: function() {
      // Auth tabs
      document.getElementById('login-tab').addEventListener('click', () => this.showLoginForm());
      document.getElementById('signup-tab').addEventListener('click', () => this.showSignupForm());

      // Auth forms
      document.getElementById('login-form').addEventListener('submit', (e) => this.handleLogin(e));
      document.getElementById('signup-form').addEventListener('submit', (e) => this.handleSignup(e));
      document.getElementById('guest-btn').addEventListener('click', () => this.continueAsGuest());
      document.getElementById('sign-out-btn').addEventListener('click', () => this.signOut());

      // Navigation
      document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.addEventListener('click', (e) => {
          const tabName = e.target.getAttribute('data-tab');
          this.showTab(tabName);
        });
      });

      // Deck management
      document.getElementById('create-deck-btn').addEventListener('click', () => this.showCreateDeckModal());
      document.getElementById('close-modal').addEventListener('click', () => this.closeModal());
      document.getElementById('create-deck-confirm-btn').addEventListener('click', (e) => {
        console.log('🔘 Create deck button clicked');
        e.preventDefault();
        this.createDeck();
      });

      // Add Enter key support for deck creation
      document.getElementById('deck-name').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          console.log('⌨️ Enter pressed in deck name field');
          this.createDeck();
        }
      });

      document.getElementById('deck-description').addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          console.log('⌨️ Enter pressed in deck description field');
          e.preventDefault();
          this.createDeck();
        }
      });
      document.getElementById('back-to-decks-btn').addEventListener('click', () => this.showDeckList());
      document.getElementById('add-card-to-deck-btn').addEventListener('click', () => this.showCardEditor());

      // Card editor
      document.getElementById('close-card-modal').addEventListener('click', () => this.closeCardEditor());
      document.getElementById('edit-front-btn').addEventListener('click', () => this.switchToFront());
      document.getElementById('edit-back-btn').addEventListener('click', () => this.switchToBack());
      document.getElementById('add-media-btn').addEventListener('click', () => this.triggerMediaUpload());
      document.getElementById('card-media-input').addEventListener('change', (e) => this.handleMediaUpload(e));
      document.getElementById('save-card-btn').addEventListener('click', () => this.saveCard());
      document.getElementById('cancel-card-btn').addEventListener('click', () => this.closeCardEditor());

      // Study modes
      document.getElementById('start-spaced-btn').addEventListener('click', () => this.startSpacedRepetition());
      document.getElementById('start-chunking-btn').addEventListener('click', () => this.startChunkingStudy());
      document.getElementById('start-review-btn').addEventListener('click', () => this.startFullReview());

      // Study controls
      document.getElementById('flip-btn').addEventListener('click', () => this.flipCard());
      document.getElementById('again-btn').addEventListener('click', () => this.markDifficulty('again'));
      document.getElementById('hard-btn').addEventListener('click', () => this.markDifficulty('hard'));
      document.getElementById('good-btn').addEventListener('click', () => this.markDifficulty('good'));
      document.getElementById('easy-btn').addEventListener('click', () => this.markDifficulty('easy'));
      document.getElementById('correct-btn').addEventListener('click', () => this.markCorrect());
      document.getElementById('incorrect-btn').addEventListener('click', () => this.markIncorrect());
      document.getElementById('end-study-btn').addEventListener('click', () => this.endStudySession());
      document.getElementById('flashcard').addEventListener('click', () => this.flipCard());

      // Settings
      document.getElementById('theme-select').addEventListener('change', () => this.changeTheme());
      document.getElementById('font-size').addEventListener('change', () => this.changeFontSize());
      document.getElementById('card-font').addEventListener('change', () => this.updateCardStyle());
      document.getElementById('card-font-size').addEventListener('change', () => this.updateCardStyle());
      document.getElementById('card-height').addEventListener('change', () => this.updateCardStyle());
      document.getElementById('card-bg-color').addEventListener('change', () => this.updateCardStyle());
      document.getElementById('card-text-color').addEventListener('change', () => this.updateCardStyle());

      // Import/Export
      document.getElementById('import-cards-btn').addEventListener('click', () => this.importCards());
      document.getElementById('filter-cards-btn').addEventListener('click', () => this.filterCards());
      document.getElementById('search-cards').addEventListener('input', () => this.filterCards());
      document.getElementById('confidence-filter').addEventListener('change', () => this.filterCards());
      document.getElementById('manage-deck').addEventListener('change', () => this.filterCards());
      document.getElementById('export-data-btn').addEventListener('click', () => this.exportData());
      document.getElementById('clear-data-btn').addEventListener('click', () => this.clearAllData());

      // Mock Exam event listeners
      document.getElementById('start-exam-btn').addEventListener('click', () => this.startMockExam());
      document.getElementById('end-exam-btn').addEventListener('click', () => this.endMockExam());
      document.getElementById('exam-show-answer-btn').addEventListener('click', () => this.showExamAnswer());
      document.getElementById('exam-next-question-btn').addEventListener('click', () => this.nextExamQuestion());
      document.getElementById('exam-mark-correct-btn').addEventListener('click', () => this.markExamAnswer(true));
      document.getElementById('exam-mark-incorrect-btn').addEventListener('click', () => this.markExamAnswer(false));
      document.getElementById('create-focus-deck-btn').addEventListener('click', () => this.createFocusDeck());
      document.getElementById('review-mistakes-btn').addEventListener('click', () => this.reviewMistakes());
      document.getElementById('retake-exam-btn').addEventListener('click', () => this.retakeExam());
      document.getElementById('new-exam-btn').addEventListener('click', () => this.showExamSetup());
    },

    // Authentication Form Handlers
    showLoginForm: function() {
      document.getElementById('login-tab').classList.add('active');
      document.getElementById('signup-tab').classList.remove('active');
      document.getElementById('login-form').style.display = 'block';
      document.getElementById('signup-form').style.display = 'none';
    },

    showSignupForm: function() {
      document.getElementById('signup-tab').classList.add('active');
      document.getElementById('login-tab').classList.remove('active');
      document.getElementById('signup-form').style.display = 'block';
      document.getElementById('login-form').style.display = 'none';
    },

    async handleLogin(e) {
      e.preventDefault();
      const email = document.getElementById('login-email').value.trim();
      const password = document.getElementById('login-password').value;

      console.log('Login attempt:', email);

      if (!email || !password) {
        this.showAuthError('Please enter both email and password');
        return;
      }

      await this.signInWithEmail(email, password);
    },

    async handleSignup(e) {
      e.preventDefault();
      const name = document.getElementById('signup-name').value.trim();
      const email = document.getElementById('signup-email').value.trim();
      const password = document.getElementById('signup-password').value;
      const confirmPassword = document.getElementById('signup-confirm').value;

      console.log('Signup attempt:', email);

      if (!name || !email || !password || !confirmPassword) {
        this.showAuthError('Please fill in all fields');
        return;
      }

      await this.signUpWithEmail(name, email, password, confirmPassword);
    },

    async signOut() {
      try {
        if (firebaseAvailable && auth.currentUser) {
          await auth.signOut();
        }

        // Clear localStorage data
        localStorage.removeItem('chunking_user');

        this.currentUser = null;
        this.currentDeck = null;
        this.allDecks = [];
        this.allCards = [];
        this.showAuthScreen();
        this.showMessage('Signed out successfully', 'success');
      } catch (error) {
        console.error('Sign out error:', error);
        // Even if Firebase sign out fails, clear local data
        localStorage.removeItem('chunking_user');
        this.currentUser = null;
        this.currentDeck = null;
        this.allDecks = [];
        this.allCards = [];
        this.showAuthScreen();
        this.showMessage('Signed out successfully', 'success');
      }
    },

    showTab: function(tabName) {
      document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
      document.querySelectorAll('.nav-tab').forEach(tab => tab.classList.remove('active'));

      document.getElementById(tabName).classList.add('active');
      document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

      if (tabName === 'decks') {
        this.showDeckList();
      } else if (tabName === 'exams') {
        this.showExamSetup();
      } else if (tabName === 'analytics') {
        this.updateAnalytics();
      } else if (tabName === 'manage') {
        this.filterCards();
      }
    },

    // Deck Management with Fixed Card Counting
    showCreateDeckModal: function() {
      document.getElementById('create-deck-modal').style.display = 'block';
    },

    closeModal: function() {
      document.getElementById('create-deck-modal').style.display = 'none';
    },

    async createDeck() {
      console.log('🏗️ Creating deck...');

      const name = document.getElementById('deck-name').value.trim();
      const description = document.getElementById('deck-description').value.trim();

      console.log('📝 Deck name:', name);
      console.log('📝 Deck description:', description);

      if (!name) {
        this.showMessage('Please enter a deck name', 'error');
        return;
      }

      // Check if we should use Firebase or localStorage
      const useFirebase = firebaseAvailable && this.currentUser.uid !== 'guest' && !this.currentUser.uid.startsWith('local_');

      console.log('🔥 Use Firebase:', useFirebase);
      console.log('👤 Current user UID:', this.currentUser.uid);

      const deckData = {
        name: name,
        description: description,
        created: useFirebase ? firebase.firestore.FieldValue.serverTimestamp() : new Date().toISOString(),
        cardCount: 0,
        userId: this.currentUser.uid
      };

      try {
        if (useFirebase) {
          console.log('💾 Saving to Firebase...');
          const docRef = await db.collection('decks').add(deckData);
          deckData.id = docRef.id;
        } else {
          console.log('💾 Saving to localStorage...');
          // Save to localStorage for guest users or local accounts
          deckData.id = 'local_' + Date.now();
          deckData.created = new Date().toISOString(); // Ensure it's a string for localStorage
          const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
          localDecks.push(deckData);
          localStorage.setItem('chunking_decks', JSON.stringify(localDecks));
        }

        console.log('✅ Deck created successfully:', deckData.id);

        this.closeModal();
        document.getElementById('deck-name').value = '';
        document.getElementById('deck-description').value = '';
        await this.loadDecks();
        this.updateDeckSelects();
        this.showMessage('Deck created successfully! 🎉', 'success');
      } catch (error) {
        console.error('❌ Error creating deck:', error);
        this.showMessage('Error creating deck: ' + error.message, 'error');
      }
    },

    async loadDecks() {
      try {
        if (firebaseAvailable && this.currentUser.uid !== 'guest' && !this.currentUser.uid.startsWith('local_')) {
          // Load from Firebase
          const snapshot = await db.collection('decks')
                  .where('userId', '==', this.currentUser.uid)
                  .get();

          this.allDecks = [];
          snapshot.forEach(doc => {
            const deckData = doc.data();
            deckData.id = doc.id;
            this.allDecks.push(deckData);
          });
        } else {
          // Load from localStorage for guest users or when Firebase is unavailable
          const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
          this.allDecks = localDecks.filter(deck => deck.userId === this.currentUser.uid);
        }

        // Update card counts for all decks
        for (const deck of this.allDecks) {
          const deckCards = this.allCards.filter(card => card.deckId === deck.id);
          const newCardCount = deckCards.length;

          // Update card count if it has changed
          if (deck.cardCount !== newCardCount) {
            deck.cardCount = newCardCount;

            // Save updated count to Firebase or localStorage
            if (this.currentUser.uid !== 'guest') {
              await db.collection('decks').doc(deck.id).update({ cardCount: newCardCount });
            } else {
              const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
              const deckIndex = localDecks.findIndex(d => d.id === deck.id);
              if (deckIndex !== -1) {
                localDecks[deckIndex].cardCount = newCardCount;
                localStorage.setItem('chunking_decks', JSON.stringify(localDecks));
              }
            }
          }
        }

        this.renderDecks();
      } catch (error) {
        console.error('Error loading decks:', error);
        this.showMessage('Error loading decks', 'error');
      }
    },

    renderDecks: function() {
      const grid = document.getElementById('deck-grid');
      if (this.allDecks.length === 0) {
        grid.innerHTML = `
                        <div style="grid-column: 1 / -1; text-align: center; padding: 60px 20px;">
                            <div style="font-size: 3em; margin-bottom: 20px;">📚</div>
                            <h3 style="color: var(--text-color); margin-bottom: 10px;">No decks yet!</h3>
                            <p style="color: var(--text-color); opacity: 0.7;">Create your first deck to get started.</p>
                        </div>
                    `;
        return;
      }

      grid.innerHTML = this.allDecks.map(deck => {
        const deckCards = this.allCards.filter(card => card.deckId === deck.id);
        const cardCount = deckCards.length;
        const avgConfidence = cardCount > 0 ?
                Math.round(deckCards.reduce((sum, card) => sum + (card.confidence || 0), 0) / cardCount) : 0;

        const dueCards = this.getDueCards(deck.id).length;

        return `
                        <div class="deck-card" data-deck-id="${deck.id}">
                            <button class="delete-btn deck-delete-btn" data-deck-id="${deck.id}" title="Delete deck">✕</button>
                            <h3 style="color: var(--primary-color); margin-bottom: 10px; padding-right: 40px;">${deck.name}</h3>
                            <p style="color: var(--text-color); margin-bottom: 20px; opacity: 0.8; min-height: 40px;">${deck.description || 'No description'}</p>
                            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.9em; color: var(--text-color); opacity: 0.7;">
                                <span>📚 ${cardCount} cards</span>
                                <span>🎯 ${avgConfidence}% avg</span>
                            </div>
                            ${dueCards > 0 ? `<div style="margin-top: 10px; color: var(--danger-color); font-size: 0.9em; font-weight: 600;">📅 ${dueCards} due today</div>` : ''}
                        </div>
                    `;
      }).join('');

      // Add event listeners for deck cards and delete buttons
      this.setupDeckEventListeners();
    },

    setupDeckEventListeners: function() {
      // Deck card click handlers (for opening deck)
      document.querySelectorAll('.deck-card').forEach(card => {
        card.addEventListener('click', (e) => {
          // Don't trigger if clicking delete button
          if (e.target.classList.contains('delete-btn')) return;

          const deckId = card.getAttribute('data-deck-id');
          this.showDeckDetail(deckId);
        });
      });

      // Delete button handlers with confirmation
      document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.stopPropagation(); // Prevent deck opening
          const deckId = btn.getAttribute('data-deck-id');
          this.confirmDeleteDeck(deckId);
        });
      });
    },

    confirmDeleteDeck: function(deckId) {
      const deck = this.allDecks.find(d => d.id === deckId);
      if (!deck) return;

      const cardCount = this.allCards.filter(card => card.deckId === deckId).length;
      const message = cardCount > 0
              ? `Are you sure you want to delete "${deck.name}"?\n\nThis will permanently delete the deck and all ${cardCount} cards in it.\n\nThis action cannot be undone.`
              : `Are you sure you want to delete "${deck.name}"?\n\nThis action cannot be undone.`;

      if (confirm(message)) {
        this.deleteDeck(deckId);
      }
    },

    async deleteDeck(deckId) {
      try {
        const deck = this.allDecks.find(d => d.id === deckId);
        if (!deck) return;

        // Delete all cards in the deck first
        const cardsToDelete = this.allCards.filter(card => card.deckId === deckId);
        for (const card of cardsToDelete) {
          await this.deleteCardById(card.id, false); // false = don't show individual confirmations
        }

        // Delete the deck
        if (this.currentUser.uid !== 'guest') {
          await db.collection('decks').doc(deckId).delete();
        } else {
          const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
          const filteredDecks = localDecks.filter(deck => deck.id !== deckId);
          localStorage.setItem('chunking_decks', JSON.stringify(filteredDecks));
        }

        // Update local arrays
        this.allDecks = this.allDecks.filter(deck => deck.id !== deckId);

        // If we're currently viewing this deck, go back to deck list
        if (this.currentDeck && this.currentDeck.id === deckId) {
          this.showDeckList();
        }

        // Refresh UI
        await this.loadDecks();
        await this.loadCards();
        this.updateDeckSelects();
        this.showMessage(`✅ Deck "${deck.name}" deleted successfully!`, 'success');
      } catch (error) {
        console.error('Error deleting deck:', error);
        this.showMessage('Error deleting deck', 'error');
      }
    },

    showDeckDetail: function(deckId) {
      this.currentDeck = this.allDecks.find(d => d.id === deckId);
      if (!this.currentDeck) return;

      document.getElementById('deck-list-view').style.display = 'none';
      document.getElementById('deck-detail-view').style.display = 'block';
      document.getElementById('deck-title').textContent = this.currentDeck.name;

      this.renderDeckCards();
    },

    showDeckList: function() {
      document.getElementById('deck-detail-view').style.display = 'none';
      document.getElementById('deck-list-view').style.display = 'block';
      this.currentDeck = null;
    },

    renderDeckCards: function() {
      const container = document.getElementById('deck-cards-grid');
      const deckCards = this.allCards.filter(card => card.deckId === this.currentDeck.id);

      if (deckCards.length === 0) {
        container.innerHTML = `
                        <div style="grid-column: 1 / -1; text-align: center; padding: 60px 20px;">
                            <div style="font-size: 3em; margin-bottom: 20px;">📝</div>
                            <h3 style="color: var(--text-color); margin-bottom: 10px;">No cards in this deck yet!</h3>
                            <p style="color: var(--text-color); opacity: 0.7;">Click "Add New Card" to create your first flashcard.</p>
                        </div>
                    `;
        return;
      }

      container.innerHTML = deckCards.map(card => {
        const confidence = card.confidence || 0;
        const confidenceClass = confidence >= 67 ? 'confidence-high' :
                confidence >= 34 ? 'confidence-medium' : 'confidence-low';

        const hasMedia = card.frontMedia || card.backMedia;
        const mediaIndicator = hasMedia ? ' 📎' : '';

        const tags = card.tags && card.tags.length > 0 ?
                `<div class="tags-container">${card.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}</div>` : '';

        const spacedRepetition = this.calculateSpacedRepetition(card);
        const srClass = spacedRepetition.status === 'due' ? 'due' :
                spacedRepetition.status === 'mastered' ? 'mastered' : '';

        return `
                        <div class="deck-card-mini" data-card-id="${card.id}">
                            <div class="confidence-indicator ${confidenceClass}"></div>
                            <div class="spaced-repetition-indicator ${srClass}">${spacedRepetition.label}</div>
                            <div class="card-content">
                                <div style="font-weight: 600; margin-bottom: 10px; color: var(--text-color);">${card.front}${mediaIndicator}</div>
                                ${card.frontMedia ? this.renderMediaPreview(card.frontMedia) : ''}
                                ${tags}
                            </div>
                            <div class="card-actions" style="margin-top: 15px; display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-size: 0.9em; color: var(--text-color);">🎯 ${confidence}%</span>
                                <button class="btn btn-danger delete-card-btn" data-card-id="${card.id}" style="padding: 6px 12px; font-size: 0.8em;">🗑️</button>
                            </div>
                        </div>
                    `;
      }).join('');

      // Add event listeners
      container.querySelectorAll('.delete-card-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.stopPropagation();
          const cardId = btn.getAttribute('data-card-id');
          this.deleteCard(cardId);
        });
      });

      container.querySelectorAll('.deck-card-mini').forEach(card => {
        card.addEventListener('click', (e) => {
          if (!e.target.classList.contains('delete-card-btn')) {
            const cardId = card.getAttribute('data-card-id');
            this.editCard(cardId);
          }
        });
      });
    },

    // Enhanced Card Editor with Visual Feedback
    showCardEditor: function(cardId = null) {
      this.resetCardEditor();

      if (cardId) {
        const card = this.allCards.find(c => c.id === cardId);
        if (card) {
          this.cardEditor.currentCard = card;
          this.cardEditor.frontText = card.front;
          this.cardEditor.backText = card.back;
          this.cardEditor.frontMedia = card.frontMedia || null;
          this.cardEditor.backMedia = card.backMedia || null;
          document.getElementById('card-tags').value = card.tags ? card.tags.join(', ') : '';
        }
      } else {
        this.cardEditor.currentCard = null;
        document.getElementById('card-tags').value = '';
      }

      this.switchToFront();
      document.getElementById('add-card-modal').style.display = 'block';
    },

    editCard: function(cardId) {
      this.showCardEditor(cardId);
    },

    closeCardEditor: function() {
      document.getElementById('add-card-modal').style.display = 'none';
      this.resetCardEditor();
    },

    resetCardEditor: function() {
      this.cardEditor = {
        currentSide: 'front',
        frontText: '',
        backText: '',
        frontMedia: null,
        backMedia: null,
        currentCard: null
      };
      document.getElementById('card-text-editor').textContent = '';
      document.getElementById('card-media-container').innerHTML = '';
      document.getElementById('add-card-modal').className = 'modal';
    },

    switchToFront: function() {
      this.saveCurrentSideContent();
      this.cardEditor.currentSide = 'front';
      this.updateCardEditor();
      document.getElementById('add-card-modal').className = 'modal editing-front';
    },

    switchToBack: function() {
      this.saveCurrentSideContent();
      this.cardEditor.currentSide = 'back';
      this.updateCardEditor();
      document.getElementById('add-card-modal').className = 'modal editing-back';
    },

    saveCurrentSideContent: function() {
      const text = document.getElementById('card-text-editor').textContent.trim();
      if (this.cardEditor.currentSide === 'front') {
        this.cardEditor.frontText = text;
      } else {
        this.cardEditor.backText = text;
      }
    },

    updateCardEditor: function() {
      const textEditor = document.getElementById('card-text-editor');
      const mediaContainer = document.getElementById('card-media-container');

      if (this.cardEditor.currentSide === 'front') {
        textEditor.textContent = this.cardEditor.frontText;
        this.displayMedia(mediaContainer, this.cardEditor.frontMedia);
      } else {
        textEditor.textContent = this.cardEditor.backText;
        this.displayMedia(mediaContainer, this.cardEditor.backMedia);
      }

      textEditor.focus();
    },

    displayMedia: function(container, media) {
      container.innerHTML = '';
      if (media && media.data) {
        const mediaElement = this.createMediaElement(media);
        if (mediaElement) {
          const wrapper = document.createElement('div');
          wrapper.style.position = 'relative';
          wrapper.style.display = 'inline-block';

          const deleteBtn = document.createElement('button');
          deleteBtn.innerHTML = '❌';
          deleteBtn.style.position = 'absolute';
          deleteBtn.style.top = '5px';
          deleteBtn.style.right = '5px';
          deleteBtn.style.background = 'rgba(255,255,255,0.8)';
          deleteBtn.style.border = 'none';
          deleteBtn.style.borderRadius = '50%';
          deleteBtn.style.width = '25px';
          deleteBtn.style.height = '25px';
          deleteBtn.style.cursor = 'pointer';
          deleteBtn.style.fontSize = '12px';

          deleteBtn.addEventListener('click', () => this.removeMedia());

          wrapper.appendChild(mediaElement);
          wrapper.appendChild(deleteBtn);
          container.appendChild(wrapper);
        }
      }
    },

    triggerMediaUpload: function() {
      document.getElementById('card-media-input').click();
    },

    handleMediaUpload: function(event) {
      const file = event.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const media = {
            type: file.type,
            data: e.target.result,
            name: file.name
          };

          if (this.cardEditor.currentSide === 'front') {
            this.cardEditor.frontMedia = media;
          } else {
            this.cardEditor.backMedia = media;
          }

          this.updateCardEditor();
        };
        reader.readAsDataURL(file);
      }
      event.target.value = '';
    },

    removeMedia: function() {
      if (this.cardEditor.currentSide === 'front') {
        this.cardEditor.frontMedia = null;
      } else {
        this.cardEditor.backMedia = null;
      }
      this.updateCardEditor();
    },

    createMediaElement: function(media) {
      if (!media || !media.data) return null;

      const fileType = media.type.split('/')[0];
      let element;

      if (fileType === 'image') {
        element = document.createElement('img');
        element.src = media.data;
        element.alt = media.name || 'Card image';
        element.className = 'media-preview';
      } else if (fileType === 'video') {
        element = document.createElement('video');
        element.src = media.data;
        element.controls = true;
        element.className = 'media-preview';
      } else if (fileType === 'audio') {
        element = document.createElement('audio');
        element.src = media.data;
        element.controls = true;
        element.style.width = '200px';
      }

      return element;
    },

    renderMediaPreview: function(media) {
      if (!media || !media.data) return '';

      const fileType = media.type.split('/')[0];
      if (fileType === 'image') {
        return `<img src="${media.data}" alt="${media.name || 'Card image'}" style="max-width: 100%; max-height: 80px; margin: 5px 0; border-radius: 5px;">`;
      } else if (fileType === 'video') {
        return `<video src="${media.data}" style="max-width: 100%; max-height: 80px; margin: 5px 0; border-radius: 5px;"></video>`;
      } else if (fileType === 'audio') {
        return `<div style="margin: 5px 0; font-size: 0.8em; color: #666;">🔊 Audio</div>`;
      }
      return '';
    },

    async saveCard() {
      try {
        this.saveCurrentSideContent();

        if (!this.currentDeck) {
          this.showMessage('No deck selected', 'error');
          return;
        }

        if (!this.cardEditor.frontText || !this.cardEditor.backText) {
          this.showMessage('Please add text to both front and back of the card', 'error');
          return;
        }

        const tagsText = document.getElementById('card-tags').value.trim();
        const tags = tagsText ? tagsText.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

        const cardData = {
          deckId: this.currentDeck.id,
          front: this.cardEditor.frontText,
          back: this.cardEditor.backText,
          frontMedia: this.cardEditor.frontMedia,
          backMedia: this.cardEditor.backMedia,
          tags: tags,
          userId: this.currentUser.uid,
          spacedRepetition: {
            interval: 1,
            easeFactor: 2.5,
            dueDate: firebase.firestore.Timestamp.now(),
            reviewCount: 0
          }
        };

        if (this.cardEditor.currentCard) {
          // Update existing card
          cardData.confidence = this.cardEditor.currentCard.confidence;
          cardData.correct = this.cardEditor.currentCard.correct;
          cardData.incorrect = this.cardEditor.currentCard.incorrect;
          cardData.created = this.cardEditor.currentCard.created;
          cardData.spacedRepetition = this.cardEditor.currentCard.spacedRepetition || cardData.spacedRepetition;

          if (this.currentUser.uid !== 'guest') {
            await db.collection('cards').doc(this.cardEditor.currentCard.id).update(cardData);
          } else {
            const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
            const cardIndex = localCards.findIndex(c => c.id === this.cardEditor.currentCard.id);
            if (cardIndex >= 0) {
              localCards[cardIndex] = { ...cardData, id: this.cardEditor.currentCard.id };
              localStorage.setItem('chunking_cards', JSON.stringify(localCards));
            }
          }

          this.showMessage('Card updated successfully! ✨', 'success');
        } else {
          // Create new card
          cardData.confidence = 0;
          cardData.correct = 0;
          cardData.incorrect = 0;
          cardData.created = firebase.firestore.FieldValue.serverTimestamp();

          if (this.currentUser.uid !== 'guest') {
            const docRef = await db.collection('cards').add(cardData);
            cardData.id = docRef.id;
          } else {
            cardData.id = 'local_' + Date.now() + Math.random();
            cardData.created = new Date();
            const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
            localCards.push(cardData);
            localStorage.setItem('chunking_cards', JSON.stringify(localCards));
          }

          // Update deck card count
          await this.updateDeckCardCount(this.currentDeck.id, 1);
          this.showMessage('Card created successfully! ✨', 'success');
        }

        await this.loadCards();
        await this.loadDecks();
        this.renderDeckCards();
        this.closeCardEditor();
      } catch (error) {
        console.error('Error saving card:', error);
        this.showMessage('Error saving card', 'error');
      }
      this.closeCardEditor();
    },

    async updateDeckCardCount(deckId, change) {
      const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
      const deckIndex = localDecks.findIndex(d => d.id === deckId);
      if (deckIndex >= 0) {
        localDecks[deckIndex].cardCount = Math.max(0, (localDecks[deckIndex].cardCount || 0) + change);
        localStorage.setItem('chunking_decks', JSON.stringify(localDecks));
      }
    },

    async loadCards() {
      try {
        if (firebaseAvailable && this.currentUser.uid !== 'guest' && !this.currentUser.uid.startsWith('local_')) {
          // Load from Firebase
          const snapshot = await db.collection('cards')
                  .where('userId', '==', this.currentUser.uid)
                  .get();

          this.allCards = [];
          snapshot.forEach(doc => {
            const cardData = doc.data();
            cardData.id = doc.id;
            this.allCards.push(cardData);
          });
        } else {
          // Load from localStorage for guest users or when Firebase is unavailable
          const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
          this.allCards = localCards.filter(card => card.userId === this.currentUser.uid);
        }

        this.updateDeckSelects();
      } catch (error) {
        console.error('Error loading cards:', error);
        this.showMessage('Error loading cards', 'error');
      }
    },

    async deleteCardById(cardId, showConfirmation = true) {
      try {
        const card = this.allCards.find(c => c.id === cardId);
        if (!card) return;

        if (showConfirmation) {
          const message = `Are you sure you want to delete this card?\n\nFront: ${card.front?.substring(0, 50)}${card.front?.length > 50 ? '...' : ''}\n\nThis action cannot be undone.`;
          if (!confirm(message)) return;
        }

        // Delete from Firebase or localStorage
        if (this.currentUser.uid !== 'guest') {
          await db.collection('cards').doc(cardId).delete();
        } else {
          const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
          const filteredCards = localCards.filter(card => card.id !== cardId);
          localStorage.setItem('chunking_cards', JSON.stringify(filteredCards));
        }

        // Update local array
        this.allCards = this.allCards.filter(card => card.id !== cardId);

        // Update deck card count
        if (card.deckId) {
          await this.updateDeckCardCount(card.deckId, -1);
        }

        if (showConfirmation) {
          this.showMessage('Card deleted successfully', 'success');
        }
      } catch (error) {
        console.error('Error deleting card:', error);
        this.showMessage('Error deleting card', 'error');
      }
    },

    async deleteCard(cardId) {
      const card = this.allCards.find(c => c.id === cardId);
      if (!card) return;

      const message = `Are you sure you want to delete this card?\n\nFront: ${card.front?.substring(0, 50)}${card.front?.length > 50 ? '...' : ''}\n\nThis action cannot be undone.`;
      if (!confirm(message)) return;

      try {
        // Delete from Firebase or localStorage
        if (this.currentUser.uid !== 'guest') {
          await db.collection('cards').doc(cardId).delete();
        } else {
          const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
          const filteredCards = localCards.filter(card => card.id !== cardId);
          localStorage.setItem('chunking_cards', JSON.stringify(filteredCards));
        }

        // Update deck card count
        if (card.deckId) {
          await this.updateDeckCardCount(card.deckId, -1);
        }

        // Update local array
        this.allCards = this.allCards.filter(c => c.id !== cardId);

        // Refresh UI
        await this.loadCards();
        await this.loadDecks();

        if (this.currentDeck) {
          this.renderDeckCards();
        } else {
          this.filterCards();
        }

        this.showMessage('Card deleted successfully! 🗑️', 'success');
      } catch (error) {
        console.error('Error deleting card:', error);
        this.showMessage('Error deleting card', 'error');
      }
    },

    // Spaced Repetition Algorithm
    calculateSpacedRepetition: function(card) {
      if (!card.spacedRepetition) {
        return { status: 'new', label: 'New' };
      }

      const now = new Date();
      const dueDate = new Date(card.spacedRepetition.dueDate);

      if (card.spacedRepetition.reviewCount >= 5 && card.spacedRepetition.easeFactor >= 2.5) {
        return { status: 'mastered', label: 'Mastered' };
      }

      if (dueDate <= now) {
        return { status: 'due', label: 'Due' };
      }

      const daysUntilDue = Math.ceil((dueDate - now) / (1000 * 60 * 60 * 24));
      if (daysUntilDue <= 1) {
        return { status: 'soon', label: 'Soon' };
      }

      return { status: 'learning', label: `${daysUntilDue}d` };
    },

    updateSpacedRepetition: function(card, difficulty) {
      if (!card.spacedRepetition) {
        card.spacedRepetition = {
          interval: 1,
          easeFactor: 2.5,
          dueDate: new Date(),
          reviewCount: 0
        };
      }

      const sr = card.spacedRepetition;
      sr.reviewCount++;

      let newInterval = sr.interval;
      let newEaseFactor = sr.easeFactor;

      switch (difficulty) {
        case 'again':
          newInterval = 1;
          newEaseFactor = Math.max(1.3, sr.easeFactor - 0.2);
          break;
        case 'hard':
          newInterval = Math.max(1, Math.floor(sr.interval * 1.2));
          newEaseFactor = Math.max(1.3, sr.easeFactor - 0.15);
          break;
        case 'good':
          if (sr.reviewCount === 1) {
            newInterval = 6;
          } else {
            newInterval = Math.floor(sr.interval * sr.easeFactor);
          }
          break;
        case 'easy':
          newInterval = Math.floor(sr.interval * sr.easeFactor * 1.3);
          newEaseFactor = sr.easeFactor + 0.15;
          break;
      }

      sr.interval = newInterval;
      sr.easeFactor = newEaseFactor;

      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + newInterval);
      sr.dueDate = dueDate;

      return sr;
    },

    getDueCards: function(deckId = null) {
      const now = new Date();
      let cards = this.allCards;

      if (deckId) {
        cards = cards.filter(card => card.deckId === deckId);
      }

      return cards.filter(card => {
        if (!card.spacedRepetition) return true; // New cards are due
        const dueDate = new Date(card.spacedRepetition.dueDate);
        return dueDate <= now;
      });
    },

    // Study Sessions with Spaced Repetition
    startSpacedRepetition: function() {
      const deckId = document.getElementById('study-deck-select').value;

      if (!deckId) {
        this.showMessage('Please select a deck', 'error');
        return;
      }

      const dueCards = this.getDueCards(deckId);
      if (dueCards.length === 0) {
        this.showMessage('No cards are due for review in this deck! 🎉', 'success');
        return;
      }

      this.currentStudySession = {
        deckId: deckId,
        cards: [...dueCards],
        currentCardIndex: 0,
        isFlipped: false,
        mode: 'spaced',
        startTime: Date.now(),
        correctAnswers: 0
      };

      this.shuffleArray(this.currentStudySession.cards);
      this.startStudyInterface();
    },

    startChunkingStudy: function() {
      const deckId = document.getElementById('study-deck-select').value;
      const chunkSize = parseInt(document.getElementById('chunk-size').value);

      if (!deckId) {
        this.showMessage('Please select a deck', 'error');
        return;
      }

      const deckCards = this.allCards.filter(card => card.deckId === deckId);
      if (deckCards.length === 0) {
        this.showMessage('This deck has no cards', 'error');
        return;
      }

      this.currentStudySession = {
        deckId: deckId,
        cards: [...deckCards],
        chunkSize: chunkSize,
        currentChunk: 0,
        currentCardIndex: 0,
        isFlipped: false,
        mode: 'chunking',
        startTime: Date.now(),
        correctAnswers: 0
      };

      this.shuffleArray(this.currentStudySession.cards);
      this.startStudyInterface();
    },

    startFullReview: function() {
      const deckId = document.getElementById('study-deck-select').value;
      const shouldShuffle = document.getElementById('shuffle-cards').checked;

      if (!deckId) {
        this.showMessage('Please select a deck', 'error');
        return;
      }

      const deckCards = this.allCards.filter(card => card.deckId === deckId);
      if (deckCards.length === 0) {
        this.showMessage('This deck has no cards', 'error');
        return;
      }

      this.currentStudySession = {
        deckId: deckId,
        cards: [...deckCards],
        currentCardIndex: 0,
        isFlipped: false,
        mode: 'review',
        startTime: Date.now(),
        correctAnswers: 0
      };

      if (shouldShuffle) {
        this.shuffleArray(this.currentStudySession.cards);
      }

      this.startStudyInterface();
    },

    startStudyInterface: function() {
      document.getElementById('study-setup').style.display = 'none';
      document.getElementById('study-session').style.display = 'block';
      this.loadNextCard();
    },

    loadNextCard: function() {
      const session = this.currentStudySession;
      if (!session) return;

      if (session.currentCardIndex >= session.cards.length) {
        this.completeStudySession();
        return;
      }

      const card = session.cards[session.currentCardIndex];
      session.isFlipped = false;

      this.updateProgressBars();
      this.displayStudyCard(card);
    },

    displayStudyCard: function(card) {
      const cardElement = document.getElementById('card-content');
      const confidenceIndicator = document.querySelector('.confidence-indicator');
      const spacedIndicator = document.querySelector('.spaced-repetition-indicator');

      // Clear previous content
      cardElement.innerHTML = '';

      // Add text content
      const textElement = document.createElement('div');
      textElement.textContent = card.front;
      textElement.style.marginBottom = '10px';
      cardElement.appendChild(textElement);

      // Add front media if exists
      if (card.frontMedia) {
        const mediaElement = this.createMediaElement(card.frontMedia);
        if (mediaElement) {
          cardElement.appendChild(mediaElement);
        }
      }

      // Update indicators
      const confidence = card.confidence || 0;
      confidenceIndicator.className = 'confidence-indicator ' +
              (confidence >= 67 ? 'confidence-high' :
                      confidence >= 34 ? 'confidence-medium' : 'confidence-low');

      const spacedRepetition = this.calculateSpacedRepetition(card);
      spacedIndicator.className = `spaced-repetition-indicator ${spacedRepetition.status === 'due' ? 'due' : spacedRepetition.status === 'mastered' ? 'mastered' : ''}`;
      spacedIndicator.textContent = spacedRepetition.label;

      // Show appropriate controls
      if (this.currentStudySession.mode === 'spaced') {
        document.getElementById('flip-btn').style.display = 'block';
        document.getElementById('again-btn').style.display = 'none';
        document.getElementById('hard-btn').style.display = 'none';
        document.getElementById('good-btn').style.display = 'none';
        document.getElementById('easy-btn').style.display = 'none';
        document.getElementById('correct-btn').style.display = 'none';
        document.getElementById('incorrect-btn').style.display = 'none';
      } else {
        document.getElementById('flip-btn').style.display = 'block';
        document.getElementById('correct-btn').style.display = 'none';
        document.getElementById('incorrect-btn').style.display = 'none';
        document.getElementById('again-btn').style.display = 'none';
        document.getElementById('hard-btn').style.display = 'none';
        document.getElementById('good-btn').style.display = 'none';
        document.getElementById('easy-btn').style.display = 'none';
      }

      document.getElementById('flashcard').classList.remove('flipped');
    },

    flipCard: function() {
      const session = this.currentStudySession;
      if (!session || session.isFlipped) return;

      let card = session.cards[session.currentCardIndex];
      const cardElement = document.getElementById('card-content');

      // Clear previous content
      cardElement.innerHTML = '';

      // Add back text content
      const textElement = document.createElement('div');
      textElement.textContent = card.back;
      textElement.style.marginBottom = '10px';
      cardElement.appendChild(textElement);

      // Add back media if exists
      if (card.backMedia) {
        const mediaElement = this.createMediaElement(card.backMedia);
        if (mediaElement) {
          cardElement.appendChild(mediaElement);
        }
      }

      document.getElementById('flashcard').classList.add('flipped');
      document.getElementById('flip-btn').style.display = 'none';

      if (session.mode === 'spaced') {
        document.getElementById('again-btn').style.display = 'block';
        document.getElementById('hard-btn').style.display = 'block';
        document.getElementById('good-btn').style.display = 'block';
        document.getElementById('easy-btn').style.display = 'block';
      } else {
        document.getElementById('correct-btn').style.display = 'block';
        document.getElementById('incorrect-btn').style.display = 'block';
      }

      session.isFlipped = true;
    },

    markDifficulty: function(difficulty) {
      const session = this.currentStudySession;
      if (!session) return;

      const card = session.cards[session.currentCardIndex];

      // Update spaced repetition
      this.updateSpacedRepetition(card, difficulty);

      // Update confidence based on difficulty
      if (difficulty === 'again') {
        card.incorrect = (card.incorrect || 0) + 1;
        card.confidence = card.correct ? Math.round((card.correct / (card.correct + card.incorrect)) * 100) : 0;
      } else {
        card.correct = (card.correct || 0) + 1;
        card.confidence = Math.round((card.correct / (card.correct + (card.incorrect || 0))) * 100);
        session.correctAnswers++;
      }

      this.updateCardInStorage(card);
      session.currentCardIndex++;
      this.loadNextCard();
    },

    markCorrect: function() {
      const session = this.currentStudySession;
      if (!session) return;

      const card = session.cards[session.currentCardIndex];
      card.correct = (card.correct || 0) + 1;
      card.confidence = Math.round((card.correct / (card.correct + (card.incorrect || 0))) * 100);
      session.correctAnswers++;

      this.updateCardInStorage(card);
      session.currentCardIndex++;
      this.loadNextCard();
    },

    markIncorrect: function() {
      const session = this.currentStudySession;
      if (!session) return;

      const card = session.cards[session.currentCardIndex];
      card.incorrect = (card.incorrect || 0) + 1;
      card.confidence = card.correct ? Math.round((card.correct / (card.correct + card.incorrect)) * 100) : 0;

      this.updateCardInStorage(card);
      session.currentCardIndex++;
      this.loadNextCard();
    },

    async updateCardInStorage(card) {
      const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
      const cardIndex = localCards.findIndex(c => c.id === card.id);
      if (cardIndex >= 0) {
        localCards[cardIndex] = { ...localCards[cardIndex], ...card };
        localStorage.setItem('chunking_cards', JSON.stringify(localCards));
      }
    },

    updateProgressBars: function() {
      const session = this.currentStudySession;
      if (!session) return;

      const progress = ((session.currentCardIndex + 1) / session.cards.length) * 100;
      document.getElementById('session-progress-bar').style.width = progress + '%';

      let sessionInfo = '';
      if (session.mode === 'spaced') {
        sessionInfo = 'Smart Study Session';
      } else if (session.mode === 'chunking') {
        sessionInfo = `Chunk ${Math.floor(session.currentCardIndex / session.chunkSize) + 1}`;
      } else {
        sessionInfo = 'Full Review';
      }

      document.getElementById('session-info').textContent = sessionInfo;
      document.getElementById('session-progress').textContent =
              `Card ${session.currentCardIndex + 1} of ${session.cards.length}`;
    },

    completeStudySession: function() {
      const session = this.currentStudySession;
      const studyTime = (Date.now() - session.startTime) / 1000 / 60; // minutes
      const accuracy = session.cards.length > 0 ? (session.correctAnswers / session.cards.length) * 100 : 0;

      // Update statistics
      this.studyStatistics.totalStudyTime += studyTime;
      this.studyStatistics.sessionsCompleted++;
      this.studyStatistics.accuracyHistory.push({
        date: new Date(),
        accuracy: accuracy,
        cardsStudied: session.cards.length,
        studyTime: studyTime
      });

      // Update daily study time
      const today = new Date().toDateString();
      this.studyStatistics.dailyStudyTime[today] = (this.studyStatistics.dailyStudyTime[today] || 0) + studyTime;

      this.saveStudyStatistics();

      this.showMessage(`🎉 Session completed! Studied ${session.cards.length} cards with ${Math.round(accuracy)}% accuracy in ${Math.round(studyTime)} minutes`, 'success');
      this.endStudySession();
    },

    endStudySession: function() {
      this.currentStudySession = null;
      document.getElementById('study-setup').style.display = 'block';
      document.getElementById('study-session').style.display = 'none';
      this.updateDashboard();
    },

    // Statistics and Analytics
    loadStudyStatistics: function() {
      const saved = localStorage.getItem('chunking_statistics');
      if (saved) {
        this.studyStatistics = { ...this.studyStatistics, ...JSON.parse(saved) };
      }
    },

    saveStudyStatistics: function() {
      localStorage.setItem('chunking_statistics', JSON.stringify(this.studyStatistics));
    },

    updateDashboard: function() {
      const dueCards = this.getDueCards().length;
      const masteredCards = this.allCards.filter(card => {
        const sr = this.calculateSpacedRepetition(card);
        return sr.status === 'mastered';
      }).length;

      // Calculate streak
      const today = new Date().toDateString();
      let streak = 0;
      let currentDate = new Date();
      while (this.studyStatistics.dailyStudyTime[currentDate.toDateString()]) {
        streak++;
        currentDate.setDate(currentDate.getDate() - 1);
      }

      document.getElementById('cards-due').textContent = dueCards;
      document.getElementById('streak-count').textContent = streak;
      document.getElementById('mastered-cards').textContent = masteredCards;
    },

    updateAnalytics: function() {
      const stats = this.studyStatistics;

      document.getElementById('total-study-time').textContent = `${Math.round(stats.totalStudyTime)}m`;

      const avgAccuracy = stats.accuracyHistory.length > 0 ?
              Math.round(stats.accuracyHistory.reduce((sum, entry) => sum + entry.accuracy, 0) / stats.accuracyHistory.length) : 0;
      document.getElementById('avg-accuracy').textContent = `${avgAccuracy}%`;

      const totalCardsReviewed = stats.accuracyHistory.reduce((sum, entry) => sum + entry.cardsStudied, 0);
      document.getElementById('cards-reviewed').textContent = totalCardsReviewed;

      const difficultCards = this.allCards.filter(card => (card.confidence || 0) < 34).length;
      document.getElementById('difficult-cards').textContent = difficultCards;

      this.updatePerformanceInsights();
      this.drawCharts();
    },

    updatePerformanceInsights: function() {
      const insights = [];
      const stats = this.studyStatistics;

      if (stats.accuracyHistory.length >= 5) {
        const recent = stats.accuracyHistory.slice(-5);
        const avgRecent = recent.reduce((sum, entry) => sum + entry.accuracy, 0) / recent.length;
        const older = stats.accuracyHistory.slice(-10, -5);
        if (older.length > 0) {
          const avgOlder = older.reduce((sum, entry) => sum + entry.accuracy, 0) / older.length;
          if (avgRecent > avgOlder + 5) {
            insights.push('📈 Your accuracy is improving! Keep up the great work.');
          } else if (avgRecent < avgOlder - 5) {
            insights.push('📉 Consider reviewing difficult cards more frequently.');
          }
        }
      }

      const difficultCards = this.allCards.filter(card => (card.confidence || 0) < 34);
      if (difficultCards.length > 0) {
        insights.push(`🎯 You have ${difficultCards.length} cards that need extra attention.`);
      }

      const masteredCards = this.allCards.filter(card => {
        const sr = this.calculateSpacedRepetition(card);
        return sr.status === 'mastered';
      });
      if (masteredCards.length > 0) {
        insights.push(`🏆 Excellent! You've mastered ${masteredCards.length} cards.`);
      }

      if (insights.length === 0) {
        insights.push('📊 Complete more study sessions to see detailed insights.');
      }

      document.getElementById('performance-insights').innerHTML = insights.map(insight => `<p>${insight}</p>`).join('');
    },

    drawCharts: function() {
      // Simple text-based charts since we don't have Chart.js
      const accuracyChart = document.getElementById('accuracy-chart');
      const timeChart = document.getElementById('time-chart');

      if (accuracyChart) {
        accuracyChart.style.display = 'none';
        const parent = accuracyChart.parentNode;
        const chartText = document.createElement('div');
        chartText.innerHTML = '<p>📈 Accuracy trend visualization would appear here with Chart.js integration</p>';
        parent.appendChild(chartText);
      }

      if (timeChart) {
        timeChart.style.display = 'none';
        const parent = timeChart.parentNode;
        const chartText = document.createElement('div');
        chartText.innerHTML = '<p>⏱️ Study time visualization would appear here with Chart.js integration</p>';
        parent.appendChild(chartText);
      }
    },

    // Import/Export and Utilities
    updateDeckSelects: function() {
      const selects = ['study-deck-select', 'import-deck', 'manage-deck'];
      selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (!select) return;

        const currentValue = select.value;

        if (selectId === 'manage-deck') {
          select.innerHTML = '<option value="">All Decks</option>';
        } else {
          select.innerHTML = '<option value="">Choose a deck...</option>';
        }

        this.allDecks.forEach(deck => {
          const option = document.createElement('option');
          option.value = deck.id;
          option.textContent = deck.name;
          select.appendChild(option);
        });

        select.value = currentValue;
      });
    },

    importCards: function() {
      const deckId = document.getElementById('import-deck').value;
      if (!deckId) {
        this.showMessage('Please select a deck first', 'error');
        return;
      }

      const csvText = document.getElementById('csv-text').value;
      const fileInput = document.getElementById('csv-file');

      if (fileInput.files[0] && !csvText) {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.processCSVContent(e.target.result, deckId);
        };
        reader.readAsText(fileInput.files[0]);
      } else if (csvText) {
        this.processCSVContent(csvText, deckId);
      } else {
        this.showMessage('Please provide CSV content either by file or text', 'error');
      }
    },

    processCSVContent: function(csvContent, deckId) {
      try {
        const lines = csvContent.split('\n').filter(line => line.trim());
        const cards = [];

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          if (!line) continue;

          const parts = this.parseCSVLine(line);
          const [front, back, tagsStr] = parts;

          if (front && back) {
            const tags = tagsStr ? tagsStr.split(';').map(tag => tag.trim()).filter(tag => tag) : [];

            cards.push({
              id: 'local_' + Date.now() + Math.random(),
              deckId: deckId,
              front: front.trim(),
              back: back.trim(),
              tags: tags,
              confidence: 0,
              correct: 0,
              incorrect: 0,
              created: new Date(),
              userId: this.currentUser.uid,
              spacedRepetition: {
                interval: 1,
                easeFactor: 2.5,
                dueDate: new Date(),
                reviewCount: 0
              }
            });
          }
        }

        if (cards.length === 0) {
          this.showMessage('No valid cards found in CSV. Format: Front,Back,Tags', 'error');
          return;
        }

        const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
        localCards.push(...cards);
        localStorage.setItem('chunking_cards', JSON.stringify(localCards));

        this.updateDeckCardCount(deckId, cards.length);
        this.loadCards();
        this.loadDecks();
        document.getElementById('csv-text').value = '';
        document.getElementById('csv-file').value = '';
        this.showMessage(`Successfully imported ${cards.length} cards! 🎉`, 'success');

      } catch (error) {
        console.error('Error importing cards:', error);
        this.showMessage('Error importing cards. Please check the CSV format.', 'error');
      }
    },

    parseCSVLine: function(line) {
      const result = [];
      let current = '';
      let inQuotes = false;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          result.push(current);
          current = '';
        } else {
          current += char;
        }
      }
      result.push(current);

      return result.map(item => item.replace(/^"|"$/g, ''));
    },

    filterCards: function() {
      const deckFilter = document.getElementById('manage-deck').value;
      const searchText = document.getElementById('search-cards').value.toLowerCase();
      const confidenceFilter = document.getElementById('confidence-filter').value;

      let filteredCards = this.allCards;

      if (deckFilter) {
        filteredCards = filteredCards.filter(card => card.deckId === deckFilter);
      }

      if (searchText) {
        filteredCards = filteredCards.filter(card =>
                card.front.toLowerCase().includes(searchText) ||
                card.back.toLowerCase().includes(searchText) ||
                (card.tags && card.tags.some(tag => tag.toLowerCase().includes(searchText)))
        );
      }

      if (confidenceFilter) {
        filteredCards = filteredCards.filter(card => {
          const confidence = card.confidence || 0;
          switch (confidenceFilter) {
            case 'low': return confidence <= 33;
            case 'medium': return confidence > 33 && confidence <= 66;
            case 'high': return confidence > 66;
            default: return true;
          }
        });
      }

      this.renderCardList(filteredCards);
    },

    renderCardList: function(cards) {
      const listElement = document.getElementById('card-list');

      if (cards.length === 0) {
        listElement.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: var(--text-color); opacity: 0.8;">
                            <div style="font-size: 2em; margin-bottom: 15px;">🔍</div>
                            <h3 style="color: var(--text-color); margin-bottom: 10px;">No cards found</h3>
                            <p style="color: var(--text-color); opacity: 0.7;">Try adjusting your filters.</p>
                        </div>
                    `;
        return;
      }

      listElement.innerHTML = cards.map(card => {
        const deck = this.allDecks.find(d => d.id === card.deckId);
        const confidence = card.confidence || 0;
        const confidenceClass = confidence >= 67 ? 'confidence-high' :
                confidence >= 34 ? 'confidence-medium' : 'confidence-low';

        const hasMedia = card.frontMedia || card.backMedia;
        const mediaIndicator = hasMedia ? ' 📎' : '';

        const tags = card.tags && card.tags.length > 0 ?
                `<div class="tags-container">${card.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}</div>` : '';

        return `
                        <div class="card-item" style="padding: 20px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--primary-color); margin-bottom: 5px;">${card.front}${mediaIndicator}</div>
                                <div style="color: var(--text-color); opacity: 0.8; margin-bottom: 8px;">${card.back}</div>
                                ${tags}
                                <div style="font-size: 0.85em; color: var(--text-color); opacity: 0.7;">
                                    📚 ${deck ? deck.name : 'Unknown Deck'} | 🎯 ${confidence}% confidence
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <span class="confidence-indicator ${confidenceClass}" style="position: relative; top: 0; right: 0;"></span>
                                <button class="btn btn-danger delete-card-btn" data-card-id="${card.id}" style="padding: 8px 15px; font-size: 0.9em;">
                                    🗑️ Delete
                                </button>
                            </div>
                        </div>
                    `;
      }).join('');

      listElement.querySelectorAll('.delete-card-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          const cardId = btn.getAttribute('data-card-id');
          this.deleteCard(cardId);
        });
      });
    },

    // Settings
    loadSettings: function() {
      const theme = localStorage.getItem('chunking_theme') || 'teal';
      const fontSize = localStorage.getItem('chunking_fontSize') || '16px';
      const cardFont = localStorage.getItem('chunking_cardFont') || 'inherit';
      const cardFontSize = localStorage.getItem('chunking_cardFontSize') || '1.3em';
      const cardHeight = localStorage.getItem('chunking_cardHeight') || '250px';
      const cardBgColor = localStorage.getItem('chunking_cardBgColor') || '#ffffff';
      const cardTextColor = localStorage.getItem('chunking_cardTextColor') || '#1e293b';

      document.getElementById('theme-select').value = theme;
      document.getElementById('font-size').value = fontSize;
      document.getElementById('card-font').value = cardFont;
      document.getElementById('card-font-size').value = cardFontSize;
      document.getElementById('card-height').value = cardHeight;
      document.getElementById('card-bg-color').value = cardBgColor;
      document.getElementById('card-text-color').value = cardTextColor;

      this.changeTheme();
      this.changeFontSize();
      this.updateCardStyle();
    },

    changeTheme: function() {
      const theme = document.getElementById('theme-select').value;
      document.body.setAttribute('data-theme', theme);
      localStorage.setItem('chunking_theme', theme);
    },

    changeFontSize: function() {
      const fontSize = document.getElementById('font-size').value;
      document.body.style.fontSize = fontSize;
      localStorage.setItem('chunking_fontSize', fontSize);
    },

    updateCardStyle: function() {
      const cardFont = document.getElementById('card-font').value;
      const cardFontSize = document.getElementById('card-font-size').value;
      const cardHeight = document.getElementById('card-height').value;
      const cardBgColor = document.getElementById('card-bg-color').value;
      const cardTextColor = document.getElementById('card-text-color').value;

      document.documentElement.style.setProperty('--card-font', cardFont);
      document.documentElement.style.setProperty('--card-font-size', cardFontSize);
      document.documentElement.style.setProperty('--card-height', cardHeight);
      document.documentElement.style.setProperty('--card-background', cardBgColor);
      document.documentElement.style.setProperty('--card-text-color', cardTextColor);

      localStorage.setItem('chunking_cardFont', cardFont);
      localStorage.setItem('chunking_cardFontSize', cardFontSize);
      localStorage.setItem('chunking_cardHeight', cardHeight);
      localStorage.setItem('chunking_cardBgColor', cardBgColor);
      localStorage.setItem('chunking_cardTextColor', cardTextColor);
    },

    exportData: function() {
      try {
        const data = {
          decks: this.allDecks,
          cards: this.allCards,
          statistics: this.studyStatistics,
          settings: {
            theme: localStorage.getItem('chunking_theme'),
            fontSize: localStorage.getItem('chunking_fontSize'),
            cardFont: localStorage.getItem('chunking_cardFont'),
            cardFontSize: localStorage.getItem('chunking_cardFontSize'),
            cardHeight: localStorage.getItem('chunking_cardHeight'),
            cardBgColor: localStorage.getItem('chunking_cardBgColor'),
            cardTextColor: localStorage.getItem('chunking_cardTextColor')
          },
          version: '2.0',
          exported: new Date().toISOString(),
          user: this.currentUser.displayName || this.currentUser.email || 'Guest User'
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `enhanced-flashcards-backup-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        this.showMessage('Data exported successfully! 📤', 'success');
      } catch (error) {
        console.error('Error exporting data:', error);
        this.showMessage('Error exporting data. Please try again.', 'error');
      }
    },

    clearAllData: function() {
      if (!confirm('⚠️ WARNING: This will delete ALL your decks, cards, and statistics permanently!')) return;
      if (!confirm('Are you absolutely sure? This action cannot be undone!')) return;
      if (!confirm('Final confirmation: Delete everything?')) return;

      try {
        localStorage.removeItem('chunking_decks');
        localStorage.removeItem('chunking_cards');
        localStorage.removeItem('chunking_statistics');

        this.allDecks = [];
        this.allCards = [];
        this.studyStatistics = {
          totalStudyTime: 0,
          sessionsCompleted: 0,
          accuracyHistory: [],
          dailyStudyTime: {}
        };
        this.currentStudySession = null;

        this.loadDecks();
        this.loadCards();
        this.updateDeckSelects();
        this.updateDashboard();

        if (document.getElementById('study-session').style.display !== 'none') {
          this.endStudySession();
        }

        this.showMessage('All data cleared successfully! 🗑️', 'success');
      } catch (error) {
        console.error('Error clearing data:', error);
        this.showMessage('Error clearing data. Please try again.', 'error');
      }
    },

    // ===== MOCK EXAM SYSTEM =====

    currentExam: null,
    examTimer: null,
    examStartTime: null,

    showExamSetup: function() {
      console.log('🎯 Showing exam setup');
      document.getElementById('exam-setup-view').style.display = 'block';
      document.getElementById('exam-taking-view').style.display = 'none';
      document.getElementById('exam-results-view').style.display = 'none';

      this.populateExamDeckSelection();
      this.loadExamHistory();
    },

    populateExamDeckSelection: function() {
      const container = document.getElementById('exam-deck-selection');
      if (this.allDecks.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: var(--text-color); opacity: 0.7;">No decks available. Create some decks first!</p>';
        return;
      }

      container.innerHTML = this.allDecks.map(deck => {
        const cardCount = this.allCards.filter(card => card.deckId === deck.id).length;
        return `
          <label style="display: flex; align-items: center; gap: 10px; padding: 10px; border-radius: 8px; cursor: pointer; transition: background 0.2s;"
                 onmouseover="this.style.background='var(--hover-color)'"
                 onmouseout="this.style.background='transparent'">
            <input type="checkbox" class="exam-deck-checkbox" value="${deck.id}" ${cardCount > 0 ? '' : 'disabled'}>
            <div style="flex: 1;">
              <div style="font-weight: 600; color: var(--text-color);">${deck.name}</div>
              <div style="font-size: 0.9em; color: var(--text-color); opacity: 0.7;">${cardCount} cards available</div>
            </div>
          </label>
        `;
      }).join('');
    },

    startMockExam: function() {
      console.log('🚀 Starting mock exam');

      const examName = document.getElementById('exam-name').value.trim() || 'Mock Exam';
      const selectedDecks = Array.from(document.querySelectorAll('.exam-deck-checkbox:checked')).map(cb => cb.value);
      const questionCount = document.getElementById('exam-question-count').value;
      const timeLimit = parseInt(document.getElementById('exam-time-limit').value);
      const includeFlashcard = document.getElementById('exam-type-flashcard').checked;
      const includeReverse = document.getElementById('exam-type-reverse').checked;

      if (selectedDecks.length === 0) {
        this.showMessage('Please select at least one deck', 'error');
        return;
      }

      if (!includeFlashcard && !includeReverse) {
        this.showMessage('Please select at least one question type', 'error');
        return;
      }

      // Get cards from selected decks
      let availableCards = this.allCards.filter(card => selectedDecks.includes(card.deckId));

      if (availableCards.length === 0) {
        this.showMessage('No cards available in selected decks', 'error');
        return;
      }

      // Create question pool
      let questions = [];

      if (includeFlashcard) {
        questions.push(...availableCards.map(card => ({
          id: card.id,
          type: 'flashcard',
          question: card.front,
          answer: card.back,
          deckId: card.deckId,
          tags: card.tags || []
        })));
      }

      if (includeReverse) {
        questions.push(...availableCards.map(card => ({
          id: card.id + '_reverse',
          type: 'reverse',
          question: card.back,
          answer: card.front,
          deckId: card.deckId,
          tags: card.tags || []
        })));
      }

      // Shuffle and limit questions
      this.shuffleArray(questions);
      const finalQuestionCount = questionCount === 'all' ? questions.length : Math.min(parseInt(questionCount), questions.length);
      questions = questions.slice(0, finalQuestionCount);

      // Initialize exam
      this.currentExam = {
        name: examName,
        questions: questions,
        currentQuestionIndex: 0,
        answers: [],
        startTime: new Date(),
        timeLimit: timeLimit,
        timeRemaining: timeLimit * 60, // Convert to seconds
        selectedDecks: selectedDecks
      };

      this.examStartTime = Date.now();

      // Start timer if time limit is set
      if (timeLimit > 0) {
        this.startExamTimer();
      }

      // Show exam interface
      document.getElementById('exam-setup-view').style.display = 'none';
      document.getElementById('exam-taking-view').style.display = 'block';
      document.getElementById('current-exam-name').textContent = examName;

      this.showCurrentExamQuestion();
    },

    startExamTimer: function() {
      const timerDisplay = document.getElementById('exam-timer');

      this.examTimer = setInterval(() => {
        this.currentExam.timeRemaining--;

        const minutes = Math.floor(this.currentExam.timeRemaining / 60);
        const seconds = this.currentExam.timeRemaining % 60;
        timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

        // Change color when time is running low
        if (this.currentExam.timeRemaining <= 300) { // 5 minutes
          timerDisplay.style.color = '#ef4444';
        } else if (this.currentExam.timeRemaining <= 600) { // 10 minutes
          timerDisplay.style.color = '#f59e0b';
        }

        if (this.currentExam.timeRemaining <= 0) {
          this.endMockExam();
        }
      }, 1000);
    },

    showCurrentExamQuestion: function() {
      const question = this.currentExam.questions[this.currentExam.currentQuestionIndex];
      const progress = `Question ${this.currentExam.currentQuestionIndex + 1} of ${this.currentExam.questions.length}`;

      document.getElementById('exam-progress').textContent = progress;
      document.getElementById('exam-question-content').innerHTML = `
        <div style="margin-bottom: 15px; font-size: 0.9em; color: var(--text-color); opacity: 0.7;">
          ${question.type === 'flashcard' ? 'Front → Back' : 'Back → Front'}
        </div>
        <h4 style="color: var(--text-color); margin-bottom: 30px; line-height: 1.4;">${question.question}</h4>
      `;

      // Reset answer section
      document.getElementById('exam-answer-input').value = '';
      document.getElementById('exam-answer-reveal').style.display = 'none';
      document.getElementById('exam-show-answer-btn').style.display = 'inline-block';
      document.getElementById('exam-next-question-btn').style.display = 'none';
    },

    showExamAnswer: function() {
      const question = this.currentExam.questions[this.currentExam.currentQuestionIndex];
      const userAnswer = document.getElementById('exam-answer-input').value.trim();

      // Store user's answer
      this.currentExam.answers[this.currentExam.currentQuestionIndex] = {
        questionId: question.id,
        userAnswer: userAnswer,
        correctAnswer: question.answer,
        question: question.question,
        type: question.type,
        deckId: question.deckId,
        tags: question.tags
      };

      // Show correct answer
      document.getElementById('exam-correct-answer').innerHTML = question.answer;
      document.getElementById('exam-answer-reveal').style.display = 'block';
      document.getElementById('exam-show-answer-btn').style.display = 'none';
    },

    markExamAnswer: function(isCorrect) {
      // Update the answer with correctness
      this.currentExam.answers[this.currentExam.currentQuestionIndex].isCorrect = isCorrect;

      // Show next question button
      document.getElementById('exam-next-question-btn').style.display = 'inline-block';
      document.getElementById('exam-mark-correct-btn').style.display = 'none';
      document.getElementById('exam-mark-incorrect-btn').style.display = 'none';
    },

    nextExamQuestion: function() {
      this.currentExam.currentQuestionIndex++;

      if (this.currentExam.currentQuestionIndex >= this.currentExam.questions.length) {
        this.endMockExam();
      } else {
        this.showCurrentExamQuestion();
      }
    },

    endMockExam: function() {
      if (this.examTimer) {
        clearInterval(this.examTimer);
        this.examTimer = null;
      }

      // Calculate results
      const results = this.calculateExamResults();

      // Save exam to history
      this.saveExamToHistory(results);

      // Show results
      this.showExamResults(results);
    },

    calculateExamResults: function() {
      const totalQuestions = this.currentExam.questions.length;
      const answeredQuestions = this.currentExam.answers.filter(a => a.isCorrect !== undefined).length;
      const correctAnswers = this.currentExam.answers.filter(a => a.isCorrect === true).length;
      const incorrectAnswers = this.currentExam.answers.filter(a => a.isCorrect === false).length;

      const score = answeredQuestions > 0 ? Math.round((correctAnswers / answeredQuestions) * 100) : 0;
      const completionTime = Math.round((Date.now() - this.examStartTime) / 1000 / 60); // minutes

      // Analyze weak areas
      const weakAreas = this.analyzeWeakAreas();

      return {
        examName: this.currentExam.name,
        totalQuestions,
        answeredQuestions,
        correctAnswers,
        incorrectAnswers,
        score,
        completionTime,
        weakAreas,
        answers: this.currentExam.answers,
        selectedDecks: this.currentExam.selectedDecks,
        date: new Date()
      };
    },

    analyzeWeakAreas: function() {
      const incorrectAnswers = this.currentExam.answers.filter(a => a.isCorrect === false);
      const deckAnalysis = {};
      const tagAnalysis = {};

      incorrectAnswers.forEach(answer => {
        // Analyze by deck
        const deck = this.allDecks.find(d => d.id === answer.deckId);
        if (deck) {
          if (!deckAnalysis[deck.name]) {
            deckAnalysis[deck.name] = { count: 0, deckId: deck.id };
          }
          deckAnalysis[deck.name].count++;
        }

        // Analyze by tags
        if (answer.tags && answer.tags.length > 0) {
          answer.tags.forEach(tag => {
            if (!tagAnalysis[tag]) {
              tagAnalysis[tag] = 0;
            }
            tagAnalysis[tag]++;
          });
        }
      });

      return {
        decks: Object.entries(deckAnalysis)
                .sort(([,a], [,b]) => b.count - a.count)
                .slice(0, 5), // Top 5 problematic decks
        tags: Object.entries(tagAnalysis)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5) // Top 5 problematic tags
      };
    },

    saveExamToHistory: function(results) {
      const examHistory = JSON.parse(localStorage.getItem('chunking_exam_history') || '[]');
      examHistory.unshift(results); // Add to beginning

      // Keep only last 20 exams
      if (examHistory.length > 20) {
        examHistory.splice(20);
      }

      localStorage.setItem('chunking_exam_history', JSON.stringify(examHistory));
    },

    showExamResults: function(results) {
      document.getElementById('exam-taking-view').style.display = 'none';
      document.getElementById('exam-results-view').style.display = 'block';

      // Show score and grade
      document.getElementById('exam-final-score').textContent = `${results.score}%`;
      document.getElementById('exam-grade-text').textContent = this.getGradeText(results.score);
      document.getElementById('exam-completion-time').textContent = `Completed in ${results.completionTime} minutes`;

      // Show performance stats
      document.getElementById('exam-performance-stats').innerHTML = `
        <div style="margin-bottom: 10px;">
          <strong>Questions Answered:</strong> ${results.answeredQuestions} / ${results.totalQuestions}
        </div>
        <div style="margin-bottom: 10px;">
          <strong>Correct:</strong> <span style="color: #10b981;">${results.correctAnswers}</span>
        </div>
        <div style="margin-bottom: 10px;">
          <strong>Incorrect:</strong> <span style="color: #ef4444;">${results.incorrectAnswers}</span>
        </div>
        <div>
          <strong>Accuracy:</strong> ${results.score}%
        </div>
      `;

      // Show focus areas
      this.showFocusAreas(results.weakAreas);
    },

    shuffleArray: function(array) {
      for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
      }
    },

    getGradeText: function(score) {
      if (score >= 90) return '🏆 Excellent!';
      if (score >= 80) return '🎉 Great Job!';
      if (score >= 70) return '👍 Good Work!';
      if (score >= 60) return '📚 Keep Studying!';
      return '💪 More Practice Needed!';
    },

    showFocusAreas: function(weakAreas) {
      const container = document.getElementById('exam-focus-areas');

      if (weakAreas.decks.length === 0 && weakAreas.tags.length === 0) {
        container.innerHTML = '<p style="color: #10b981;">🎯 Great job! No major weak areas identified.</p>';
        return;
      }

      let html = '';

      if (weakAreas.decks.length > 0) {
        html += '<div style="margin-bottom: 15px;"><strong>Challenging Decks:</strong></div>';
        weakAreas.decks.forEach(([deckName, data]) => {
          html += `<div style="margin-bottom: 8px; padding: 8px; background: rgba(239, 68, 68, 0.1); border-radius: 6px;">
            📚 ${deckName} (${data.count} mistakes)
          </div>`;
        });
      }

      if (weakAreas.tags.length > 0) {
        html += '<div style="margin-bottom: 15px; margin-top: 20px;"><strong>Challenging Topics:</strong></div>';
        weakAreas.tags.forEach(([tag, count]) => {
          html += `<div style="margin-bottom: 8px; padding: 8px; background: rgba(245, 158, 11, 0.1); border-radius: 6px;">
            🏷️ ${tag} (${count} mistakes)
          </div>`;
        });
      }

      container.innerHTML = html;
    },

    createFocusDeck: function() {
      if (!this.currentExam || !this.currentExam.answers) {
        this.showMessage('No exam data available', 'error');
        return;
      }

      const incorrectAnswers = this.currentExam.answers.filter(a => a.isCorrect === false);

      if (incorrectAnswers.length === 0) {
        this.showMessage('No incorrect answers to create focus deck from!', 'error');
        return;
      }

      // Get unique card IDs from incorrect answers
      const cardIds = [...new Set(incorrectAnswers.map(a => a.questionId.replace('_reverse', '')))];
      const focusCards = this.allCards.filter(card => cardIds.includes(card.id));

      if (focusCards.length === 0) {
        this.showMessage('Unable to find cards for focus deck', 'error');
        return;
      }

      // Create focus deck
      const focusDeckName = `${this.currentExam.name} - Focus Areas`;
      const focusDeckData = {
        name: focusDeckName,
        description: `Auto-generated focus deck from exam mistakes (${focusCards.length} cards)`,
        created: new Date().toISOString(),
        cardCount: 0,
        userId: this.currentUser.uid,
        id: 'local_focus_' + Date.now()
      };

      // Save focus deck
      const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
      localDecks.push(focusDeckData);
      localStorage.setItem('chunking_decks', JSON.stringify(localDecks));

      // Create copies of focus cards for the new deck
      const focusDeckCards = focusCards.map(card => ({
        ...card,
        id: 'local_focus_card_' + Date.now() + '_' + Math.random(),
        deckId: focusDeckData.id,
        created: new Date().toISOString(),
        confidence: 0, // Reset confidence for focused study
        correct: 0,
        incorrect: 0
      }));

      // Save focus cards
      const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
      localCards.push(...focusDeckCards);
      localStorage.setItem('chunking_cards', JSON.stringify(localCards));

      // Update deck card count
      focusDeckData.cardCount = focusDeckCards.length;
      localStorage.setItem('chunking_decks', JSON.stringify(localDecks));

      // Refresh data
      this.loadDecks();
      this.loadCards();

      this.showMessage(`Focus deck "${focusDeckName}" created with ${focusDeckCards.length} cards! 📚`, 'success');
    },

    reviewMistakes: function() {
      if (!this.currentExam || !this.currentExam.answers) {
        this.showMessage('No exam data available', 'error');
        return;
      }

      const incorrectAnswers = this.currentExam.answers.filter(a => a.isCorrect === false);

      if (incorrectAnswers.length === 0) {
        this.showMessage('No mistakes to review - perfect score!', 'success');
        return;
      }

      // Create a simple review modal
      const reviewHtml = incorrectAnswers.map((answer, index) => `
        <div style="margin-bottom: 20px; padding: 20px; border: 1px solid var(--border-color); border-radius: 10px;">
          <div style="font-weight: 600; color: var(--primary-color); margin-bottom: 10px;">
            Question ${index + 1}: ${answer.type === 'flashcard' ? 'Front → Back' : 'Back → Front'}
          </div>
          <div style="margin-bottom: 10px;">
            <strong>Question:</strong> ${answer.question}
          </div>
          <div style="margin-bottom: 10px;">
            <strong>Your Answer:</strong> <span style="color: #ef4444;">${answer.userAnswer || '(No answer provided)'}</span>
          </div>
          <div>
            <strong>Correct Answer:</strong> <span style="color: #10b981;">${answer.correctAnswer}</span>
          </div>
        </div>
      `).join('');

      // Show in a simple alert for now (could be enhanced with a proper modal)
      const reviewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
      reviewWindow.document.write(`
        <html>
          <head><title>Exam Review - ${this.currentExam.name}</title></head>
          <body style="font-family: Arial, sans-serif; padding: 20px; line-height: 1.6;">
            <h2>📝 Exam Review: ${this.currentExam.name}</h2>
            <p><strong>Mistakes to Review:</strong> ${incorrectAnswers.length}</p>
            ${reviewHtml}
          </body>
        </html>
      `);
    },

    retakeExam: function() {
      // Reset to exam setup with same settings
      this.showExamSetup();

      // Pre-fill the form with previous exam settings
      if (this.currentExam) {
        document.getElementById('exam-name').value = this.currentExam.name + ' (Retake)';

        // Check the same decks
        this.currentExam.selectedDecks.forEach(deckId => {
          const checkbox = document.querySelector(`.exam-deck-checkbox[value="${deckId}"]`);
          if (checkbox) checkbox.checked = true;
        });
      }
    },

    loadExamHistory: function() {
      const examHistory = JSON.parse(localStorage.getItem('chunking_exam_history') || '[]');
      const container = document.getElementById('exam-history-list');

      if (examHistory.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: var(--text-color); opacity: 0.7;">No exams taken yet. Create your first mock exam above!</p>';
        return;
      }

      container.innerHTML = examHistory.slice(0, 5).map(exam => {
        const date = new Date(exam.date).toLocaleDateString();
        const scoreColor = exam.score >= 80 ? '#10b981' : exam.score >= 60 ? '#f59e0b' : '#ef4444';

        return `
          <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; border: 1px solid var(--border-color); border-radius: 10px; margin-bottom: 10px;">
            <div>
              <div style="font-weight: 600; color: var(--text-color);">${exam.examName}</div>
              <div style="font-size: 0.9em; color: var(--text-color); opacity: 0.7;">${date} • ${exam.completionTime} min</div>
            </div>
            <div style="text-align: right;">
              <div style="font-size: 1.2em; font-weight: 600; color: ${scoreColor};">${exam.score}%</div>
              <div style="font-size: 0.8em; color: var(--text-color); opacity: 0.7;">${exam.correctAnswers}/${exam.totalQuestions}</div>
            </div>
          </div>
        `;
      }).join('');
    },

    showMessage: function(message, type = 'success') {
      const messageDiv = document.createElement('div');
      messageDiv.className = type === 'success' ? 'success' : 'error';
      messageDiv.textContent = message;
      messageDiv.style.position = 'fixed';
      messageDiv.style.top = '25px';
      messageDiv.style.right = '25px';
      messageDiv.style.zIndex = '1001';
      messageDiv.style.padding = '15px 25px';
      messageDiv.style.borderRadius = '15px';
      messageDiv.style.maxWidth = '350px';
      messageDiv.style.boxShadow = '0 10px 30px rgba(0,0,0,0.2)';
      messageDiv.style.animation = 'slideIn 0.3s ease';

      document.body.appendChild(messageDiv);

      setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
          if (document.body.contains(messageDiv)) {
            document.body.removeChild(messageDiv);
          }
        }, 300);
      }, 3000);
    },

    setupKeyboardShortcuts: function() {
      document.addEventListener('keydown', (e) => {
        if (!this.currentStudySession) return;

        switch (e.key.toLowerCase()) {
          case ' ':
            e.preventDefault();
            if (document.getElementById('flip-btn').style.display !== 'none') {
              this.flipCard();
            }
            break;
          case '1':
            if (document.getElementById('again-btn').style.display !== 'none') {
              this.markDifficulty('again');
            }
            break;
          case '2':
            if (document.getElementById('hard-btn').style.display !== 'none') {
              this.markDifficulty('hard');
            }
            break;
          case '3':
            if (document.getElementById('good-btn').style.display !== 'none') {
              this.markDifficulty('good');
            }
            break;
          case '4':
            if (document.getElementById('easy-btn').style.display !== 'none') {
              this.markDifficulty('easy');
            }
            break;
          case 'c':
            if (document.getElementById('correct-btn').style.display !== 'none') {
              this.markCorrect();
            }
            break;
          case 'x':
            if (document.getElementById('incorrect-btn').style.display !== 'none') {
              this.markIncorrect();
            }
            break;
        }
      });
    }
  };

  // Initialize the app when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    EnhancedChunkingApp.init();
  });
</script>
</body>
</html>/script>
</body>
</html>