<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>ChunkyMonkey - Smart Flashcards</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lobster&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html, body {
      width: 100%;
      max-width: 100%;
      overflow-x: hidden;
    }

    :root {
      --primary-color: #14b8a6;
      --secondary-color: #ec4899;
      --accent-color: #8b5cf6;
      --danger-color: #ef4444;
      --danger-bg: #fef2f2;
      --danger-hover: #fecaca;
      --warning-color: #f59e0b;
      --success-color: var(--primary-color);
      --bg-color: #f8fafc;
      --card-bg: #ffffff;f
      --text-color: #1e293b;
      --border-color: #e2e8f0;
      --shadow: 0 4px 20px rgba(20, 184, 166, 0.1);
      --gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    }

    [data-theme="dark"] {
      --bg-color: #0f172a;
      --card-bg: #1e293b;
      --text-color: #ffffff;
      --border-color: #334155;
      --shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      --danger-bg: #2d1b1b;
      --danger-hover: #3d2525;
    }

    [data-theme="dark"] .nav-tab {
      color: #ffffff;
    }

    [data-theme="dark"] .nav-tab.active {
      color: white;
    }

    body {
      font-family: var(--card-font, 'Inter', sans-serif);
      background: var(--bg-color);
      color: var(--text-color);
      line-height: 1.6;
      transition: all 0.3s ease;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    /* Authentication Screen */
    #auth-screen {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background: var(--gradient);
    }

    .auth-container {
      background: var(--card-bg);
      padding: 40px;
      border-radius: 20px;
      box-shadow: var(--shadow);
      width: 100%;
      max-width: 400px;
      text-align: center;
    }

    .auth-tabs {
      display: flex;
      margin-bottom: 30px;
      border-radius: 10px;
      overflow: hidden;
      background: var(--bg-color);
    }

    .auth-tab {
      flex: 1;
      padding: 12px;
      background: transparent;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      color: var(--text-color);
    }

    .auth-tab.active {
      background: var(--primary-color);
      color: white;
    }

    .auth-form {
      display: none;
    }

    .auth-form.active {
      display: block;
    }

    .form-group {
      margin-bottom: 20px;
      text-align: left;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: var(--text-color);
    }

    .form-control {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid var(--border-color);
      border-radius: 10px;
      font-size: 16px;
      transition: all 0.3s ease;
      background: var(--card-bg);
      color: var(--text-color);
    }

    .form-control:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-block;
      text-align: center;
    }

    .btn-primary {
      background: var(--primary-color);
      color: white;
    }

    .btn-primary:hover {
      background: #0f9488;
      transform: translateY(-2px);
    }

    .btn-secondary {
      background: var(--border-color);
      color: var(--text-color);
    }

    .btn-secondary:hover {
      background: #cbd5e1;
    }

    .auth-error {
      background: var(--danger-bg);
      color: var(--danger-color);
      padding: 12px;
      border-radius: 8px;
      margin-bottom: 20px;
      display: none;
    }

    /* Main App */
    #main-app {
      display: none;
      min-height: 100vh;
    }

    .header {
      background: var(--card-bg);
      padding: 20px 0;
      box-shadow: var(--shadow);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    .logo {
      font-family: 'Lobster', cursive;
      font-size: 2em;
      background: var(--gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .nav-tabs {
      display: flex;
      gap: 10px;
      margin: 20px 0;
      flex-wrap: wrap;
    }

    .nav-tab {
      padding: 12px 20px;
      background: var(--card-bg);
      border: 2px solid var(--border-color);
      border-radius: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
    }

    .nav-tab.active {
      background: var(--primary-color);
      color: white;
      border-color: var(--primary-color);
    }

    .tab-content {
      display: none;
      padding: 20px 0;
    }

    .tab-content.active {
      display: block;
    }

    /* Cards and Decks */
    .deck-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .deck-card {
      background: var(--card-bg);
      padding: 25px;
      border-radius: 15px;
      box-shadow: var(--shadow);
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
    }

    .deck-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(20, 184, 166, 0.15);
    }

    .delete-btn {
      position: absolute;
      top: 15px;
      right: 15px;
      background: var(--danger-color);
      color: white;
      border: none;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      cursor: pointer;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    .delete-btn:hover {
      background: #dc2626;
      transform: scale(1.1);
    }

    /* Modal */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      overflow-y: auto;
    }

    .modal-content {
      background-color: var(--card-bg);
      margin: 20px auto;
      padding: 30px;
      border-radius: 15px;
      width: 90%;
      max-width: 500px;
      position: relative;
      box-shadow: var(--shadow);
      max-height: calc(100vh - 40px);
      overflow-y: auto;
    }

    .close {
      position: absolute;
      right: 20px;
      top: 15px;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
      color: var(--text-color);
    }

    .close:hover {
      color: var(--danger-color);
    }

    /* Real-time Card Editor Styles */
    .flashcard {
      background: var(--card-bg);
      border: 3px solid var(--border-color);
      border-radius: 25px;
      padding: 50px;
      margin: 25px 0;
      min-height: var(--card-height, 250px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      font-size: var(--card-font-size, 1.3em);
      line-height: 1.6;
      color: var(--card-text-color, inherit);
      font-family: var(--card-font, inherit);
      box-shadow: var(--shadow);
      background: var(--card-background, var(--card-bg));
    }

    .flashcard img, .flashcard video, .flashcard audio {
      max-width: 100%;
      max-height: 200px;
      margin: 10px 0;
      border-radius: 10px;
    }

    .media-preview {
      max-width: 200px;
      max-height: 150px;
      border-radius: 5px;
      margin-top: 10px;
    }

    .media-preview img, .media-preview video {
      width: 100%;
      height: auto;
      border-radius: 5px;
    }

    #card-text-editor:focus {
      border-color: var(--primary-color) !important;
      background: rgba(20, 184, 166, 0.05);
    }

    #card-text-editor:empty:before {
      content: attr(placeholder);
      color: #999;
      font-style: italic;
    }

    .editing-front #edit-front-btn {
      background: var(--accent-color);
    }

    .editing-back #edit-back-btn {
      background: var(--accent-color);
    }

    .color-preset:hover {
      transform: scale(1.05);
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .color-picker-circle {
      width: 50px !important;
      height: 50px !important;
      border-radius: 50% !important;
      border: 3px solid var(--border-color) !important;
      cursor: pointer !important;
      padding: 0 !important;
    }

    .color-picker-circle::-webkit-color-swatch-wrapper {
      padding: 0;
      border-radius: 50%;
    }

    .color-picker-circle::-webkit-color-swatch {
      border: none;
      border-radius: 50%;
    }

    .deck-card-mini {
      background: var(--card-bg);
      border: 2px solid var(--border-color);
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      box-shadow: var(--shadow);
      min-height: 200px;
      display: flex;
      flex-direction: column;
    }

    .deck-card-mini:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 25px rgba(20, 184, 166, 0.2);
      border-color: var(--primary-color);
    }

    .deck-card-mini .card-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      font-size: var(--card-font-size, 1.1em);
      font-family: var(--card-font, inherit);
      color: var(--card-text-color, inherit);
      padding: 10px;
    }

    .deck-card-mini .card-actions {
      margin-top: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .confidence-indicator {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 3px solid #fff;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    }

    .confidence-high { background: var(--success-color); }
    .confidence-medium { background: var(--warning-color); }
    .confidence-low { background: var(--danger-color); }

    /* Responsive */
    @media (max-width: 768px) {
      .container {
        padding: 10px;
      }
      
      .auth-container {
        padding: 20px;
        margin: 20px;
      }
      
      .deck-grid {
        grid-template-columns: 1fr;
      }
      
      .nav-tabs {
        justify-content: center;
      }
      
      .nav-tab {
        flex: 1;
        text-align: center;
        min-width: 120px;
      }
    }
  </style>
</head>
<body>

<!-- Authentication Screen -->
<div id="auth-screen">
  <div class="auth-container">
    <h1 class="logo">🐵 ChunkyMonkey</h1>
    <p style="margin-bottom: 30px; color: var(--text-color); opacity: 0.8;">Smart spaced repetition learning</p>
    
    <div id="auth-error" class="auth-error"></div>
    
    <div class="auth-tabs">
      <button class="auth-tab active" id="login-tab">Sign In</button>
      <button class="auth-tab" id="signup-tab">Sign Up</button>
    </div>

    <!-- Login Form -->
    <form id="login-form" class="auth-form active">
      <div class="form-group">
        <label for="login-email">Email:</label>
        <input type="email" id="login-email" class="form-control" required>
      </div>
      <div class="form-group">
        <label for="login-password">Password:</label>
        <input type="password" id="login-password" class="form-control" required>
      </div>
      <button type="submit" class="btn btn-primary" style="width: 100%; margin-bottom: 10px;">Sign In</button>
      <button type="button" id="forgot-password-btn" class="btn btn-secondary" style="width: 100%; font-size: 0.9em;">🔒 Forgot Password?</button>
    </form>

    <!-- Signup Form -->
    <form id="signup-form" class="auth-form">
      <div class="form-group">
        <label for="signup-name">Name:</label>
        <input type="text" id="signup-name" class="form-control" required>
      </div>
      <div class="form-group">
        <label for="signup-email">Email:</label>
        <input type="email" id="signup-email" class="form-control" required>
      </div>
      <div class="form-group">
        <label for="signup-password">Password:</label>
        <input type="password" id="signup-password" class="form-control" required>
      </div>
      <div class="form-group">
        <label for="signup-confirm">Confirm Password:</label>
        <input type="password" id="signup-confirm" class="form-control" required>
      </div>
      <button type="submit" class="btn btn-primary" style="width: 100%; margin-bottom: 15px;">Create Account</button>
    </form>

    <button id="guest-btn" class="btn btn-secondary" style="width: 100%;">Continue as Guest</button>
  </div>
</div>

<!-- Main Application -->
<div id="main-app">
  <header class="header">
    <div class="header-content">
      <h1 class="logo">🐵 ChunkyMonkey</h1>
      <div class="user-info">
        <span id="user-name">User</span>
        <button id="sign-out-btn" class="btn btn-secondary">Sign Out</button>
      </div>
    </div>
  </header>

  <div class="container">
    <!-- Navigation Tabs -->
    <div class="nav-tabs">
      <button class="nav-tab active" data-tab="decks">📚 My Decks</button>
      <button class="nav-tab" data-tab="study">🧠 Study</button>
      <button class="nav-tab" data-tab="exams">🎯 Mock Exams</button>
      <button class="nav-tab" data-tab="analytics">📊 Analytics</button>
      <button class="nav-tab" data-tab="import">📥 Import Cards</button>
      <button class="nav-tab" data-tab="manage">🗂️ Manage Cards</button>
      <button class="nav-tab" data-tab="settings">⚙️ Settings</button>
    </div>

    <!-- Decks Tab -->
    <div id="decks" class="tab-content active">
      <!-- Deck List View -->
      <div id="deck-list-view">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
          <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">📚 My Decks</h2>
          <button id="create-deck-btn" class="btn btn-primary">✨ Create Deck</button>
        </div>
        <div id="deck-grid" class="deck-grid">
          <p style="text-align: center; color: var(--text-color); opacity: 0.7; grid-column: 1 / -1;">No decks yet. Create your first deck to get started!</p>
        </div>
      </div>

      <!-- Deck Detail View -->
      <div id="deck-detail-view" style="display: none;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; flex-wrap: wrap; gap: 15px;">
          <button id="back-to-decks-btn" class="btn btn-secondary">← Back to Decks</button>
          <h2 id="deck-title" style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; flex: 1; text-align: center;"></h2>
          <button id="add-card-to-deck-btn" class="btn btn-primary">✏️ Add New Card</button>
        </div>

        <div id="deck-cards-grid" class="deck-grid">
          <!-- Cards will be rendered here -->
        </div>
      </div>
    </div>

    <!-- Other tabs will be added here -->
    <!-- Study Tab -->
    <div id="study" class="tab-content">
      <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">📚 Study Session</h2>

      <!-- Study Setup -->
      <div id="study-setup" style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); margin-bottom: 25px;">
        <h3 style="color: var(--primary-color); margin-bottom: 20px;">🎯 Choose What to Study</h3>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
          <div class="form-group">
            <label>Select Deck(s):</label>
            <div id="study-deck-checkboxes" style="max-height: 200px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: 8px; padding: 15px;">
              <!-- Deck checkboxes will be populated here -->
            </div>
          </div>

          <div>
            <div class="form-group" style="margin-bottom: 15px;">
              <label for="study-mode-select">Study Mode:</label>
              <select id="study-mode-select" class="form-control">
                <option value="chunking">🧩 Chunking Method (Recommended)</option>
                <option value="spaced">📅 Spaced Repetition</option>
                <option value="all">📚 Study All Cards</option>
              </select>
            </div>

            <div id="chunking-options" class="form-group">
              <label for="chunk-size">Chunk Size:</label>
              <select id="chunk-size" class="form-control">
                <option value="5">5 cards per chunk</option>
                <option value="7" selected>7 cards per chunk</option>
                <option value="9">9 cards per chunk</option>
                <option value="custom">Custom size</option>
              </select>
              <input type="number" id="custom-chunk-size" class="form-control" style="margin-top: 10px; display: none;" min="3" max="20" value="7" placeholder="Enter chunk size">

              <div style="margin-top: 15px;">
                <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                  <input type="checkbox" id="shuffle-cards" checked>
                  <span>Shuffle cards</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <div style="display: flex; gap: 15px; align-items: center; margin-bottom: 20px;">
          <div style="flex: 1;">
            <strong>Available Cards:</strong> <span id="available-cards-count">0</span>
          </div>
          <button id="start-study-btn" class="btn btn-primary" style="padding: 12px 30px;">🚀 Start Studying</button>
        </div>
      </div>

      <!-- Quick Study Options -->
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
        <button class="btn btn-secondary quick-study-btn" data-type="due" style="padding: 20px; text-align: center;">
          <div style="font-size: 1.5em; margin-bottom: 8px;">📅</div>
          <div style="font-weight: 600;">Review Due Cards</div>
          <div style="font-size: 0.9em; opacity: 0.8;"><span id="due-cards-count">0</span> cards</div>
        </button>

        <button class="btn btn-secondary quick-study-btn" data-type="new" style="padding: 20px; text-align: center;">
          <div style="font-size: 1.5em; margin-bottom: 8px;">✨</div>
          <div style="font-weight: 600;">Learn New Cards</div>
          <div style="font-size: 0.9em; opacity: 0.8;"><span id="new-cards-count">0</span> cards</div>
        </button>

        <button class="btn btn-secondary quick-study-btn" data-type="random" style="padding: 20px; text-align: center;">
          <div style="font-size: 1.5em; margin-bottom: 8px;">🎲</div>
          <div style="font-weight: 600;">Random Practice</div>
          <div style="font-size: 0.9em; opacity: 0.8;">Mixed cards</div>
        </button>
      </div>

      <!-- Study Session Interface -->
      <div id="study-session" style="display: none;">
        <div class="study-header" style="background: var(--card-bg); border-radius: 15px; padding: 20px; margin-bottom: 25px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <div>
              <h3 id="study-session-title" style="color: var(--primary-color); margin: 0;">Chunking Study Session</h3>
              <p id="study-progress" style="margin: 5px 0 0 0; color: var(--text-color); opacity: 0.8;">Card 1 of 7 in Chunk 1</p>
            </div>
            <div style="text-align: right;">
              <div id="study-stats" style="font-size: 0.9em; color: var(--text-color); opacity: 0.8;">
                <div>Correct: <span id="correct-count">0</span></div>
                <div>Incorrect: <span id="incorrect-count">0</span></div>
              </div>
              <button id="end-study-btn" class="btn btn-secondary" style="margin-top: 10px;">End Session</button>
            </div>
          </div>

          <!-- Progress Bars -->
          <div style="margin-bottom: 10px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
              <span style="font-size: 0.9em; color: var(--text-color); opacity: 0.8;">Current Chunk Progress</span>
              <span id="chunk-progress-text" style="font-size: 0.9em; color: var(--text-color); opacity: 0.8;">0/7</span>
            </div>
            <div style="background: var(--border-color); height: 8px; border-radius: 4px; overflow: hidden;">
              <div id="chunk-progress-bar" style="background: var(--primary-color); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
            </div>
          </div>

          <div>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
              <span style="font-size: 0.9em; color: var(--text-color); opacity: 0.8;">Overall Progress</span>
              <span id="overall-progress-text" style="font-size: 0.9em; color: var(--text-color); opacity: 0.8;">0/21</span>
            </div>
            <div style="background: var(--border-color); height: 8px; border-radius: 4px; overflow: hidden;">
              <div id="overall-progress-bar" style="background: var(--secondary-color); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
            </div>
          </div>
        </div>

        <div class="study-card" style="background: var(--card-bg); border-radius: 20px; padding: 40px; text-align: center; box-shadow: 0 8px 32px rgba(0,0,0,0.1); min-height: 400px; display: flex; flex-direction: column; justify-content: center;">
          <div id="study-card-content">
            <div id="card-side-indicator" style="font-size: 0.9em; color: var(--text-color); opacity: 0.6; margin-bottom: 20px;">Front</div>
            <div id="card-content" style="font-size: 1.3em; line-height: 1.6; margin-bottom: 30px; min-height: 100px; display: flex; align-items: center; justify-content: center;">
              Loading card...
            </div>
          </div>

          <div id="study-actions">
            <button id="flip-card-btn" class="btn btn-primary" style="margin-bottom: 20px; padding: 15px 30px; font-size: 1.1em;">🔄 Flip Card (Space)</button>

            <div id="difficulty-buttons" style="display: none;">
              <p style="margin-bottom: 15px; color: var(--text-color); opacity: 0.8;">How did you do?</p>
              <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                <button class="btn btn-danger" onclick="rateDifficulty(1)" style="flex: 1; min-width: 140px;">❌ Wrong (R)</button>
                <button class="btn" onclick="rateDifficulty(2)" style="flex: 1; min-width: 140px; background: #f59e0b; color: white;">⚠️ Not Confident (Y)</button>
                <button class="btn" onclick="rateDifficulty(3)" style="flex: 1; min-width: 140px; background: #10b981; color: white;">✅ Correct (G)</button>
              </div>
            </div>
          </div>
        </div>


      </div>

      <!-- Study Complete -->
      <div id="study-complete" style="display: none;">
        <div style="background: var(--card-bg); border-radius: 20px; padding: 40px; text-align: center; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
          <div style="font-size: 3em; margin-bottom: 20px;">🎉</div>
          <h2 style="color: var(--primary-color); margin-bottom: 20px;">Study Session Complete!</h2>
          <div id="session-summary" style="margin-bottom: 30px;">
            <p style="font-size: 1.1em; margin-bottom: 10px;">Great job! You've completed your study session.</p>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
              <div>
                <div style="font-size: 2em; color: #10b981;">✅</div>
                <div>Correct: <span id="final-correct">0</span></div>
              </div>
              <div>
                <div style="font-size: 2em; color: #ef4444;">❌</div>
                <div>Incorrect: <span id="final-incorrect">0</span></div>
              </div>
            </div>
          </div>
          <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
            <button id="study-again-btn" class="btn btn-primary">📚 Study Again</button>
            <button id="back-to-options-btn" class="btn btn-secondary">🏠 Back to Options</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Mock Exams Tab -->
    <div id="exams" class="tab-content">
      <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">🎯 Mock Exams</h2>

      <!-- Exam Setup View -->
      <div id="exam-setup-view">
        <div class="exam-setup-card" style="background: var(--card-bg); border-radius: 20px; padding: 30px; margin-bottom: 25px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
          <h3 style="color: var(--primary-color); margin-bottom: 20px;">📝 Create Mock Exam</h3>

          <div class="form-group" style="margin-bottom: 20px;">
            <label for="exam-name" style="display: block; margin-bottom: 8px; font-weight: 600;">Exam Name:</label>
            <input type="text" id="exam-name" class="form-control" placeholder="e.g., Biology Midterm Practice" style="width: 100%;">
          </div>

          <div class="form-group" style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 8px; font-weight: 600;">Select Decks:</label>
            <div id="exam-deck-selection" style="max-height: 200px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: 10px; padding: 15px;">
              <!-- Deck checkboxes will be populated here -->
            </div>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
            <div class="form-group">
              <label for="exam-question-count" style="display: block; margin-bottom: 8px; font-weight: 600;">Number of Questions:</label>
              <select id="exam-question-count" class="form-control">
                <option value="10">10 Questions</option>
                <option value="20" selected>20 Questions</option>
                <option value="30">30 Questions</option>
                <option value="50">50 Questions</option>
                <option value="all">All Available Cards</option>
              </select>
            </div>

            <div class="form-group">
              <label for="exam-time-limit" style="display: block; margin-bottom: 8px; font-weight: 600;">Time Limit:</label>
              <select id="exam-time-limit" class="form-control">
                <option value="0">No Time Limit</option>
                <option value="15">15 Minutes</option>
                <option value="30" selected>30 Minutes</option>
                <option value="45">45 Minutes</option>
                <option value="60">60 Minutes</option>
                <option value="90">90 Minutes</option>
              </select>
            </div>
          </div>

          <div class="form-group" style="margin-bottom: 25px;">
            <label style="display: block; margin-bottom: 8px; font-weight: 600;">Question Types:</label>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
              <label style="display: flex; align-items: center; gap: 8px;">
                <input type="checkbox" id="exam-type-flashcard" checked> Flashcard Style (Front → Back)
              </label>
              <label style="display: flex; align-items: center; gap: 8px;">
                <input type="checkbox" id="exam-type-reverse"> Reverse (Back → Front)
              </label>
            </div>
          </div>

          <button id="start-exam-btn" class="btn btn-primary" style="width: 100%; padding: 15px; font-size: 1.1em;">🚀 Start Mock Exam</button>
        </div>

        <!-- Previous Exam Results -->
        <div class="exam-history-card" style="background: var(--card-bg); border-radius: 20px; padding: 30px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
          <h3 style="color: var(--primary-color); margin-bottom: 20px;">📊 Recent Exam Results</h3>
          <div id="exam-history-list">
            <p style="text-align: center; color: var(--text-color); opacity: 0.7;">No exams taken yet. Create your first mock exam above!</p>
          </div>
        </div>
      </div>

      <!-- Exam Taking View -->
      <div id="exam-taking-view" style="display: none;">
        <div class="exam-header" style="background: var(--card-bg); border-radius: 15px; padding: 20px; margin-bottom: 25px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
          <div>
            <h3 id="current-exam-name" style="color: var(--primary-color); margin: 0;">Mock Exam</h3>
            <p id="exam-progress" style="margin: 5px 0 0 0; color: var(--text-color); opacity: 0.8;">Question 1 of 20</p>
          </div>
          <div style="text-align: right;">
            <div id="exam-timer" style="font-size: 1.5em; font-weight: 600; color: var(--primary-color);">30:00</div>
            <button id="end-exam-btn" class="btn btn-secondary" style="margin-top: 10px;">End Exam</button>
          </div>
        </div>

        <div class="exam-question-card" style="background: var(--card-bg); border-radius: 20px; padding: 40px; text-align: center; box-shadow: 0 8px 32px rgba(0,0,0,0.1); min-height: 300px; display: flex; flex-direction: column; justify-content: center;">
          <div id="exam-question-content">
            <h4 style="color: var(--text-color); margin-bottom: 30px;">Loading question...</h4>
          </div>

          <div id="exam-answer-section" style="margin-top: 30px;">
            <textarea id="exam-answer-input" class="form-control" rows="4" placeholder="Type your answer here..." style="margin-bottom: 20px; font-size: 1.1em;"></textarea>
            <div style="display: flex; gap: 15px; justify-content: center;">
              <button id="exam-show-answer-btn" class="btn btn-secondary">Show Answer</button>
              <button id="exam-next-question-btn" class="btn btn-primary" style="display: none;">Next Question</button>
            </div>
          </div>
        </div>

        <div id="exam-answer-reveal" style="display: none; background: var(--card-bg); border-radius: 15px; padding: 25px; margin-top: 20px; border-left: 4px solid var(--primary-color);">
          <h5 style="color: var(--primary-color); margin-bottom: 15px;">Correct Answer:</h5>
          <div id="exam-correct-answer" style="margin-bottom: 20px; font-size: 1.1em;"></div>
          <div style="display: flex; gap: 15px; justify-content: center;">
            <button id="exam-mark-correct-btn" class="btn btn-primary" style="background: #10b981;">✅ I Got It Right</button>
            <button id="exam-mark-incorrect-btn" class="btn btn-danger">❌ I Got It Wrong</button>
          </div>
        </div>
      </div>

      <!-- Exam Results View -->
      <div id="exam-results-view" style="display: none;">
        <div class="exam-results-header" style="background: var(--card-bg); border-radius: 20px; padding: 30px; margin-bottom: 25px; text-align: center; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
          <h2 id="exam-final-score" style="color: var(--primary-color); margin-bottom: 15px;">85%</h2>
          <p id="exam-grade-text" style="font-size: 1.2em; margin-bottom: 10px;">Great Job!</p>
          <p id="exam-completion-time" style="color: var(--text-color); opacity: 0.8;">Completed in 25 minutes</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px; margin-bottom: 25px;">
          <div class="results-card" style="background: var(--card-bg); border-radius: 15px; padding: 25px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
            <h4 style="color: var(--primary-color); margin-bottom: 15px;">📊 Performance Breakdown</h4>
            <div id="exam-performance-stats"></div>
          </div>

          <div class="results-card" style="background: var(--card-bg); border-radius: 15px; padding: 25px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
            <h4 style="color: var(--primary-color); margin-bottom: 15px;">🎯 Areas to Focus</h4>
            <div id="exam-focus-areas"></div>
          </div>
        </div>

        <div class="results-actions" style="background: var(--card-bg); border-radius: 15px; padding: 25px; text-align: center; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
          <h4 style="color: var(--primary-color); margin-bottom: 20px;">📚 Recommended Actions</h4>
          <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
            <button id="create-focus-deck-btn" class="btn btn-primary">📝 Create Focus Deck</button>
            <button id="review-mistakes-btn" class="btn btn-secondary">🔍 Review Mistakes</button>
            <button id="retake-exam-btn" class="btn" style="background: var(--accent-color); color: white;">🔄 Retake Exam</button>
            <button id="new-exam-btn" class="btn btn-secondary">➕ New Exam</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Analytics Tab -->
    <div id="analytics" class="tab-content">
      <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">📊 Analytics & Progress</h2>

      <!-- Overview Cards -->
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
        <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); text-align: center;">
          <div style="font-size: 2.5em; color: var(--primary-color); margin-bottom: 10px;">📚</div>
          <div style="font-size: 2em; font-weight: 600; color: var(--text-color); margin-bottom: 5px;" id="total-decks">0</div>
          <div style="color: var(--text-color); opacity: 0.7;">Total Decks</div>
        </div>

        <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); text-align: center;">
          <div style="font-size: 2.5em; color: var(--secondary-color); margin-bottom: 10px;">🃏</div>
          <div style="font-size: 2em; font-weight: 600; color: var(--text-color); margin-bottom: 5px;" id="total-cards">0</div>
          <div style="color: var(--text-color); opacity: 0.7;">Total Cards</div>
        </div>

        <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); text-align: center;">
          <div style="font-size: 2.5em; color: var(--accent-color); margin-bottom: 10px;">🎯</div>
          <div style="font-size: 2em; font-weight: 600; color: var(--text-color); margin-bottom: 5px;" id="avg-confidence">0%</div>
          <div style="color: var(--text-color); opacity: 0.7;">Average Confidence</div>
        </div>

        <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); text-align: center;">
          <div style="font-size: 2.5em; color: var(--warning-color); margin-bottom: 10px;">🏆</div>
          <div style="font-size: 2em; font-weight: 600; color: var(--text-color); margin-bottom: 5px;" id="study-streak">0</div>
          <div style="color: var(--text-color); opacity: 0.7;">Study Streak (Days)</div>
        </div>
      </div>

      <!-- Deck Performance -->
      <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); margin-bottom: 25px;">
        <h3 style="color: var(--primary-color); margin-bottom: 20px;">📈 Deck Performance</h3>
        <div id="deck-performance-list">
          <p style="text-align: center; color: var(--text-color); opacity: 0.7;">No study data available yet. Start studying to see your progress!</p>
        </div>
      </div>

      <!-- Recent Activity -->
      <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
        <h3 style="color: var(--primary-color); margin-bottom: 20px;">📅 Recent Activity</h3>
        <div id="recent-activity-list">
          <p style="text-align: center; color: var(--text-color); opacity: 0.7;">No recent activity. Start studying to track your progress!</p>
        </div>
      </div>
    </div>

    <!-- Import Tab -->
    <div id="import" class="tab-content">
      <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">📥 Import Cards</h2>

      <!-- Import Options -->
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; margin-bottom: 30px;">

        <!-- CSV Import -->
        <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
          <h3 style="color: var(--primary-color); margin-bottom: 15px;">📄 CSV Import</h3>
          <p style="margin-bottom: 20px; color: var(--text-color); opacity: 0.8;">Import cards from a CSV file with columns: Front, Back, Tags (optional)</p>

          <div class="form-group">
            <label for="csv-deck-select">Import to Deck:</label>
            <select id="csv-deck-select" class="form-control">
              <option value="">Select a deck...</option>
            </select>
          </div>

          <div class="form-group">
            <label for="csv-file-input">Choose CSV File:</label>
            <input type="file" id="csv-file-input" accept=".csv" class="form-control">
          </div>

          <button id="import-csv-btn" class="btn btn-primary" style="width: 100%;">📥 Import CSV</button>

          <div style="margin-top: 15px; padding: 15px; background: var(--bg-color); border-radius: 8px; font-size: 0.9em;">
            <strong>CSV Format:</strong><br>
            Front,Back,Tags<br>
            "What is photosynthesis?","Process by which plants make food","biology,plants"
          </div>
        </div>

        <!-- Text Import -->
        <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
          <h3 style="color: var(--secondary-color); margin-bottom: 15px;">📝 Text Import</h3>
          <p style="margin-bottom: 20px; color: var(--text-color); opacity: 0.8;">Paste text with questions and answers separated by a delimiter</p>

          <div class="form-group">
            <label for="text-deck-select">Import to Deck:</label>
            <select id="text-deck-select" class="form-control">
              <option value="">Select a deck...</option>
            </select>
          </div>

          <div class="form-group">
            <label for="text-delimiter">Delimiter:</label>
            <select id="text-delimiter" class="form-control">
              <option value="|">Pipe (|)</option>
              <option value=";">Semicolon (;)</option>
              <option value="\t">Tab</option>
              <option value="::">Double Colon (::)</option>
            </select>
          </div>

          <div class="form-group">
            <label for="import-text-area">Paste Your Text:</label>
            <textarea id="import-text-area" class="form-control" rows="8" placeholder="Question 1|Answer 1&#10;Question 2|Answer 2&#10;Question 3|Answer 3"></textarea>
          </div>

          <button id="import-text-btn" class="btn btn-primary" style="width: 100%;">📥 Import Text</button>
        </div>

        <!-- Anki Import -->
        <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
          <h3 style="color: var(--accent-color); margin-bottom: 15px;">🎴 Anki Import</h3>
          <p style="margin-bottom: 20px; color: var(--text-color); opacity: 0.8;">Import cards from Anki export files (.txt format)</p>

          <div class="form-group">
            <label for="anki-deck-select">Import to Deck:</label>
            <select id="anki-deck-select" class="form-control">
              <option value="">Select a deck...</option>
            </select>
          </div>

          <div class="form-group">
            <label for="anki-file-input">Choose Anki Export File:</label>
            <input type="file" id="anki-file-input" accept=".txt" class="form-control">
          </div>

          <button id="import-anki-btn" class="btn btn-primary" style="width: 100%;">📥 Import Anki</button>

          <div style="margin-top: 15px; padding: 15px; background: var(--bg-color); border-radius: 8px; font-size: 0.9em;">
            <strong>How to export from Anki:</strong><br>
            1. Open Anki<br>
            2. File → Export<br>
            3. Choose "Notes in Plain Text"<br>
            4. Select your deck<br>
            5. Export and upload here
          </div>
        </div>
      </div>

      <!-- Import History -->
      <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
        <h3 style="color: var(--primary-color); margin-bottom: 20px;">📋 Import History</h3>
        <div id="import-history-list">
          <p style="text-align: center; color: var(--text-color); opacity: 0.7;">No imports yet. Import some cards to see your history!</p>
        </div>
      </div>
    </div>

    <!-- Manage Cards Tab -->
    <div id="manage" class="tab-content">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
        <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">🗂️ Manage Cards</h2>
        <button id="add-card-btn" class="btn btn-primary">✨ Add Card</button>
      </div>

      <!-- Filters -->
      <div style="background: var(--card-bg); padding: 20px; border-radius: 15px; margin-bottom: 25px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; align-items: end;">
          <div class="form-group">
            <label for="filter-deck" style="display: block; margin-bottom: 8px; font-weight: 600;">Filter by Deck:</label>
            <select id="filter-deck" class="form-control">
              <option value="">All Decks</option>
            </select>
          </div>
          <div class="form-group">
            <label for="search-cards" style="display: block; margin-bottom: 8px; font-weight: 600;">Search Cards:</label>
            <input type="text" id="search-cards" class="form-control" placeholder="Search front or back content...">
          </div>
          <div class="form-group">
            <button id="clear-filters-btn" class="btn btn-secondary" style="width: 100%;">Clear Filters</button>
          </div>
        </div>
      </div>

      <!-- Cards List -->
      <div id="cards-container">
        <div id="cards-list" class="deck-grid">
          <p style="text-align: center; color: var(--text-color); opacity: 0.7; grid-column: 1 / -1;">No cards found. Add some cards to get started!</p>
        </div>
      </div>
    </div>

    <!-- Settings Tab -->
    <div id="settings" class="tab-content">
      <h2 style="background: var(--gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 25px;">⚙️ Settings</h2>

      <!-- App Settings -->
      <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); margin-bottom: 25px;">
        <h3 style="color: var(--primary-color); margin-bottom: 20px;">🎨 Appearance</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
          <div class="form-group">
            <label for="theme-select">Theme:</label>
            <select id="theme-select" class="form-control">
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="auto">Auto (System)</option>
            </select>
          </div>

          <div class="form-group">
            <label for="accent-color-select">Accent Color:</label>
            <select id="accent-color-select" class="form-control">
              <option value="teal">Teal (Default)</option>
              <option value="blue">Blue</option>
              <option value="purple">Purple</option>
              <option value="pink">Pink</option>
              <option value="green">Green</option>
              <option value="orange">Orange</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Study Settings -->
      <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); margin-bottom: 25px;">
        <h3 style="color: var(--primary-color); margin-bottom: 20px;">📚 Study Preferences</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
          <div class="form-group">
            <label for="new-cards-per-day">New Cards Per Day:</label>
            <input type="number" id="new-cards-per-day" class="form-control" value="20" min="1" max="100">
            <small style="color: var(--text-color); opacity: 0.7;">Maximum new cards to learn each day</small>
          </div>

          <div class="form-group">
            <label for="review-cards-per-day">Review Cards Per Day:</label>
            <input type="number" id="review-cards-per-day" class="form-control" value="100" min="1" max="500">
            <small style="color: var(--text-color); opacity: 0.7;">Maximum cards to review each day</small>
          </div>

          <div class="form-group">
            <label for="graduation-interval">Initial Review Interval:</label>
            <input type="number" id="graduation-interval" class="form-control" value="1" min="1" max="30">
            <small style="color: var(--text-color); opacity: 0.7;">Days before first review of new cards</small>
          </div>

          <div class="form-group">
            <label for="easy-interval">Easy Card Interval:</label>
            <input type="number" id="easy-interval" class="form-control" value="7" min="1" max="30">
            <small style="color: var(--text-color); opacity: 0.7;">Days before reviewing "correct" cards again</small>
          </div>
        </div>

        <div style="margin-top: 20px;">
          <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
            <input type="checkbox" id="auto-advance-setting" checked>
            <span>Auto-advance to next card after rating</span>
          </label>
        </div>

        <div style="margin-top: 15px;">
          <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
            <input type="checkbox" id="show-answer-timer-setting">
            <span>Show timer for answer reveal</span>
          </label>
        </div>
      </div>

      <!-- Notifications -->
      <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); margin-bottom: 25px;">
        <h3 style="color: var(--primary-color); margin-bottom: 20px;">🔔 Notifications</h3>

        <div class="form-group" style="margin-bottom: 20px;">
          <label for="notification-email">Email for Notifications:</label>
          <input type="email" id="notification-email" class="form-control" placeholder="<EMAIL>">
          <small style="color: var(--text-color); opacity: 0.7;">We'll send study reminders and progress updates to this email</small>
        </div>

        <div style="margin-bottom: 15px;">
          <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
            <input type="checkbox" id="daily-reminder-setting" checked>
            <span>Daily study reminder emails</span>
          </label>
        </div>

        <div style="margin-bottom: 15px;">
          <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
            <input type="checkbox" id="weekly-progress-setting" checked>
            <span>Weekly progress reports</span>
          </label>
        </div>

        <div style="margin-bottom: 15px;">
          <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
            <input type="checkbox" id="achievement-notifications-setting" checked>
            <span>Achievement notifications</span>
          </label>
        </div>

        <div class="form-group" style="margin-top: 20px;">
          <label for="reminder-time">Daily Reminder Time:</label>
          <input type="time" id="reminder-time" class="form-control" value="09:00">
        </div>

        <button id="save-notification-settings-btn" class="btn btn-primary" style="margin-top: 15px;">💾 Save Notification Settings</button>
      </div>

      <!-- Data Management -->
      <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); margin-bottom: 25px;">
        <h3 style="color: var(--primary-color); margin-bottom: 20px;">💾 Data Management</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
          <button id="export-data-btn" class="btn btn-secondary">📤 Export All Data</button>
          <button id="backup-data-btn" class="btn btn-secondary">💾 Create Backup</button>
          <button id="sync-data-btn" class="btn btn-primary">🔄 Sync Data</button>
          <button id="clear-cache-btn" class="btn btn-secondary">🗑️ Clear Cache</button>
        </div>

        <div style="margin-top: 20px; padding: 15px; background: var(--danger-bg); border-radius: 8px; border-left: 4px solid var(--danger-color);">
          <h4 style="color: var(--danger-color); margin-bottom: 10px;">⚠️ Danger Zone</h4>
          <p style="margin-bottom: 15px; color: var(--text-color); opacity: 0.8;">These actions cannot be undone. Please be careful.</p>
          <button id="reset-progress-btn" class="btn btn-danger" style="margin-right: 10px;">🔄 Reset All Progress</button>
          <button id="delete-all-data-btn" class="btn btn-danger">🗑️ Delete All Data</button>
        </div>
      </div>

      <!-- Account Settings -->
      <div style="background: var(--card-bg); padding: 25px; border-radius: 15px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
        <h3 style="color: var(--primary-color); margin-bottom: 20px;">👤 Account</h3>

        <div style="margin-bottom: 20px;">
          <strong>Email:</strong> <span id="user-email">Loading...</span>
        </div>

        <div style="margin-bottom: 20px;">
          <strong>Account Type:</strong> <span id="account-type">Loading...</span>
        </div>

        <div style="margin-bottom: 20px;">
          <strong>Member Since:</strong> <span id="member-since">Loading...</span>
        </div>

        <div style="display: flex; gap: 15px; flex-wrap: wrap;">
          <button id="change-password-btn" class="btn btn-secondary">🔒 Change Password</button>
          <button id="download-data-btn" class="btn btn-secondary">📥 Download My Data</button>
          <button id="delete-account-btn" class="btn btn-danger">❌ Delete Account</button>
        </div>
      </div>
    </div>

    <!-- Change Password Modal -->
    <div id="change-password-modal" class="modal">
      <div class="modal-content" style="max-width: 400px;">
        <span class="close" id="close-password-modal">&times;</span>
        <h3 style="color: var(--primary-color); margin-bottom: 25px;">🔒 Change Password</h3>

        <div class="form-group">
          <label for="current-password">Current Password:</label>
          <input type="password" id="current-password" class="form-control" required>
        </div>

        <div class="form-group">
          <label for="new-password">New Password:</label>
          <input type="password" id="new-password" class="form-control" required>
        </div>

        <div class="form-group">
          <label for="confirm-new-password">Confirm New Password:</label>
          <input type="password" id="confirm-new-password" class="form-control" required>
        </div>

        <div style="display: flex; gap: 15px; margin-top: 30px;">
          <button id="update-password-btn" class="btn btn-primary" style="flex: 1;">Update Password</button>
          <button id="cancel-password-btn" class="btn btn-secondary" style="flex: 1;">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create Deck Modal -->
<div id="create-deck-modal" class="modal">
  <div class="modal-content">
    <span class="close" id="close-modal">&times;</span>
    <h3 style="color: var(--primary-color); margin-bottom: 25px;">✨ Create New Deck</h3>
    <div class="form-group">
      <label for="deck-name">Deck Name:</label>
      <input type="text" id="deck-name" class="form-control" placeholder="e.g., Biology Chapter 5">
    </div>
    <div class="form-group">
      <label for="deck-description">Description (optional):</label>
      <textarea id="deck-description" class="form-control" rows="3" placeholder="Brief description of the deck content"></textarea>
    </div>
    <button id="create-deck-confirm-btn" class="btn btn-primary" style="width: 100%;">🚀 Create Deck</button>
  </div>
</div>

<!-- Real-Time Card Editor Modal -->
<div id="card-editor-modal" class="modal">
  <div class="modal-content" style="max-width: 800px;">
    <span class="close" id="close-card-modal">&times;</span>
    <h3 id="card-modal-title" style="color: var(--primary-color); margin-bottom: 20px;">✏️ Create New Flashcard</h3>

    <!-- Action Buttons at Top -->
    <div style="display: flex; gap: 15px; margin-bottom: 25px;">
      <button id="save-card-btn" class="btn btn-primary" style="flex: 1;">💾 Save Card</button>
      <button id="cancel-card-btn" class="btn btn-secondary" style="flex: 1;">❌ Cancel</button>
    </div>

    <!-- Card Side Toggle -->
    <div style="text-align: center; margin-bottom: 20px;">
      <button id="edit-front-btn" class="btn btn-primary" style="margin-right: 10px;">Edit Front</button>
      <button id="edit-back-btn" class="btn btn-secondary">Edit Back</button>
    </div>

    <!-- Live Card Preview/Editor -->
    <div id="card-editor" class="flashcard" style="min-height: 300px; cursor: text; position: relative; background: var(--card-background, var(--card-bg)); color: var(--card-text-color, var(--text-color)); font-family: var(--card-font, inherit); font-size: var(--card-font-size, 1.3em);">
      <div class="confidence-indicator confidence-low"></div>
      <div id="card-edit-content" style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;">
        <div contenteditable="true" id="card-text-editor" style="width: 90%; min-height: 100px; outline: none; text-align: center; border: 2px dashed transparent; padding: 10px; border-radius: 8px;" placeholder="Click here to add text..."></div>
        <div id="card-media-container" style="margin-top: 15px;">
          <!-- Media will be added here -->
        </div>
        <div style="margin-top: 15px;">
          <input type="file" id="card-media-input" accept="image/*,video/*,audio/*" style="display: none;">
          <button type="button" id="add-media-btn" class="btn btn-secondary" style="font-size: 0.9em; padding: 8px 16px;">📎 Add Media</button>
        </div>
      </div>
    </div>

    <!-- Card Styling Controls -->
    <div style="background: var(--bg-color); padding: 20px; border-radius: 15px; margin: 20px 0;">
      <h4 style="color: var(--primary-color); margin-bottom: 15px;">🎨 Card Styling</h4>
      <div style="display: grid; grid-template-columns: 2fr 1fr 60px 60px; gap: 15px; align-items: end;">
        <div class="form-group">
          <label for="card-font-select">Font:</label>
          <select id="card-font-select" class="form-control">
            <option value="inherit">Default</option>
            <option value="'Avenir', 'Helvetica', sans-serif">Avenir</option>
            <option value="'Arial', sans-serif">Arial</option>
            <option value="'Helvetica', sans-serif">Helvetica</option>
            <option value="'Georgia', serif">Georgia</option>
            <option value="'Times New Roman', serif">Times New Roman</option>
            <option value="'Palatino', serif">Palatino</option>
            <option value="'Garamond', serif">Garamond</option>
            <option value="'Comic Sans MS', cursive">Comic Sans</option>
            <option value="'Courier New', monospace">Courier New</option>
            <option value="'Monaco', monospace">Monaco</option>
            <option value="'Verdana', sans-serif">Verdana</option>
            <option value="'Trebuchet MS', sans-serif">Trebuchet MS</option>
            <option value="'Impact', sans-serif">Impact</option>
            <option value="'Brush Script MT', cursive">Brush Script</option>
            <option value="'Papyrus', fantasy">Papyrus</option>
          </select>
        </div>
        <div class="form-group">
          <label for="card-font-size-select">Size:</label>
          <select id="card-font-size-select" class="form-control">
            <option value="1em">Small</option>
            <option value="1.3em" selected>Medium</option>
            <option value="1.6em">Large</option>
            <option value="2em">XL</option>
          </select>
        </div>
        <div class="form-group">
          <label for="card-bg-color-picker">BG:</label>
          <input type="color" id="card-bg-color-picker" class="form-control color-picker-circle" value="#ffffff">
        </div>
        <div class="form-group">
          <label for="card-text-color-picker">Text:</label>
          <input type="color" id="card-text-color-picker" class="form-control color-picker-circle" value="#1e293b">
        </div>
      </div>

      <!-- Quick Color Presets -->
      <div style="margin-top: 15px;">
        <label style="display: block; margin-bottom: 8px; font-weight: 600;">Quick Presets:</label>
        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
          <button class="color-preset" data-bg="#ffffff" data-text="#1e293b" style="background: #ffffff; color: #1e293b; border: 2px solid #e2e8f0; padding: 6px 10px; border-radius: 6px; cursor: pointer; font-size: 0.85em;">Default</button>
          <button class="color-preset" data-bg="#fce7f3" data-text="#be185d" style="background: #fce7f3; color: #be185d; border: 2px solid #f9a8d4; padding: 6px 10px; border-radius: 6px; cursor: pointer; font-size: 0.85em;">Pink</button>
          <button class="color-preset" data-bg="#dbeafe" data-text="#1e40af" style="background: #dbeafe; color: #1e40af; border: 2px solid #93c5fd; padding: 6px 10px; border-radius: 6px; cursor: pointer; font-size: 0.85em;">Blue</button>
          <button class="color-preset" data-bg="#dcfce7" data-text="#166534" style="background: #dcfce7; color: #166534; border: 2px solid #86efac; padding: 6px 10px; border-radius: 6px; cursor: pointer; font-size: 0.85em;">Green</button>
          <button class="color-preset" data-bg="#fef3c7" data-text="#92400e" style="background: #fef3c7; color: #92400e; border: 2px solid #fcd34d; padding: 6px 10px; border-radius: 6px; cursor: pointer; font-size: 0.85em;">Yellow</button>
          <button class="color-preset" data-bg="#1f2937" data-text="#f9fafb" style="background: #1f2937; color: #f9fafb; border: 2px solid #4b5563; padding: 6px 10px; border-radius: 6px; cursor: pointer; font-size: 0.85em;">Dark</button>
        </div>
      </div>
    </div>

    <!-- Tags -->
    <div class="form-group">
      <label for="card-tags">Tags (optional):</label>
      <input type="text" id="card-tags" class="form-control" placeholder="e.g., biology, cell-structure, important (comma-separated)">
      <small style="color: var(--text-color); opacity: 0.7;">Tags help organize and filter your cards</small>
    </div>


  </div>
</div>

<!-- Firebase SDK -->
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>

<script>
// Firebase Configuration - Chunking Flashcards Project
const firebaseConfig = {
  apiKey: "AIzaSyDsIw2-nh1JrRI10e4AfMnd4dAWdQ33K1M",
  authDomain: "chunking-flashcards.firebaseapp.com",
  projectId: "chunking-flashcards",
  storageBucket: "chunking-flashcards.firebasestorage.app",
  messagingSenderId: "949621433092",
  appId: "1:949621433092:web:0fc00e67a712cad251a651",
  measurementId: "G-R68E9TNGLH"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const db = firebase.firestore();

// App State
const app = {
  currentUser: null,
  decks: [],
  cards: []
};

// Authentication Functions
function showAuthScreen() {
  document.getElementById('auth-screen').style.display = 'flex';
  document.getElementById('main-app').style.display = 'none';
}

function showMainApp() {
  document.getElementById('auth-screen').style.display = 'none';
  document.getElementById('main-app').style.display = 'block';
  document.getElementById('user-name').textContent = app.currentUser.displayName || app.currentUser.email || 'User';
  loadUserData();
}

function showAuthError(message) {
  const errorDiv = document.getElementById('auth-error');
  errorDiv.textContent = message;
  errorDiv.style.display = 'block';
  setTimeout(() => {
    errorDiv.style.display = 'none';
  }, 5000);
}

// Firebase Auth State Listener
auth.onAuthStateChanged(user => {
  if (user) {
    console.log('User signed in:', user.email);
    app.currentUser = user;
    showMainApp();
  } else {
    console.log('No user signed in');
    app.currentUser = null;
    showAuthScreen();
  }
});

// Authentication Event Handlers
document.getElementById('login-form').addEventListener('submit', async (e) => {
  e.preventDefault();
  const email = document.getElementById('login-email').value;
  const password = document.getElementById('login-password').value;

  try {
    await auth.signInWithEmailAndPassword(email, password);
  } catch (error) {
    console.error('Login error:', error);
    showAuthError(getAuthErrorMessage(error.code));
  }
});

document.getElementById('signup-form').addEventListener('submit', async (e) => {
  e.preventDefault();
  const name = document.getElementById('signup-name').value;
  const email = document.getElementById('signup-email').value;
  const password = document.getElementById('signup-password').value;
  const confirmPassword = document.getElementById('signup-confirm').value;

  if (password !== confirmPassword) {
    showAuthError('Passwords do not match');
    return;
  }

  if (password.length < 6) {
    showAuthError('Password must be at least 6 characters');
    return;
  }

  try {
    const userCredential = await auth.createUserWithEmailAndPassword(email, password);
    await userCredential.user.updateProfile({ displayName: name });

    // Create user document in your dedicated flashcards project
    await db.collection('users').doc(userCredential.user.uid).set({
      name: name,
      email: email,
      createdAt: firebase.firestore.FieldValue.serverTimestamp(),
      settings: {
        theme: 'light',
        notifications: true
      }
    });
  } catch (error) {
    console.error('Signup error:', error);
    showAuthError(getAuthErrorMessage(error.code));
  }
});

document.getElementById('guest-btn').addEventListener('click', () => {
  app.currentUser = { uid: 'guest', displayName: 'Guest User', email: 'guest@local' };
  showMainApp();
});

document.getElementById('sign-out-btn').addEventListener('click', async () => {
  try {
    if (app.currentUser.uid !== 'guest') {
      await auth.signOut();
    } else {
      app.currentUser = null;
      showAuthScreen();
    }
  } catch (error) {
    console.error('Sign out error:', error);
  }
});

// Auth Tab Switching
document.getElementById('login-tab').addEventListener('click', () => {
  document.getElementById('login-tab').classList.add('active');
  document.getElementById('signup-tab').classList.remove('active');
  document.getElementById('login-form').classList.add('active');
  document.getElementById('signup-form').classList.remove('active');
});

document.getElementById('signup-tab').addEventListener('click', () => {
  document.getElementById('signup-tab').classList.add('active');
  document.getElementById('login-tab').classList.remove('active');
  document.getElementById('signup-form').classList.add('active');
  document.getElementById('login-form').classList.remove('active');
});

document.getElementById('forgot-password-btn').addEventListener('click', async () => {
  const email = prompt('Enter your email address to reset your password:');
  if (!email) return;

  try {
    await auth.sendPasswordResetEmail(email);
    alert('Password reset email sent! Check your inbox.');
  } catch (error) {
    console.error('Password reset error:', error);
    alert('Error sending password reset email. Please check your email address.');
  }
});

function getAuthErrorMessage(errorCode) {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'No account found with this email. Please sign up first.';
    case 'auth/wrong-password':
    case 'auth/invalid-login-credentials':
      return 'Invalid email or password. Please check your credentials.';
    case 'auth/email-already-in-use':
      return 'An account with this email already exists. Please sign in instead.';
    case 'auth/weak-password':
      return 'Password is too weak. Please choose a stronger password.';
    case 'auth/invalid-email':
      return 'Invalid email address. Please check your email.';
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later.';
    case 'auth/network-request-failed':
      return 'Network error. Please check your connection.';
    default:
      return 'Authentication failed. Please try again.';
  }
}

// Data Loading Functions
async function loadUserData() {
  console.log('Loading user data for:', app.currentUser.uid);

  if (app.currentUser.uid === 'guest') {
    loadLocalData();
  } else {
    await loadFirebaseData();
  }

  console.log('Final data loaded - Decks:', app.decks.length, 'Cards:', app.cards.length);
  renderDecks();
}

function loadLocalData() {
  // Use the same localStorage keys as your existing app
  app.decks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
  app.cards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
  console.log('Loaded from localStorage - Decks:', app.decks.length, 'Cards:', app.cards.length);
}

async function loadFirebaseData() {
  try {
    console.log('Loading Firebase data for user:', app.currentUser.uid);

    // Load decks from your dedicated flashcards Firebase project
    try {
      const decksSnapshot = await db.collection('decks')
        .where('userId', '==', app.currentUser.uid)
        .get();

      app.decks = [];
      decksSnapshot.forEach(doc => {
        app.decks.push({ id: doc.id, ...doc.data() });
      });
      console.log('Loaded decks:', app.decks.length);
    } catch (deckError) {
      console.warn('Error loading decks, trying fallback:', deckError);
      // Fallback to localStorage if Firestore fails
      app.decks = JSON.parse(localStorage.getItem('chunking_decks') || '[]')
        .filter(deck => deck.userId === app.currentUser.uid);
    }

    // Try to load cards
    try {
      const cardsSnapshot = await db.collection('cards')
        .where('userId', '==', app.currentUser.uid)
        .get();

      app.cards = [];
      cardsSnapshot.forEach(doc => {
        app.cards.push({ id: doc.id, ...doc.data() });
      });
      console.log('Loaded cards:', app.cards.length);
    } catch (cardError) {
      console.warn('Error loading cards, trying fallback:', cardError);
      // Fallback to localStorage if Firestore fails
      app.cards = JSON.parse(localStorage.getItem('chunking_cards') || '[]')
        .filter(card => card.userId === app.currentUser.uid);
    }
  } catch (error) {
    console.error('Error loading data:', error);
    // Complete fallback to localStorage
    loadLocalData();
  }
}

// Deck Management
function renderDecks() {
  const grid = document.getElementById('deck-grid');

  if (app.decks.length === 0) {
    grid.innerHTML = '<p style="text-align: center; color: var(--text-color); opacity: 0.7; grid-column: 1 / -1;">No decks yet. Create your first deck to get started!</p>';
    return;
  }

  grid.innerHTML = app.decks.map(deck => {
    const cardCount = app.cards.filter(card => card.deckId === deck.id).length;
    return `
      <div class="deck-card" data-deck-id="${deck.id}">
        <button class="delete-btn" data-deck-id="${deck.id}" title="Delete deck">✕</button>
        <h3 style="color: var(--primary-color); margin-bottom: 10px; padding-right: 40px;">${deck.name}</h3>
        <p style="color: var(--text-color); margin-bottom: 20px; opacity: 0.8; min-height: 40px;">${deck.description || 'No description'}</p>
        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.9em; color: var(--text-color); opacity: 0.7;">
          <span>📚 ${cardCount} cards</span>
          <span>📅 Created ${new Date(deck.created?.toDate?.() || deck.created).toLocaleDateString()}</span>
        </div>
      </div>
    `;
  }).join('');

  // Add event listeners after rendering
  grid.querySelectorAll('.deck-card').forEach(card => {
    const deckId = card.dataset.deckId;
    card.addEventListener('click', () => openDeckDetail(deckId));
  });

  grid.querySelectorAll('.delete-btn').forEach(btn => {
    const deckId = btn.dataset.deckId;
    btn.addEventListener('click', (e) => {
      e.stopPropagation();
      deleteDeck(deckId);
    });
  });
}

// Deck Detail View Functions
let currentDeck = null;

function openDeckDetail(deckId) {
  currentDeck = app.decks.find(d => d.id === deckId);
  if (!currentDeck) return;

  document.getElementById('deck-list-view').style.display = 'none';
  document.getElementById('deck-detail-view').style.display = 'block';
  document.getElementById('deck-title').textContent = `📚 ${currentDeck.name}`;

  renderDeckCards();
}

function closeDeckDetail() {
  document.getElementById('deck-detail-view').style.display = 'none';
  document.getElementById('deck-list-view').style.display = 'block';
  currentDeck = null;
}

function renderDeckCards() {
  const grid = document.getElementById('deck-cards-grid');
  const deckCards = app.cards.filter(card => card.deckId === currentDeck.id);

  if (deckCards.length === 0) {
    grid.innerHTML = '<p style="text-align: center; color: var(--text-color); opacity: 0.7; grid-column: 1 / -1;">No cards in this deck yet. Add your first card!</p>';
    return;
  }

  grid.innerHTML = deckCards.map(card => {
    const confidenceClass = card.confidence >= 70 ? 'confidence-high' :
                           card.confidence >= 40 ? 'confidence-medium' : 'confidence-low';

    return `
      <div class="deck-card-mini" data-card-id="${card.id}" style="background: ${card.backgroundColor || 'var(--card-bg)'}; color: ${card.textColor || 'var(--text-color)'}; font-family: ${card.fontFamily || 'inherit'}; font-size: ${card.fontSize || '1.1em'};">
        <div class="confidence-indicator ${confidenceClass}"></div>
        <button class="delete-btn" data-card-id="${card.id}" title="Delete card">✕</button>

        <div class="card-content">
          <div style="font-weight: 600; margin-bottom: 10px; opacity: 0.8; font-size: 0.9em;">FRONT</div>
          <div style="margin-bottom: 15px; min-height: 60px;">${card.front}</div>
          ${card.frontMedia ? `<div class="media-preview">${renderCardMedia(card.frontMedia)}</div>` : ''}
        </div>

        <div class="card-actions">
          <div style="font-size: 0.8em; color: var(--text-color); opacity: 0.6;">
            Confidence: ${card.confidence || 0}%
          </div>
          <div style="font-size: 0.7em; color: var(--text-color); opacity: 0.5; font-style: italic;">
            Click to edit
          </div>
        </div>
      </div>
    `;
  }).join('');

  // Add event listeners after rendering
  grid.querySelectorAll('.deck-card-mini').forEach(cardEl => {
    const cardId = cardEl.dataset.cardId;
    cardEl.addEventListener('click', () => editCard(cardId));
  });

  grid.querySelectorAll('.delete-btn').forEach(btn => {
    const cardId = btn.dataset.cardId;
    btn.addEventListener('click', (e) => {
      e.stopPropagation();
      deleteCard(cardId);
    });
  });


}

function renderCardMedia(media) {
  if (!media) return '';

  // Handle both object and JSON string formats
  let mediaObj = media;
  if (typeof media === 'string') {
    try {
      mediaObj = JSON.parse(media);
    } catch (e) {
      return '';
    }
  }

  if (mediaObj.type === 'image') {
    return `<img src="${mediaObj.url}" alt="Card media" style="max-width: 100%; max-height: 100px; border-radius: 5px;">`;
  } else if (mediaObj.type === 'video') {
    return `<video src="${mediaObj.url}" controls style="max-width: 100%; max-height: 100px; border-radius: 5px;"></video>`;
  } else if (mediaObj.type === 'audio') {
    return `<audio src="${mediaObj.url}" controls style="width: 100%;"></audio>`;
  }
  return '';
}

async function createDeck() {
  const name = document.getElementById('deck-name').value.trim();
  const description = document.getElementById('deck-description').value.trim();

  if (!name) {
    alert('Please enter a deck name');
    return;
  }

  const deckData = {
    name: name,
    description: description,
    created: new Date(),
    userId: app.currentUser.uid
  };

  try {
    if (app.currentUser.uid === 'guest') {
      // Save to localStorage
      deckData.id = 'local_' + Date.now();
      app.decks.push(deckData);
      localStorage.setItem('chunking_decks', JSON.stringify(app.decks));
    } else {
      // Try to save to Firebase first
      try {
        deckData.created = firebase.firestore.FieldValue.serverTimestamp();
        const docRef = await db.collection('decks').add(deckData);
        deckData.id = docRef.id;
        app.decks.push(deckData);
        console.log('Deck saved to Firebase:', deckData.id);
      } catch (firebaseError) {
        console.warn('Firebase save failed, using localStorage:', firebaseError);
        // Fallback to localStorage if Firebase fails
        deckData.id = 'local_' + Date.now();
        deckData.created = new Date().toISOString();
        app.decks.push(deckData);

        // Save to localStorage as backup
        const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
        localDecks.push(deckData);
        localStorage.setItem('chunking_decks', JSON.stringify(localDecks));
      }
    }

    // Clear form and close modal
    document.getElementById('deck-name').value = '';
    document.getElementById('deck-description').value = '';
    document.getElementById('create-deck-modal').style.display = 'none';

    renderDecks();
    alert('Deck created successfully! 🎉');
  } catch (error) {
    console.error('Error creating deck:', error);
    alert('Error creating deck. Please try again.');
  }
}

async function deleteDeck(deckId) {
  const deck = app.decks.find(d => d.id === deckId);
  if (!deck) return;

  const cardCount = app.cards.filter(card => card.deckId === deckId).length;
  const message = cardCount > 0
    ? `Are you sure you want to delete "${deck.name}"?\n\nThis will permanently delete the deck and all ${cardCount} cards in it.\n\nThis action cannot be undone.`
    : `Are you sure you want to delete "${deck.name}"?\n\nThis action cannot be undone.`;

  if (!confirm(message)) return;

  try {
    if (app.currentUser.uid === 'guest' || deckId.startsWith('local_')) {
      // Delete from localStorage
      app.decks = app.decks.filter(d => d.id !== deckId);
      app.cards = app.cards.filter(c => c.deckId !== deckId);
      localStorage.setItem('chunking_decks', JSON.stringify(app.decks));
      localStorage.setItem('chunking_cards', JSON.stringify(app.cards));
    } else {
      // Try to delete from Firebase
      try {
        await db.collection('decks').doc(deckId).delete();

        // Delete all cards in the deck
        const cardsToDelete = app.cards.filter(card => card.deckId === deckId);
        for (const card of cardsToDelete) {
          await db.collection('cards').doc(card.id).delete();
        }

        console.log('Deck deleted from Firebase:', deckId);
      } catch (firebaseError) {
        console.warn('Firebase delete failed, updating localStorage:', firebaseError);
        // Update localStorage as fallback
        const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
        const filteredDecks = localDecks.filter(d => d.id !== deckId);
        localStorage.setItem('chunking_decks', JSON.stringify(filteredDecks));

        const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
        const filteredCards = localCards.filter(c => c.deckId !== deckId);
        localStorage.setItem('chunking_cards', JSON.stringify(filteredCards));
      }

      // Update local arrays
      app.decks = app.decks.filter(d => d.id !== deckId);
      app.cards = app.cards.filter(c => c.deckId !== deckId);
    }

    renderDecks();
    alert('Deck deleted successfully!');
  } catch (error) {
    console.error('Error deleting deck:', error);
    alert('Error deleting deck. Please try again.');
  }
}

// Tab Navigation
document.querySelectorAll('.nav-tab').forEach(tab => {
  tab.addEventListener('click', () => {
    const tabName = tab.getAttribute('data-tab');

    // Update active tab
    document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
    tab.classList.add('active');

    // Update active content
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    document.getElementById(tabName).classList.add('active');
  });
});

// Modal Management
document.getElementById('create-deck-btn').addEventListener('click', () => {
  document.getElementById('create-deck-modal').style.display = 'block';
});

document.getElementById('close-modal').addEventListener('click', () => {
  document.getElementById('create-deck-modal').style.display = 'none';
});

document.getElementById('create-deck-confirm-btn').addEventListener('click', createDeck);

// Close modal when clicking outside
window.addEventListener('click', (e) => {
  const modal = document.getElementById('create-deck-modal');
  if (e.target === modal) {
    modal.style.display = 'none';
  }
});

// Debug function to check existing data
function checkExistingData() {
  console.log('=== CHECKING EXISTING DATA ===');
  console.log('chunking_decks:', localStorage.getItem('chunking_decks'));
  console.log('chunking_cards:', localStorage.getItem('chunking_cards'));
  console.log('chunking_users:', localStorage.getItem('chunking_users'));
  console.log('chunking_user:', localStorage.getItem('chunking_user'));

  // Check all localStorage keys
  const allKeys = [];
  for (let i = 0; i < localStorage.length; i++) {
    allKeys.push(localStorage.key(i));
  }
  console.log('All localStorage keys:', allKeys);
}

// ===== MOCK EXAM SYSTEM =====
const examState = {
  currentExam: null,
  examTimer: null,
  examStartTime: null
};

function showExamSetup() {
  document.getElementById('exam-setup-view').style.display = 'block';
  document.getElementById('exam-taking-view').style.display = 'none';
  document.getElementById('exam-results-view').style.display = 'none';
  populateExamDeckSelection();
  loadExamHistory();
}

function populateExamDeckSelection() {
  const container = document.getElementById('exam-deck-selection');
  if (app.decks.length === 0) {
    container.innerHTML = '<p style="text-align: center; color: var(--text-color); opacity: 0.7;">No decks available. Create some decks first!</p>';
    return;
  }

  container.innerHTML = app.decks.map(deck => {
    const cardCount = app.cards.filter(card => card.deckId === deck.id).length;
    return `
      <label style="display: flex; align-items: center; gap: 10px; padding: 10px; border-radius: 8px; cursor: pointer; transition: background 0.2s;"
             onmouseover="this.style.background='var(--border-color)'"
             onmouseout="this.style.background='transparent'">
        <input type="checkbox" class="exam-deck-checkbox" value="${deck.id}" ${cardCount > 0 ? '' : 'disabled'}>
        <div style="flex: 1;">
          <div style="font-weight: 600; color: var(--text-color);">${deck.name}</div>
          <div style="font-size: 0.9em; color: var(--text-color); opacity: 0.7;">${cardCount} cards available</div>
        </div>
      </label>
    `;
  }).join('');
}

function startMockExam() {
  const examName = document.getElementById('exam-name').value.trim() || 'Mock Exam';
  const selectedDecks = Array.from(document.querySelectorAll('.exam-deck-checkbox:checked')).map(cb => cb.value);
  const questionCount = document.getElementById('exam-question-count').value;
  const timeLimit = parseInt(document.getElementById('exam-time-limit').value);
  const includeFlashcard = document.getElementById('exam-type-flashcard').checked;
  const includeReverse = document.getElementById('exam-type-reverse').checked;

  if (selectedDecks.length === 0) {
    alert('Please select at least one deck');
    return;
  }

  if (!includeFlashcard && !includeReverse) {
    alert('Please select at least one question type');
    return;
  }

  // Get cards from selected decks
  let availableCards = app.cards.filter(card => selectedDecks.includes(card.deckId));

  if (availableCards.length === 0) {
    alert('No cards available in selected decks');
    return;
  }

  // Create question pool
  let questions = [];

  if (includeFlashcard) {
    questions.push(...availableCards.map(card => ({
      id: card.id,
      type: 'flashcard',
      question: card.front,
      answer: card.back,
      deckId: card.deckId,
      tags: card.tags || []
    })));
  }

  if (includeReverse) {
    questions.push(...availableCards.map(card => ({
      id: card.id + '_reverse',
      type: 'reverse',
      question: card.back,
      answer: card.front,
      deckId: card.deckId,
      tags: card.tags || []
    })));
  }

  // Shuffle and limit questions
  shuffleArray(questions);
  const finalQuestionCount = questionCount === 'all' ? questions.length : Math.min(parseInt(questionCount), questions.length);
  questions = questions.slice(0, finalQuestionCount);

  // Initialize exam
  examState.currentExam = {
    name: examName,
    questions: questions,
    currentQuestionIndex: 0,
    answers: [],
    startTime: new Date(),
    timeLimit: timeLimit,
    timeRemaining: timeLimit * 60,
    selectedDecks: selectedDecks
  };

  examState.examStartTime = Date.now();

  // Start timer if time limit is set
  if (timeLimit > 0) {
    startExamTimer();
  }

  // Show exam interface
  document.getElementById('exam-setup-view').style.display = 'none';
  document.getElementById('exam-taking-view').style.display = 'block';
  document.getElementById('current-exam-name').textContent = examName;

  showCurrentExamQuestion();
}

function shuffleArray(array) {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
}

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
  console.log('🐵 ChunkyMonkey initialized');
  checkExistingData();
  setupEventListeners();
});

function setupEventListeners() {
  // Mock Exam event listeners
  document.getElementById('start-exam-btn').addEventListener('click', startMockExam);
  document.getElementById('end-exam-btn').addEventListener('click', endMockExam);
  document.getElementById('exam-show-answer-btn').addEventListener('click', showExamAnswer);
  document.getElementById('exam-next-question-btn').addEventListener('click', nextExamQuestion);
  document.getElementById('exam-mark-correct-btn').addEventListener('click', () => markExamAnswer(true));
  document.getElementById('exam-mark-incorrect-btn').addEventListener('click', () => markExamAnswer(false));
  document.getElementById('create-focus-deck-btn').addEventListener('click', createFocusDeck);
  document.getElementById('review-mistakes-btn').addEventListener('click', reviewMistakes);
  document.getElementById('retake-exam-btn').addEventListener('click', retakeExam);
  document.getElementById('new-exam-btn').addEventListener('click', showExamSetup);

  // Initialize exam setup when tab is shown
  document.querySelector('[data-tab="exams"]').addEventListener('click', () => {
    setTimeout(showExamSetup, 100); // Small delay to ensure tab is active
  });

  // Initialize study tab when shown
  document.querySelector('[data-tab="study"]').addEventListener('click', () => {
    setTimeout(() => {
      populateStudyDeckSelector();
      updateStudyCardCounts();
    }, 100);
  });

  // Card management
  document.getElementById('add-card-btn').addEventListener('click', () => showRealTimeCardEditor());
  document.getElementById('close-card-modal').addEventListener('click', hideCardEditor);
  document.getElementById('save-card-btn').addEventListener('click', saveCard);
  document.getElementById('cancel-card-btn').addEventListener('click', hideCardEditor);

  // Real-time card editor
  document.getElementById('back-to-decks-btn').addEventListener('click', closeDeckDetail);
  document.getElementById('add-card-to-deck-btn').addEventListener('click', () => showRealTimeCardEditor());

  // Card editor controls
  document.getElementById('edit-front-btn').addEventListener('click', () => switchCardSide('front'));
  document.getElementById('edit-back-btn').addEventListener('click', () => switchCardSide('back'));
  document.getElementById('add-media-btn').addEventListener('click', () => document.getElementById('card-media-input').click());
  document.getElementById('card-media-input').addEventListener('change', handleMediaUpload);

  // Real-time styling
  document.getElementById('card-font-select').addEventListener('change', updateCardStyle);
  document.getElementById('card-font-size-select').addEventListener('change', updateCardStyle);
  document.getElementById('card-bg-color-picker').addEventListener('change', updateCardStyle);
  document.getElementById('card-text-color-picker').addEventListener('change', updateCardStyle);

  // Color presets
  document.querySelectorAll('.color-preset').forEach(preset => {
    preset.addEventListener('click', (e) => {
      const bg = e.target.dataset.bg;
      const text = e.target.dataset.text;
      document.getElementById('card-bg-color-picker').value = bg;
      document.getElementById('card-text-color-picker').value = text;
      updateCardStyle();
    });
  });

  // Settings event listeners
  document.getElementById('theme-select').addEventListener('change', changeTheme);
  document.getElementById('accent-color-select').addEventListener('change', changeAccentColor);

  // Load settings on startup
  loadUserSettings();

  // Study session event listeners
  document.getElementById('flip-card-btn').addEventListener('click', flipStudyCard);
  document.getElementById('end-study-btn').addEventListener('click', () => {
    document.getElementById('study-session').style.display = 'none';
    document.getElementById('study-setup').style.display = 'block';
  });
  document.getElementById('study-again-btn').addEventListener('click', () => {
    document.getElementById('study-complete').style.display = 'none';
    document.getElementById('study-setup').style.display = 'block';
  });
  document.getElementById('back-to-options-btn').addEventListener('click', () => {
    document.getElementById('study-complete').style.display = 'none';
    document.getElementById('study-setup').style.display = 'block';
  });

  // Initialize study options
  initializeStudyOptions();

  // Keyboard shortcuts
  document.addEventListener('keydown', handleKeyboardShortcuts);

  // Manage cards event listeners
  document.getElementById('filter-deck').addEventListener('change', renderCards);
  document.getElementById('search-cards').addEventListener('input', renderCards);
  document.getElementById('clear-filters-btn').addEventListener('click', () => {
    document.getElementById('filter-deck').value = '';
    document.getElementById('search-cards').value = '';
    renderCards();
  });

  // Initialize manage cards when tab is shown
  document.querySelector('[data-tab="manage"]').addEventListener('click', () => {
    setTimeout(() => {
      updateCardCounts();
      renderCards();
    }, 100);
  });

  // Initialize import tab when shown
  document.querySelector('[data-tab="import"]').addEventListener('click', () => {
    setTimeout(() => {
      populateImportDeckSelectors();
    }, 100);
  });

  // Account settings event listeners
  const changePasswordBtn = document.getElementById('change-password-btn');
  const downloadDataBtn = document.getElementById('download-data-btn');
  const deleteAccountBtn = document.getElementById('delete-account-btn');

  if (changePasswordBtn) changePasswordBtn.addEventListener('click', showChangePasswordModal);
  if (downloadDataBtn) downloadDataBtn.addEventListener('click', downloadUserData);
  if (deleteAccountBtn) deleteAccountBtn.addEventListener('click', deleteUserAccount);
  document.getElementById('close-password-modal').addEventListener('click', hideChangePasswordModal);
  document.getElementById('update-password-btn').addEventListener('click', updatePassword);
  document.getElementById('cancel-password-btn').addEventListener('click', hideChangePasswordModal);

  // Data management event listeners
  const exportBtn = document.getElementById('export-data-btn');
  const backupBtn = document.getElementById('backup-data-btn');
  const syncBtn = document.getElementById('sync-data-btn');
  const clearBtn = document.getElementById('clear-cache-btn');
  const resetBtn = document.getElementById('reset-progress-btn');
  const deleteBtn = document.getElementById('delete-all-data-btn');

  if (exportBtn) exportBtn.addEventListener('click', exportAllData);
  if (backupBtn) backupBtn.addEventListener('click', createBackup);
  if (syncBtn) syncBtn.addEventListener('click', syncData);
  if (clearBtn) clearBtn.addEventListener('click', clearCache);
  if (resetBtn) resetBtn.addEventListener('click', resetProgress);
  if (deleteBtn) deleteBtn.addEventListener('click', deleteAllData);

  // Notification event listeners
  const saveNotificationBtn = document.getElementById('save-notification-settings-btn');
  if (saveNotificationBtn) saveNotificationBtn.addEventListener('click', saveNotificationSettings);
}

// Settings Functions
function changeTheme() {
  const theme = document.getElementById('theme-select').value;

  if (theme === 'dark') {
    document.body.setAttribute('data-theme', 'dark');
  } else if (theme === 'light') {
    document.body.setAttribute('data-theme', 'light');
  } else {
    // Auto theme
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    document.body.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
  }

  localStorage.setItem('chunky_theme', theme);
}

function changeAccentColor() {
  const color = document.getElementById('accent-color-select').value;
  const root = document.documentElement;

  const colorMap = {
    teal: '#14b8a6',
    blue: '#3b82f6',
    purple: '#8b5cf6',
    pink: '#ec4899',
    green: '#10b981',
    orange: '#f59e0b'
  };

  root.style.setProperty('--primary-color', colorMap[color]);
  localStorage.setItem('chunky_accent_color', color);
}

function loadUserSettings() {
  // Load theme
  const savedTheme = localStorage.getItem('chunky_theme') || 'light';
  document.getElementById('theme-select').value = savedTheme;
  changeTheme();

  // Load accent color
  const savedAccent = localStorage.getItem('chunky_accent_color') || 'teal';
  document.getElementById('accent-color-select').value = savedAccent;
  changeAccentColor();

  // Load account info
  loadAccountInfo();
}

function loadAccountInfo() {
  if (app.currentUser) {
    const emailEl = document.getElementById('user-email');
    const typeEl = document.getElementById('account-type');
    const memberEl = document.getElementById('member-since');

    if (emailEl) emailEl.textContent = app.currentUser.email || 'N/A';
    if (typeEl) typeEl.textContent = app.currentUser.uid === 'guest' ? 'Guest' : 'Registered';

    if (memberEl) {
      if (app.currentUser.metadata && app.currentUser.metadata.creationTime) {
        memberEl.textContent = new Date(app.currentUser.metadata.creationTime).toLocaleDateString();
      } else {
        memberEl.textContent = 'Unknown';
      }
    }
  }
}

// Initialize settings when tab is shown
document.querySelector('[data-tab="settings"]').addEventListener('click', () => {
  setTimeout(() => {
    loadUserSettings();
    loadAccountInfo();
  }, 100);
});

// Study Session Functions
const studyState = {
  currentSession: null,
  currentCardIndex: 0,
  showingBack: false,
  correctCount: 0,
  incorrectCount: 0,
  // Chunking specific
  currentChunk: 0,
  chunkSize: 7,
  chunks: [],
  chunkCorrectCards: [],
  studyMode: 'chunking'
};

function initializeStudyOptions() {
  // Populate deck selector
  populateStudyDeckSelector();

  // Add event listeners with null checks
  const chunkSizeEl = document.getElementById('chunk-size');
  const startStudyBtn = document.getElementById('start-study-btn');

  if (chunkSizeEl) chunkSizeEl.addEventListener('change', handleChunkSizeChange);
  if (startStudyBtn) startStudyBtn.addEventListener('click', startCustomStudySession);

  // Quick study buttons
  document.querySelectorAll('.quick-study-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      const type = btn.dataset.type;
      startQuickStudy(type);
    });
  });

  updateStudyCardCounts();
}

function populateStudyDeckSelector() {
  const container = document.getElementById('study-deck-checkboxes');
  if (!container) return;

  if (app.decks.length === 0) {
    container.innerHTML = '<p style="text-align: center; color: var(--text-color); opacity: 0.7;">No decks available. Create some decks first!</p>';
    return;
  }

  container.innerHTML = app.decks.map(deck => {
    const cardCount = app.cards.filter(card => card.deckId === deck.id).length;
    return `
      <label style="display: flex; align-items: center; gap: 10px; padding: 8px; border-radius: 6px; cursor: pointer; transition: background 0.2s;"
             onmouseover="this.style.background='var(--border-color)'"
             onmouseout="this.style.background='transparent'">
        <input type="checkbox" class="study-deck-checkbox" value="${deck.id}" ${cardCount > 0 ? 'checked' : 'disabled'}>
        <div style="flex: 1;">
          <div style="font-weight: 600; color: var(--text-color);">${deck.name}</div>
          <div style="font-size: 0.9em; color: var(--text-color); opacity: 0.7;">${cardCount} cards available</div>
        </div>
      </label>
    `;
  }).join('');

  // Add event listeners to checkboxes
  container.querySelectorAll('.study-deck-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateAvailableCardsCount);
  });

  updateAvailableCardsCount();
}

function populateImportDeckSelectors() {
  const selectors = ['csv-deck-select', 'text-deck-select', 'anki-deck-select'];

  selectors.forEach(selectorId => {
    const selector = document.getElementById(selectorId);
    if (selector) {
      selector.innerHTML = '<option value="">Select a deck...</option>' +
        app.decks.map(deck => `<option value="${deck.id}">${deck.name}</option>`).join('');
    }
  });
}

function handleChunkSizeChange() {
  const chunkSize = document.getElementById('chunk-size').value;
  const customInput = document.getElementById('custom-chunk-size');

  if (chunkSize === 'custom') {
    customInput.style.display = 'block';
    customInput.focus();
  } else {
    customInput.style.display = 'none';
  }
}

function updateAvailableCardsCount() {
  const selectedDecks = Array.from(document.querySelectorAll('.study-deck-checkbox:checked')).map(cb => cb.value);
  const availableCards = app.cards.filter(card => selectedDecks.includes(card.deckId));
  document.getElementById('available-cards-count').textContent = availableCards.length;
}

function updateStudyCardCounts() {
  const now = new Date();
  const dueCards = app.cards.filter(card => {
    const nextReview = card.nextReview ? new Date(card.nextReview) : new Date(0);
    return nextReview <= now;
  });

  const newCards = app.cards.filter(card => !card.lastReviewed);

  document.getElementById('due-cards-count').textContent = dueCards.length;
  document.getElementById('new-cards-count').textContent = newCards.length;
}

function startCustomStudySession() {
  const selectedDecks = Array.from(document.querySelectorAll('.study-deck-checkbox:checked')).map(cb => cb.value);
  const studyMode = document.getElementById('study-mode-select').value;

  if (selectedDecks.length === 0) {
    alert('Please select at least one deck to study');
    return;
  }

  let studyCards = app.cards.filter(card => selectedDecks.includes(card.deckId));

  if (studyCards.length === 0) {
    alert('No cards available in selected decks');
    return;
  }

  // Get chunk size
  let chunkSize = parseInt(document.getElementById('chunk-size').value);
  if (document.getElementById('chunk-size').value === 'custom') {
    chunkSize = parseInt(document.getElementById('custom-chunk-size').value) || 7;
  }

  // Get shuffle preference
  const shouldShuffle = document.getElementById('shuffle-cards').checked;

  startStudySession(studyCards, studyMode, chunkSize, shouldShuffle);
}

function startQuickStudy(type) {
  let studyCards = [];

  switch (type) {
    case 'due':
      const now = new Date();
      studyCards = app.cards.filter(card => {
        const nextReview = card.nextReview ? new Date(card.nextReview) : new Date(0);
        return nextReview <= now;
      });
      break;
    case 'new':
      studyCards = app.cards.filter(card => !card.lastReviewed);
      break;
    case 'random':
      studyCards = [...app.cards];
      shuffleArray(studyCards);
      studyCards = studyCards.slice(0, 20); // Limit to 20 random cards
      break;
  }

  if (studyCards.length === 0) {
    alert('No cards available for this study type!');
    return;
  }

  startStudySession(studyCards, 'chunking', 7);
}

function startStudySession(cards, mode = 'chunking', chunkSize = 7, shouldShuffle = true) {
  // Shuffle cards if requested
  if (shouldShuffle) {
    shuffleArray(cards);
  }

  studyState.studyMode = mode;
  studyState.chunkSize = chunkSize;
  studyState.currentCardIndex = 0;
  studyState.showingBack = false;
  studyState.correctCount = 0;
  studyState.incorrectCount = 0;
  studyState.currentChunk = 0;
  studyState.chunkCorrectCards = [];

  if (mode === 'chunking') {
    // Split cards into chunks
    studyState.chunks = [];
    for (let i = 0; i < cards.length; i += chunkSize) {
      studyState.chunks.push(cards.slice(i, i + chunkSize));
    }

    studyState.currentSession = {
      cards: studyState.chunks[0], // Start with first chunk
      allCards: cards,
      type: 'chunking'
    };
  } else {
    studyState.currentSession = {
      cards: cards,
      type: mode
    };
  }

  showStudySession();
  showCurrentStudyCard();
}

function showStudySession() {
  document.getElementById('study-setup').style.display = 'none';
  document.getElementById('study-session').style.display = 'block';
  document.getElementById('study-complete').style.display = 'none';

  const sessionTitle = {
    chunking: '🧩 Chunking Study Session',
    spaced: '📅 Spaced Repetition',
    all: '📚 Study All Cards'
  };

  document.getElementById('study-session-title').textContent = sessionTitle[studyState.studyMode] || '📚 Study Session';
}

function showCurrentStudyCard() {
  const card = studyState.currentSession.cards[studyState.currentCardIndex];

  // Update progress text
  if (studyState.studyMode === 'chunking') {
    const chunkProgress = `Card ${studyState.currentCardIndex + 1} of ${studyState.currentSession.cards.length} in Chunk ${studyState.currentChunk + 1}`;
    document.getElementById('study-progress').textContent = chunkProgress;

    // Update progress bars
    const chunkCorrectCount = studyState.chunkCorrectCards.length;
    const chunkTotal = studyState.currentSession.cards.length;
    const chunkPercent = (chunkCorrectCount / chunkTotal) * 100;

    document.getElementById('chunk-progress-text').textContent = `${chunkCorrectCount}/${chunkTotal}`;
    document.getElementById('chunk-progress-bar').style.width = `${chunkPercent}%`;

    const overallCorrect = studyState.correctCount;
    const overallTotal = studyState.currentSession.allCards ? studyState.currentSession.allCards.length : studyState.currentSession.cards.length;
    const overallPercent = (overallCorrect / overallTotal) * 100;

    document.getElementById('overall-progress-text').textContent = `${overallCorrect}/${overallTotal}`;
    document.getElementById('overall-progress-bar').style.width = `${overallPercent}%`;
  } else {
    const progress = `Card ${studyState.currentCardIndex + 1} of ${studyState.currentSession.cards.length}`;
    document.getElementById('study-progress').textContent = progress;
  }

  document.getElementById('correct-count').textContent = studyState.correctCount;
  document.getElementById('incorrect-count').textContent = studyState.incorrectCount;

  // Show front of card
  studyState.showingBack = false;
  document.getElementById('card-side-indicator').textContent = 'Front';
  document.getElementById('card-content').innerHTML = card.front;

  // Apply card styling
  const cardElement = document.querySelector('.study-card');
  if (card.backgroundColor) cardElement.style.background = card.backgroundColor;
  if (card.textColor) cardElement.style.color = card.textColor;
  if (card.fontFamily) cardElement.style.fontFamily = card.fontFamily;
  if (card.fontSize) cardElement.style.fontSize = card.fontSize;

  // Reset buttons
  document.getElementById('flip-card-btn').style.display = 'block';
  document.getElementById('difficulty-buttons').style.display = 'none';
}

function flipStudyCard() {
  const card = studyState.currentSession.cards[studyState.currentCardIndex];

  if (!studyState.showingBack) {
    // Show back
    studyState.showingBack = true;
    document.getElementById('card-side-indicator').textContent = 'Back';
    document.getElementById('card-content').innerHTML = card.back;
    document.getElementById('flip-card-btn').style.display = 'none';
    document.getElementById('difficulty-buttons').style.display = 'block';
  }
}

function rateDifficulty(rating) {
  const card = studyState.currentSession.cards[studyState.currentCardIndex];

  // Update card statistics
  card.lastReviewed = new Date();

  if (rating === 3) { // Correct
    studyState.correctCount++;
    card.correct = (card.correct || 0) + 1;

    // For chunking mode, track correct cards in current chunk
    if (studyState.studyMode === 'chunking') {
      if (!studyState.chunkCorrectCards.includes(studyState.currentCardIndex)) {
        studyState.chunkCorrectCards.push(studyState.currentCardIndex);
      }
    }
  } else { // Wrong (1) or Not Confident (2)
    studyState.incorrectCount++;
    card.incorrect = (card.incorrect || 0) + 1;

    // For chunking mode, remove from correct cards if it was there
    if (studyState.studyMode === 'chunking') {
      const index = studyState.chunkCorrectCards.indexOf(studyState.currentCardIndex);
      if (index > -1) {
        studyState.chunkCorrectCards.splice(index, 1);
      }
    }
  }

  // Calculate next review date based on rating
  const intervals = {
    1: 1,      // Wrong - 1 day
    2: 2,      // Not Confident - 2 days
    3: 7       // Correct - 7 days
  };

  const nextReviewDate = new Date();
  nextReviewDate.setDate(nextReviewDate.getDate() + intervals[rating]);
  card.nextReview = nextReviewDate;

  // Update confidence
  const total = card.correct + card.incorrect;
  card.confidence = total > 0 ? Math.round((card.correct / total) * 100) : 0;

  // Save card updates
  saveCardProgress(card);

  // Handle chunking logic
  if (studyState.studyMode === 'chunking') {
    handleChunkingProgress();
  } else {
    // Move to next card normally
    studyState.currentCardIndex++;

    if (studyState.currentCardIndex >= studyState.currentSession.cards.length) {
      endStudySession();
    } else {
      showCurrentStudyCard();
    }
  }
}

function handleChunkingProgress() {
  // Check if all cards in current chunk are correct
  const allChunkCorrect = studyState.chunkCorrectCards.length === studyState.currentSession.cards.length;

  if (allChunkCorrect) {
    // Move to next chunk
    studyState.currentChunk++;

    if (studyState.currentChunk >= studyState.chunks.length) {
      // All chunks completed
      endStudySession();
      return;
    }

    // Start next chunk
    studyState.currentSession.cards = studyState.chunks[studyState.currentChunk];
    studyState.currentCardIndex = 0;
    studyState.chunkCorrectCards = [];

    alert(`🎉 Chunk ${studyState.currentChunk} completed! Moving to chunk ${studyState.currentChunk + 1}.`);
    showCurrentStudyCard();
  } else {
    // Get the current card
    const currentCard = studyState.currentSession.cards[studyState.currentCardIndex];
    const isCurrentCardCorrect = studyState.chunkCorrectCards.includes(studyState.currentCardIndex);

    if (!isCurrentCardCorrect) {
      // Move incorrect card to end of chunk (unless it's the only card left)
      const incorrectCards = studyState.currentSession.cards.filter((_, index) =>
        !studyState.chunkCorrectCards.includes(index)
      );

      if (incorrectCards.length > 1) {
        // Remove current card and add it to the end
        studyState.currentSession.cards.splice(studyState.currentCardIndex, 1);
        studyState.currentSession.cards.push(currentCard);

        // Update the correct cards indices since we removed a card
        studyState.chunkCorrectCards = studyState.chunkCorrectCards.map(index =>
          index > studyState.currentCardIndex ? index - 1 : index
        );

        // Don't increment currentCardIndex since we removed the current card
        // If we're at the end, wrap to beginning
        if (studyState.currentCardIndex >= studyState.currentSession.cards.length) {
          studyState.currentCardIndex = 0;
        }
      } else {
        // Only one incorrect card left, just move to next position
        studyState.currentCardIndex++;
        if (studyState.currentCardIndex >= studyState.currentSession.cards.length) {
          studyState.currentCardIndex = 0;
        }
      }
    } else {
      // Current card is correct, move to next card
      studyState.currentCardIndex++;
      if (studyState.currentCardIndex >= studyState.currentSession.cards.length) {
        studyState.currentCardIndex = 0;
      }
    }

    // Find next card that's not mastered
    let attempts = 0;
    while (studyState.chunkCorrectCards.includes(studyState.currentCardIndex) &&
           attempts < studyState.currentSession.cards.length) {
      studyState.currentCardIndex++;
      if (studyState.currentCardIndex >= studyState.currentSession.cards.length) {
        studyState.currentCardIndex = 0;
      }
      attempts++;
    }

    showCurrentStudyCard();
  }
}

function endStudySession() {
  document.getElementById('study-session').style.display = 'none';
  document.getElementById('study-complete').style.display = 'block';

  document.getElementById('final-correct').textContent = studyState.correctCount;
  document.getElementById('final-incorrect').textContent = studyState.incorrectCount;

  // Save study session to history
  const sessionData = {
    type: studyState.currentSession.type,
    cardsStudied: studyState.currentSession.cards.length,
    correct: studyState.correctCount,
    incorrect: studyState.incorrectCount,
    date: new Date()
  };

  const studyHistory = JSON.parse(localStorage.getItem('chunky_study_history') || '[]');
  studyHistory.unshift(sessionData);
  if (studyHistory.length > 50) studyHistory.splice(50);
  localStorage.setItem('chunky_study_history', JSON.stringify(studyHistory));

  // Update analytics
  updateAnalytics();
}

async function saveCardProgress(card) {
  try {
    if (app.currentUser.uid === 'guest' || card.id.startsWith('local_')) {
      const cardIndex = app.cards.findIndex(c => c.id === card.id);
      if (cardIndex >= 0) {
        app.cards[cardIndex] = card;
        localStorage.setItem('chunking_cards', JSON.stringify(app.cards));
      }
    } else {
      await db.collection('cards').doc(card.id).update({
        lastReviewed: card.lastReviewed,
        nextReview: card.nextReview,
        correct: card.correct,
        incorrect: card.incorrect,
        confidence: card.confidence
      });

      const cardIndex = app.cards.findIndex(c => c.id === card.id);
      if (cardIndex >= 0) {
        app.cards[cardIndex] = card;
      }
    }
  } catch (error) {
    console.error('Error saving card progress:', error);
  }
}

// Keyboard Shortcuts
function handleKeyboardShortcuts(event) {
  // Only handle shortcuts when study session is active
  if (document.getElementById('study-session').style.display === 'none') {
    return;
  }

  // Prevent shortcuts when typing in input fields
  if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
    return;
  }

  switch (event.key.toLowerCase()) {
    case ' ':
    case 'spacebar':
      event.preventDefault();
      if (document.getElementById('flip-card-btn').style.display !== 'none') {
        flipStudyCard();
      }
      break;
    case 'r':
      event.preventDefault();
      if (document.getElementById('difficulty-buttons').style.display !== 'none') {
        rateDifficulty(1); // Wrong
      }
      break;
    case 'y':
      event.preventDefault();
      if (document.getElementById('difficulty-buttons').style.display !== 'none') {
        rateDifficulty(2); // Not Confident
      }
      break;
    case 'g':
      event.preventDefault();
      if (document.getElementById('difficulty-buttons').style.display !== 'none') {
        rateDifficulty(3); // Correct
      }
      break;
  }
}

// Real-time Card Editor State
const cardEditor = {
  currentSide: 'front',
  frontContent: '',
  backContent: '',
  frontMedia: null,
  backMedia: null,
  editingCard: null
};

function showRealTimeCardEditor(card = null) {
  const modal = document.getElementById('card-editor-modal');

  if (card) {
    // Editing existing card
    cardEditor.editingCard = card;
    cardEditor.frontContent = card.front;
    cardEditor.backContent = card.back;
    cardEditor.frontMedia = card.frontMedia || null;
    cardEditor.backMedia = card.backMedia || null;

    // Set styling
    if (card.backgroundColor) document.getElementById('card-bg-color-picker').value = card.backgroundColor;
    if (card.textColor) document.getElementById('card-text-color-picker').value = card.textColor;
    if (card.fontFamily) document.getElementById('card-font-select').value = card.fontFamily;
    if (card.fontSize) document.getElementById('card-font-size-select').value = card.fontSize;
    if (card.tags) document.getElementById('card-tags').value = card.tags.join(', ');

    document.getElementById('card-modal-title').textContent = '✏️ Edit Flashcard';
  } else {
    // Creating new card
    cardEditor.editingCard = null;
    cardEditor.frontContent = '';
    cardEditor.backContent = '';
    cardEditor.frontMedia = null;
    cardEditor.backMedia = null;

    // Reset styling
    document.getElementById('card-bg-color-picker').value = '#ffffff';
    document.getElementById('card-text-color-picker').value = '#1e293b';
    document.getElementById('card-font-select').value = 'inherit';
    document.getElementById('card-font-size-select').value = '1.3em';
    document.getElementById('card-tags').value = '';

    document.getElementById('card-modal-title').textContent = '✏️ Create New Flashcard';
  }

  switchCardSide('front');
  updateCardStyle();
  modal.style.display = 'block';
}

function switchCardSide(side) {
  cardEditor.currentSide = side;

  // Update button states
  document.getElementById('edit-front-btn').className = side === 'front' ? 'btn btn-primary' : 'btn btn-secondary';
  document.getElementById('edit-back-btn').className = side === 'back' ? 'btn btn-primary' : 'btn btn-secondary';

  // Update content
  const editor = document.getElementById('card-text-editor');
  const mediaContainer = document.getElementById('card-media-container');

  if (side === 'front') {
    editor.innerHTML = cardEditor.frontContent;
    mediaContainer.innerHTML = cardEditor.frontMedia ? renderCardMedia(cardEditor.frontMedia) : '';
    editor.setAttribute('placeholder', 'Click here to add front content...');
  } else {
    editor.innerHTML = cardEditor.backContent;
    mediaContainer.innerHTML = cardEditor.backMedia ? renderCardMedia(cardEditor.backMedia) : '';
    editor.setAttribute('placeholder', 'Click here to add back content...');
  }

  // Add input listener for real-time updates
  editor.oninput = () => {
    if (cardEditor.currentSide === 'front') {
      cardEditor.frontContent = editor.innerHTML;
    } else {
      cardEditor.backContent = editor.innerHTML;
    }
  };
}

function updateCardStyle() {
  const cardElement = document.getElementById('card-editor');
  const font = document.getElementById('card-font-select').value;
  const fontSize = document.getElementById('card-font-size-select').value;
  const bgColor = document.getElementById('card-bg-color-picker').value;
  const textColor = document.getElementById('card-text-color-picker').value;

  cardElement.style.setProperty('--card-background', bgColor);
  cardElement.style.setProperty('--card-text-color', textColor);
  cardElement.style.setProperty('--card-font', font);
  cardElement.style.setProperty('--card-font-size', fontSize);

  // Apply to editor
  cardElement.style.background = bgColor;
  cardElement.style.color = textColor;
  cardElement.style.fontFamily = font;
  cardElement.style.fontSize = fontSize;
}

function handleMediaUpload(event) {
  const file = event.target.files[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = (e) => {
    const mediaData = {
      type: file.type.startsWith('image/') ? 'image' :
            file.type.startsWith('video/') ? 'video' :
            file.type.startsWith('audio/') ? 'audio' : 'file',
      url: e.target.result,
      name: file.name
    };

    if (cardEditor.currentSide === 'front') {
      cardEditor.frontMedia = mediaData;
    } else {
      cardEditor.backMedia = mediaData;
    }

    // Update display
    switchCardSide(cardEditor.currentSide);
  };

  reader.readAsDataURL(file);
}

function editCard(cardId) {
  const card = app.cards.find(c => c.id === cardId);
  if (card) {
    showRealTimeCardEditor(card);
  }
}

function previewCard(cardId) {
  const card = app.cards.find(c => c.id === cardId);
  if (!card) return;

  // Create a simple preview modal
  const previewHtml = `
    <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 2000; display: flex; align-items: center; justify-content: center;" onclick="this.remove()">
      <div style="background: ${card.backgroundColor || 'var(--card-bg)'}; color: ${card.textColor || 'var(--text-color)'}; font-family: ${card.fontFamily || 'inherit'}; font-size: ${card.fontSize || '1.3em'}; padding: 40px; border-radius: 20px; max-width: 500px; text-align: center; position: relative;" onclick="event.stopPropagation()">
        <button onclick="this.closest('div').remove()" style="position: absolute; top: 15px; right: 20px; background: none; border: none; font-size: 24px; cursor: pointer; color: inherit;">&times;</button>
        <div style="margin-bottom: 20px; font-weight: 600; opacity: 0.8;">FRONT</div>
        <div style="margin-bottom: 30px;">${card.front}</div>
        ${card.frontMedia ? renderCardMedia(card.frontMedia) : ''}
        <hr style="margin: 30px 0; opacity: 0.3;">
        <div style="margin-bottom: 20px; font-weight: 600; opacity: 0.8;">BACK</div>
        <div>${card.back}</div>
        ${card.backMedia ? renderCardMedia(card.backMedia) : ''}
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', previewHtml);
}

// Exam Timer Functions
function startExamTimer() {
  const timerDisplay = document.getElementById('exam-timer');

  examState.examTimer = setInterval(() => {
    examState.currentExam.timeRemaining--;

    const minutes = Math.floor(examState.currentExam.timeRemaining / 60);
    const seconds = examState.currentExam.timeRemaining % 60;
    timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

    if (examState.currentExam.timeRemaining <= 300) {
      timerDisplay.style.color = '#ef4444';
    } else if (examState.currentExam.timeRemaining <= 600) {
      timerDisplay.style.color = '#f59e0b';
    }

    if (examState.currentExam.timeRemaining <= 0) {
      endMockExam();
    }
  }, 1000);
}

function showCurrentExamQuestion() {
  const question = examState.currentExam.questions[examState.currentExam.currentQuestionIndex];
  const progress = `Question ${examState.currentExam.currentQuestionIndex + 1} of ${examState.currentExam.questions.length}`;

  document.getElementById('exam-progress').textContent = progress;
  document.getElementById('exam-question-content').innerHTML = `
    <div style="margin-bottom: 15px; font-size: 0.9em; color: var(--text-color); opacity: 0.7;">
      ${question.type === 'flashcard' ? 'Front → Back' : 'Back → Front'}
    </div>
    <h4 style="color: var(--text-color); margin-bottom: 30px; line-height: 1.4;">${question.question}</h4>
  `;

  document.getElementById('exam-answer-input').value = '';
  document.getElementById('exam-answer-reveal').style.display = 'none';
  document.getElementById('exam-show-answer-btn').style.display = 'inline-block';
  document.getElementById('exam-next-question-btn').style.display = 'none';
}

function showExamAnswer() {
  const question = examState.currentExam.questions[examState.currentExam.currentQuestionIndex];
  const userAnswer = document.getElementById('exam-answer-input').value.trim();

  examState.currentExam.answers[examState.currentExam.currentQuestionIndex] = {
    questionId: question.id,
    userAnswer: userAnswer,
    correctAnswer: question.answer,
    question: question.question,
    type: question.type,
    deckId: question.deckId,
    tags: question.tags
  };

  document.getElementById('exam-correct-answer').innerHTML = question.answer;
  document.getElementById('exam-answer-reveal').style.display = 'block';
  document.getElementById('exam-show-answer-btn').style.display = 'none';
}

function markExamAnswer(isCorrect) {
  examState.currentExam.answers[examState.currentExam.currentQuestionIndex].isCorrect = isCorrect;
  document.getElementById('exam-next-question-btn').style.display = 'inline-block';
  document.getElementById('exam-mark-correct-btn').style.display = 'none';
  document.getElementById('exam-mark-incorrect-btn').style.display = 'none';
}

function nextExamQuestion() {
  examState.currentExam.currentQuestionIndex++;

  if (examState.currentExam.currentQuestionIndex >= examState.currentExam.questions.length) {
    endMockExam();
  } else {
    showCurrentExamQuestion();
  }
}

function endMockExam() {
  if (examState.examTimer) {
    clearInterval(examState.examTimer);
    examState.examTimer = null;
  }

  const results = calculateExamResults();
  saveExamToHistory(results);
  showExamResults(results);
}

function calculateExamResults() {
  const totalQuestions = examState.currentExam.questions.length;
  const answeredQuestions = examState.currentExam.answers.filter(a => a.isCorrect !== undefined).length;
  const correctAnswers = examState.currentExam.answers.filter(a => a.isCorrect === true).length;
  const incorrectAnswers = examState.currentExam.answers.filter(a => a.isCorrect === false).length;

  const score = answeredQuestions > 0 ? Math.round((correctAnswers / answeredQuestions) * 100) : 0;
  const completionTime = Math.round((Date.now() - examState.examStartTime) / 1000 / 60);

  const weakAreas = analyzeWeakAreas();

  return {
    examName: examState.currentExam.name,
    totalQuestions,
    answeredQuestions,
    correctAnswers,
    incorrectAnswers,
    score,
    completionTime,
    weakAreas,
    answers: examState.currentExam.answers,
    selectedDecks: examState.currentExam.selectedDecks,
    date: new Date()
  };
}

function analyzeWeakAreas() {
  const incorrectAnswers = examState.currentExam.answers.filter(a => a.isCorrect === false);
  const deckAnalysis = {};
  const tagAnalysis = {};

  incorrectAnswers.forEach(answer => {
    const deck = app.decks.find(d => d.id === answer.deckId);
    if (deck) {
      if (!deckAnalysis[deck.name]) {
        deckAnalysis[deck.name] = { count: 0, deckId: deck.id };
      }
      deckAnalysis[deck.name].count++;
    }

    if (answer.tags && answer.tags.length > 0) {
      answer.tags.forEach(tag => {
        if (!tagAnalysis[tag]) {
          tagAnalysis[tag] = 0;
        }
        tagAnalysis[tag]++;
      });
    }
  });

  return {
    decks: Object.entries(deckAnalysis).sort(([,a], [,b]) => b.count - a.count).slice(0, 5),
    tags: Object.entries(tagAnalysis).sort(([,a], [,b]) => b - a).slice(0, 5)
  };
}

function saveExamToHistory(results) {
  const examHistory = JSON.parse(localStorage.getItem('chunky_exam_history') || '[]');
  examHistory.unshift(results);

  if (examHistory.length > 20) {
    examHistory.splice(20);
  }

  localStorage.setItem('chunky_exam_history', JSON.stringify(examHistory));
}

function showExamResults(results) {
  document.getElementById('exam-taking-view').style.display = 'none';
  document.getElementById('exam-results-view').style.display = 'block';

  document.getElementById('exam-final-score').textContent = `${results.score}%`;
  document.getElementById('exam-grade-text').textContent = getGradeText(results.score);
  document.getElementById('exam-completion-time').textContent = `Completed in ${results.completionTime} minutes`;

  document.getElementById('exam-performance-stats').innerHTML = `
    <div style="margin-bottom: 10px;"><strong>Questions Answered:</strong> ${results.answeredQuestions} / ${results.totalQuestions}</div>
    <div style="margin-bottom: 10px;"><strong>Correct:</strong> <span style="color: #10b981;">${results.correctAnswers}</span></div>
    <div style="margin-bottom: 10px;"><strong>Incorrect:</strong> <span style="color: #ef4444;">${results.incorrectAnswers}</span></div>
    <div><strong>Accuracy:</strong> ${results.score}%</div>
  `;

  showFocusAreas(results.weakAreas);
}

function getGradeText(score) {
  if (score >= 90) return '🏆 Excellent!';
  if (score >= 80) return '🎉 Great Job!';
  if (score >= 70) return '👍 Good Work!';
  if (score >= 60) return '📚 Keep Studying!';
  return '💪 More Practice Needed!';
}

function showFocusAreas(weakAreas) {
  const container = document.getElementById('exam-focus-areas');

  if (weakAreas.decks.length === 0 && weakAreas.tags.length === 0) {
    container.innerHTML = '<p style="color: #10b981;">🎯 Great job! No major weak areas identified.</p>';
    return;
  }

  let html = '';

  if (weakAreas.decks.length > 0) {
    html += '<div style="margin-bottom: 15px;"><strong>Challenging Decks:</strong></div>';
    weakAreas.decks.forEach(([deckName, data]) => {
      html += `<div style="margin-bottom: 8px; padding: 8px; background: rgba(239, 68, 68, 0.1); border-radius: 6px;">
        📚 ${deckName} (${data.count} mistakes)
      </div>`;
    });
  }

  if (weakAreas.tags.length > 0) {
    html += '<div style="margin-bottom: 15px; margin-top: 20px;"><strong>Challenging Topics:</strong></div>';
    weakAreas.tags.forEach(([tag, count]) => {
      html += `<div style="margin-bottom: 8px; padding: 8px; background: rgba(245, 158, 11, 0.1); border-radius: 6px;">
        🏷️ ${tag} (${count} mistakes)
      </div>`;
    });
  }

  container.innerHTML = html;
}

function loadExamHistory() {
  const examHistory = JSON.parse(localStorage.getItem('chunky_exam_history') || '[]');
  const container = document.getElementById('exam-history-list');

  if (examHistory.length === 0) {
    container.innerHTML = '<p style="text-align: center; color: var(--text-color); opacity: 0.7;">No exams taken yet. Create your first mock exam above!</p>';
    return;
  }

  container.innerHTML = examHistory.slice(0, 5).map(exam => {
    const date = new Date(exam.date).toLocaleDateString();
    const scoreColor = exam.score >= 80 ? '#10b981' : exam.score >= 60 ? '#f59e0b' : '#ef4444';

    return `
      <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; border: 1px solid var(--border-color); border-radius: 10px; margin-bottom: 10px;">
        <div>
          <div style="font-weight: 600; color: var(--text-color);">${exam.examName}</div>
          <div style="font-size: 0.9em; color: var(--text-color); opacity: 0.7;">${date} • ${exam.completionTime} min</div>
        </div>
        <div style="text-align: right;">
          <div style="font-size: 1.2em; font-weight: 600; color: ${scoreColor};">${exam.score}%</div>
          <div style="font-size: 0.8em; color: var(--text-color); opacity: 0.7;">${exam.correctAnswers}/${exam.totalQuestions}</div>
        </div>
      </div>
    `;
  }).join('');
}

// Card Management Functions
function showCardEditor(card = null) {
  const modal = document.getElementById('card-editor-modal');
  const title = document.getElementById('card-modal-title');
  const deckSelect = document.getElementById('card-deck-select');

  // Populate deck options
  deckSelect.innerHTML = '<option value="">Select a deck...</option>' +
    app.decks.map(deck => `<option value="${deck.id}">${deck.name}</option>`).join('');

  if (card) {
    title.textContent = '✏️ Edit Card';
    document.getElementById('card-deck-select').value = card.deckId;
    document.getElementById('card-front').value = card.front;
    document.getElementById('card-back').value = card.back;
    document.getElementById('card-tags').value = (card.tags || []).join(', ');
    modal.dataset.cardId = card.id;
  } else {
    title.textContent = '✨ Add New Card';
    document.getElementById('card-deck-select').value = '';
    document.getElementById('card-front').value = '';
    document.getElementById('card-back').value = '';
    document.getElementById('card-tags').value = '';
    delete modal.dataset.cardId;
  }

  modal.style.display = 'block';
}

function hideCardEditor() {
  document.getElementById('card-editor-modal').style.display = 'none';

  // Reset editor state
  cardEditor.currentSide = 'front';
  cardEditor.frontContent = '';
  cardEditor.backContent = '';
  cardEditor.frontMedia = null;
  cardEditor.backMedia = null;
  cardEditor.editingCard = null;
}

async function saveCard() {
  // Get data from real-time editor
  const deckId = currentDeck ? currentDeck.id : document.getElementById('card-deck-select')?.value;
  const front = cardEditor.frontContent.trim();
  const back = cardEditor.backContent.trim();
  const tagsInput = document.getElementById('card-tags').value.trim();
  const tags = tagsInput ? tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

  if (!deckId || !front || !back) {
    alert('Please fill in front and back content');
    return;
  }

  const cardData = {
    deckId: deckId,
    front: front,
    back: back,
    tags: tags,
    userId: app.currentUser.uid,
    confidence: 0,
    correct: 0,
    incorrect: 0,
    lastReviewed: null,
    nextReview: new Date(),
    created: new Date(),
    // Styling properties
    backgroundColor: document.getElementById('card-bg-color-picker').value,
    textColor: document.getElementById('card-text-color-picker').value,
    fontFamily: document.getElementById('card-font-select').value,
    fontSize: document.getElementById('card-font-size-select').value,
    // Media properties (store as base64 strings for localStorage compatibility)
    frontMedia: cardEditor.frontMedia ? JSON.stringify(cardEditor.frontMedia) : null,
    backMedia: cardEditor.backMedia ? JSON.stringify(cardEditor.backMedia) : null
  };

  const isEditing = cardEditor.editingCard !== null;

  try {
    if (isEditing) {
      // Update existing card
      const cardId = cardEditor.editingCard.id;
      if (app.currentUser.uid === 'guest' || cardId.startsWith('local_')) {
        const cardIndex = app.cards.findIndex(c => c.id === cardId);
        if (cardIndex >= 0) {
          app.cards[cardIndex] = { ...app.cards[cardIndex], ...cardData };
          localStorage.setItem('chunking_cards', JSON.stringify(app.cards));
        }
      } else {
        await db.collection('cards').doc(cardId).update(cardData);
        const cardIndex = app.cards.findIndex(c => c.id === cardId);
        if (cardIndex >= 0) {
          app.cards[cardIndex] = { ...app.cards[cardIndex], ...cardData };
        }
      }
      alert('Card updated successfully! ✨');
    } else {
      // Create new card
      if (app.currentUser.uid === 'guest') {
        cardData.id = 'local_' + Date.now();
        app.cards.push(cardData);
        localStorage.setItem('chunking_cards', JSON.stringify(app.cards));
      } else {
        try {
          cardData.created = firebase.firestore.FieldValue.serverTimestamp();
          const docRef = await db.collection('cards').add(cardData);
          cardData.id = docRef.id;
          app.cards.push(cardData);
        } catch (firebaseError) {
          console.warn('Firebase save failed, using localStorage:', firebaseError);
          cardData.id = 'local_' + Date.now();
          cardData.created = new Date().toISOString();
          app.cards.push(cardData);

          const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
          localCards.push(cardData);
          localStorage.setItem('chunking_cards', JSON.stringify(localCards));
        }
      }
      alert('Card created successfully! ✨');
    }

    hideCardEditor();
    if (currentDeck) {
      renderDeckCards();
    } else {
      renderCards();
    }
    updateCardCounts();
  } catch (error) {
    console.error('Error saving card:', error);
    alert('Error saving card. Please try again.');
  }
}

function renderCards() {
  const container = document.getElementById('cards-list');
  const filterDeck = document.getElementById('filter-deck').value;
  const searchTerm = document.getElementById('search-cards').value.toLowerCase();

  let filteredCards = app.cards;

  if (filterDeck) {
    filteredCards = filteredCards.filter(card => card.deckId === filterDeck);
  }

  if (searchTerm) {
    filteredCards = filteredCards.filter(card =>
      card.front.toLowerCase().includes(searchTerm) ||
      card.back.toLowerCase().includes(searchTerm) ||
      (card.tags && card.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
    );
  }

  if (filteredCards.length === 0) {
    container.innerHTML = '<p style="text-align: center; color: var(--text-color); opacity: 0.7; grid-column: 1 / -1;">No cards found matching your criteria.</p>';
    return;
  }

  container.innerHTML = filteredCards.map(card => {
    const deck = app.decks.find(d => d.id === card.deckId);
    const deckName = deck ? deck.name : 'Unknown Deck';

    return `
      <div class="deck-card" style="position: relative;">
        <button class="delete-btn" onclick="deleteCard('${card.id}')" title="Delete card">✕</button>
        <div style="margin-bottom: 15px; padding-right: 40px;">
          <div style="font-size: 0.8em; color: var(--primary-color); margin-bottom: 5px;">${deckName}</div>
          <div style="font-weight: 600; margin-bottom: 10px; color: var(--text-color);">Front:</div>
          <div style="margin-bottom: 15px; padding: 10px; background: var(--bg-color); border-radius: 8px; min-height: 60px;">${card.front}</div>
          <div style="font-weight: 600; margin-bottom: 10px; color: var(--text-color);">Back:</div>
          <div style="margin-bottom: 15px; padding: 10px; background: var(--bg-color); border-radius: 8px; min-height: 60px;">${card.back}</div>
          ${card.tags && card.tags.length > 0 ? `
            <div style="margin-bottom: 10px;">
              ${card.tags.map(tag => `<span style="background: var(--primary-color); color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8em; margin-right: 5px;">${tag}</span>`).join('')}
            </div>
          ` : ''}
        </div>
        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.9em; color: var(--text-color); opacity: 0.7;">
          <span>Confidence: ${card.confidence || 0}%</span>
          <button class="edit-card-btn" data-card-id="${card.id}" class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8em;">Edit</button>
        </div>
      </div>
    `;
  }).join('');

  // Add event listeners for edit buttons
  container.querySelectorAll('.edit-card-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      const cardId = btn.dataset.cardId;
      const card = app.cards.find(c => c.id === cardId);
      if (card) {
        showRealTimeCardEditor(card);
      }
    });
  });
}

function updateCardCounts() {
  // Update deck card counts in the display
  renderDecks();

  // Update filter dropdown
  const filterSelect = document.getElementById('filter-deck');
  if (filterSelect) {
    const currentValue = filterSelect.value;
    filterSelect.innerHTML = '<option value="">All Decks</option>' +
      app.decks.map(deck => {
        const cardCount = app.cards.filter(card => card.deckId === deck.id).length;
        return `<option value="${deck.id}">${deck.name} (${cardCount} cards)</option>`;
      }).join('');
    filterSelect.value = currentValue;
  }
}

async function deleteCard(cardId) {
  const card = app.cards.find(c => c.id === cardId);
  if (!card) return;

  if (!confirm(`Are you sure you want to delete this card?\n\nFront: ${card.front.substring(0, 50)}${card.front.length > 50 ? '...' : ''}\n\nThis action cannot be undone.`)) {
    return;
  }

  try {
    if (app.currentUser.uid === 'guest' || cardId.startsWith('local_')) {
      app.cards = app.cards.filter(c => c.id !== cardId);
      localStorage.setItem('chunking_cards', JSON.stringify(app.cards));
    } else {
      try {
        await db.collection('cards').doc(cardId).delete();
        app.cards = app.cards.filter(c => c.id !== cardId);
      } catch (firebaseError) {
        console.warn('Firebase delete failed, updating localStorage:', firebaseError);
        const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
        const filteredCards = localCards.filter(c => c.id !== cardId);
        localStorage.setItem('chunking_cards', JSON.stringify(filteredCards));
        app.cards = app.cards.filter(c => c.id !== cardId);
      }
    }

    renderCards();
    updateCardCounts();
    alert('Card deleted successfully!');
  } catch (error) {
    console.error('Error deleting card:', error);
    alert('Error deleting card. Please try again.');
  }
}

// Focus Deck Creation
function createFocusDeck() {
  if (!examState.currentExam || !examState.currentExam.answers) {
    alert('No exam data available');
    return;
  }

  const incorrectAnswers = examState.currentExam.answers.filter(a => a.isCorrect === false);

  if (incorrectAnswers.length === 0) {
    alert('No incorrect answers to create focus deck from!');
    return;
  }

  const cardIds = [...new Set(incorrectAnswers.map(a => a.questionId.replace('_reverse', '')))];
  const focusCards = app.cards.filter(card => cardIds.includes(card.id));

  if (focusCards.length === 0) {
    alert('Unable to find cards for focus deck');
    return;
  }

  const focusDeckName = `${examState.currentExam.name} - Focus Areas`;
  const focusDeckData = {
    name: focusDeckName,
    description: `Auto-generated focus deck from exam mistakes (${focusCards.length} cards)`,
    created: new Date(),
    userId: app.currentUser.uid,
    id: 'local_focus_' + Date.now()
  };

  // Create copies of focus cards for the new deck
  const focusDeckCards = focusCards.map(card => ({
    ...card,
    id: 'local_focus_card_' + Date.now() + '_' + Math.random(),
    deckId: focusDeckData.id,
    created: new Date(),
    confidence: 0,
    correct: 0,
    incorrect: 0
  }));

  // Add deck and cards to app state
  app.decks.push(focusDeckData);
  app.cards.push(...focusDeckCards);

  // Save to storage
  if (app.currentUser.uid === 'guest') {
    localStorage.setItem('chunking_decks', JSON.stringify(app.decks));
    localStorage.setItem('chunking_cards', JSON.stringify(app.cards));
  } else {
    // Save to localStorage as backup
    const localDecks = JSON.parse(localStorage.getItem('chunking_decks') || '[]');
    localDecks.push(focusDeckData);
    localStorage.setItem('chunking_decks', JSON.stringify(localDecks));

    const localCards = JSON.parse(localStorage.getItem('chunking_cards') || '[]');
    localCards.push(...focusDeckCards);
    localStorage.setItem('chunking_cards', JSON.stringify(localCards));
  }

  renderDecks();
  alert(`Focus deck "${focusDeckName}" created with ${focusDeckCards.length} cards! 📚`);
}

function reviewMistakes() {
  if (!examState.currentExam || !examState.currentExam.answers) {
    alert('No exam data available');
    return;
  }

  const incorrectAnswers = examState.currentExam.answers.filter(a => a.isCorrect === false);

  if (incorrectAnswers.length === 0) {
    alert('No mistakes to review - perfect score!');
    return;
  }

  const reviewHtml = incorrectAnswers.map((answer, index) => `
    <div style="margin-bottom: 20px; padding: 20px; border: 1px solid var(--border-color); border-radius: 10px;">
      <div style="font-weight: 600; color: var(--primary-color); margin-bottom: 10px;">
        Question ${index + 1}: ${answer.type === 'flashcard' ? 'Front → Back' : 'Back → Front'}
      </div>
      <div style="margin-bottom: 10px;">
        <strong>Question:</strong> ${answer.question}
      </div>
      <div style="margin-bottom: 10px;">
        <strong>Your Answer:</strong> <span style="color: #ef4444;">${answer.userAnswer || '(No answer provided)'}</span>
      </div>
      <div>
        <strong>Correct Answer:</strong> <span style="color: #10b981;">${answer.correctAnswer}</span>
      </div>
    </div>
  `).join('');

  const reviewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
  reviewWindow.document.write(`
    <html>
      <head><title>Exam Review - ${examState.currentExam.name}</title></head>
      <body style="font-family: Arial, sans-serif; padding: 20px; line-height: 1.6;">
        <h2>📝 Exam Review: ${examState.currentExam.name}</h2>
        <p><strong>Mistakes to Review:</strong> ${incorrectAnswers.length}</p>
        ${reviewHtml}
      </body>
    </html>
  `);
}

function retakeExam() {
  showExamSetup();

  if (examState.currentExam) {
    document.getElementById('exam-name').value = examState.currentExam.name + ' (Retake)';

    examState.currentExam.selectedDecks.forEach(deckId => {
      const checkbox = document.querySelector(`.exam-deck-checkbox[value="${deckId}"]`);
      if (checkbox) checkbox.checked = true;
    });
  }
}

// Account Settings Functions
function showChangePasswordModal() {
  if (app.currentUser.uid === 'guest') {
    alert('Password change is not available for guest accounts.');
    return;
  }
  document.getElementById('change-password-modal').style.display = 'block';
}

function hideChangePasswordModal() {
  document.getElementById('change-password-modal').style.display = 'none';
  document.getElementById('current-password').value = '';
  document.getElementById('new-password').value = '';
  document.getElementById('confirm-new-password').value = '';
}

async function updatePassword() {
  const currentPassword = document.getElementById('current-password').value;
  const newPassword = document.getElementById('new-password').value;
  const confirmPassword = document.getElementById('confirm-new-password').value;

  if (!currentPassword || !newPassword || !confirmPassword) {
    alert('Please fill in all fields');
    return;
  }

  if (newPassword !== confirmPassword) {
    alert('New passwords do not match');
    return;
  }

  if (newPassword.length < 6) {
    alert('New password must be at least 6 characters');
    return;
  }

  try {
    // Re-authenticate user first
    const credential = firebase.auth.EmailAuthProvider.credential(app.currentUser.email, currentPassword);
    await app.currentUser.reauthenticateWithCredential(credential);

    // Update password
    await app.currentUser.updatePassword(newPassword);

    alert('Password updated successfully!');
    hideChangePasswordModal();
  } catch (error) {
    console.error('Password update error:', error);
    if (error.code === 'auth/wrong-password') {
      alert('Current password is incorrect');
    } else {
      alert('Error updating password: ' + error.message);
    }
  }
}

function downloadUserData() {
  const userData = {
    user: {
      email: app.currentUser.email,
      uid: app.currentUser.uid,
      displayName: app.currentUser.displayName
    },
    decks: app.decks,
    cards: app.cards,
    exportDate: new Date().toISOString()
  };

  const dataStr = JSON.stringify(userData, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });

  const link = document.createElement('a');
  link.href = URL.createObjectURL(dataBlob);
  link.download = `chunkymonkey-data-${new Date().toISOString().split('T')[0]}.json`;
  link.click();
}

async function deleteUserAccount() {
  if (app.currentUser.uid === 'guest') {
    alert('Cannot delete guest account.');
    return;
  }

  const confirmation = prompt('This will permanently delete your account and all data. Type "DELETE" to confirm:');
  if (confirmation !== 'DELETE') {
    return;
  }

  const password = prompt('Enter your password to confirm account deletion:');
  if (!password) {
    return;
  }

  try {
    // Re-authenticate user
    const credential = firebase.auth.EmailAuthProvider.credential(app.currentUser.email, password);
    await app.currentUser.reauthenticateWithCredential(credential);

    // Delete user data from Firestore
    const batch = db.batch();

    // Delete all user's cards
    const cardsSnapshot = await db.collection('cards').where('userId', '==', app.currentUser.uid).get();
    cardsSnapshot.forEach(doc => batch.delete(doc.ref));

    // Delete all user's decks
    const decksSnapshot = await db.collection('decks').where('userId', '==', app.currentUser.uid).get();
    decksSnapshot.forEach(doc => batch.delete(doc.ref));

    // Delete user document
    batch.delete(db.collection('users').doc(app.currentUser.uid));

    await batch.commit();

    // Delete the user account
    await app.currentUser.delete();

    alert('Account deleted successfully.');
  } catch (error) {
    console.error('Account deletion error:', error);
    alert('Error deleting account: ' + error.message);
  }
}

// Data Management Functions
function exportAllData() {
  const exportData = {
    decks: app.decks,
    cards: app.cards,
    studyHistory: JSON.parse(localStorage.getItem('chunky_study_history') || '[]'),
    examHistory: JSON.parse(localStorage.getItem('chunky_exam_history') || '[]'),
    settings: {
      theme: localStorage.getItem('chunky_theme'),
      accentColor: localStorage.getItem('chunky_accent_color')
    },
    exportDate: new Date().toISOString(),
    version: '1.0'
  };

  const dataStr = JSON.stringify(exportData, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });

  const link = document.createElement('a');
  link.href = URL.createObjectURL(dataBlob);
  link.download = `chunkymonkey-export-${new Date().toISOString().split('T')[0]}.json`;
  link.click();

  alert('Data exported successfully! 📤');
}

function createBackup() {
  const backupData = {
    decks: app.decks,
    cards: app.cards,
    timestamp: new Date().toISOString()
  };

  const backups = JSON.parse(localStorage.getItem('chunky_backups') || '[]');
  backups.unshift(backupData);

  // Keep only last 5 backups
  if (backups.length > 5) {
    backups.splice(5);
  }

  localStorage.setItem('chunky_backups', JSON.stringify(backups));
  alert('Backup created successfully! 💾');
}

async function syncData() {
  if (app.currentUser.uid === 'guest') {
    alert('Sync is not available for guest accounts. Please sign in to sync your data.');
    return;
  }

  try {
    // Force reload data from Firebase
    await loadFirebaseData();
    renderDecks();
    updateCardCounts();
    alert('Data synced successfully! 🔄');
  } catch (error) {
    console.error('Sync error:', error);
    alert('Error syncing data. Please try again.');
  }
}

function clearCache() {
  if (confirm('This will clear temporary data and refresh the app. Continue?')) {
    // Clear temporary localStorage items
    localStorage.removeItem('chunky_study_history');
    localStorage.removeItem('chunky_exam_history');
    localStorage.removeItem('chunky_backups');

    alert('Cache cleared successfully! 🗑️');
    location.reload();
  }
}

function resetProgress() {
  if (!confirm('This will reset all study progress (confidence levels, review dates) but keep your cards and decks. This action cannot be undone. Continue?')) {
    return;
  }

  const confirmation = prompt('Type "RESET" to confirm:');
  if (confirmation !== 'RESET') {
    return;
  }

  // Reset all card progress
  app.cards.forEach(card => {
    card.confidence = 0;
    card.correct = 0;
    card.incorrect = 0;
    card.lastReviewed = null;
    card.nextReview = new Date();
  });

  // Clear study history
  localStorage.removeItem('chunky_study_history');
  localStorage.removeItem('chunky_exam_history');

  // Save updated cards
  if (app.currentUser.uid === 'guest') {
    localStorage.setItem('chunking_cards', JSON.stringify(app.cards));
  } else {
    // Update Firebase
    app.cards.forEach(async (card) => {
      if (!card.id.startsWith('local_')) {
        try {
          await db.collection('cards').doc(card.id).update({
            confidence: 0,
            correct: 0,
            incorrect: 0,
            lastReviewed: null,
            nextReview: new Date()
          });
        } catch (error) {
          console.error('Error resetting card:', error);
        }
      }
    });
  }

  alert('Progress reset successfully! 🔄');
}

function deleteAllData() {
  if (!confirm('This will permanently delete ALL your data (decks, cards, progress). This action cannot be undone. Continue?')) {
    return;
  }

  const confirmation = prompt('Type "DELETE ALL" to confirm:');
  if (confirmation !== 'DELETE ALL') {
    return;
  }

  // Clear all app data
  app.decks = [];
  app.cards = [];

  // Clear localStorage
  localStorage.removeItem('chunking_decks');
  localStorage.removeItem('chunking_cards');
  localStorage.removeItem('chunky_study_history');
  localStorage.removeItem('chunky_exam_history');
  localStorage.removeItem('chunky_backups');

  // Clear Firebase data if authenticated
  if (app.currentUser.uid !== 'guest') {
    // Note: This would require batch deletion in a real implementation
    alert('Firebase data deletion requires manual cleanup. Local data cleared.');
  }

  // Refresh UI
  renderDecks();
  updateCardCounts();

  alert('All data deleted successfully! 🗑️');
}

// Notification Functions
function saveNotificationSettings() {
  const email = document.getElementById('notification-email').value.trim();
  const dailyReminder = document.getElementById('daily-reminder-setting').checked;
  const weeklyProgress = document.getElementById('weekly-progress-setting').checked;
  const achievements = document.getElementById('achievement-notifications-setting').checked;
  const reminderTime = document.getElementById('reminder-time').value;

  if (email && !isValidEmail(email)) {
    alert('Please enter a valid email address.');
    return;
  }

  const notificationSettings = {
    email: email,
    dailyReminder: dailyReminder,
    weeklyProgress: weeklyProgress,
    achievements: achievements,
    reminderTime: reminderTime,
    savedAt: new Date().toISOString()
  };

  localStorage.setItem('chunky_notification_settings', JSON.stringify(notificationSettings));

  if (email) {
    // In a real implementation, this would send the settings to a backend service
    alert('📧 Notification settings saved! Email notifications will be sent to ' + email);

    // Simulate sending a test email
    setTimeout(() => {
      alert('📬 Test email sent! Check your inbox for a welcome message from ChunkyMonkey.');
    }, 1000);
  } else {
    alert('⚙️ Notification settings saved locally!');
  }
}

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Analytics Functions
function updateAnalytics() {
  // Update total counts
  document.getElementById('total-decks').textContent = app.decks.length;
  document.getElementById('total-cards').textContent = app.cards.length;

  // Calculate average confidence
  const cardsWithConfidence = app.cards.filter(card => card.confidence > 0);
  const avgConfidence = cardsWithConfidence.length > 0
    ? Math.round(cardsWithConfidence.reduce((sum, card) => sum + card.confidence, 0) / cardsWithConfidence.length)
    : 0;
  document.getElementById('avg-confidence').textContent = `${avgConfidence}%`;

  // Calculate study streak
  const studyHistory = JSON.parse(localStorage.getItem('chunky_study_history') || '[]');
  let streak = 0;
  const today = new Date();

  for (let i = 0; i < studyHistory.length; i++) {
    const sessionDate = new Date(studyHistory[i].date);
    const daysDiff = Math.floor((today - sessionDate) / (1000 * 60 * 60 * 24));

    if (daysDiff === streak) {
      streak++;
    } else {
      break;
    }
  }

  document.getElementById('study-streak').textContent = streak;

  // Update deck performance
  updateDeckPerformance();

  // Update recent activity
  updateRecentActivity();
}

function updateDeckPerformance() {
  const container = document.getElementById('deck-performance-list');

  if (app.decks.length === 0) {
    container.innerHTML = '<p style="text-align: center; color: var(--text-color); opacity: 0.7;">No decks available.</p>';
    return;
  }

  const deckStats = app.decks.map(deck => {
    const deckCards = app.cards.filter(card => card.deckId === deck.id);
    const totalCards = deckCards.length;
    const studiedCards = deckCards.filter(card => card.lastReviewed).length;
    const avgConfidence = deckCards.length > 0
      ? Math.round(deckCards.reduce((sum, card) => sum + (card.confidence || 0), 0) / deckCards.length)
      : 0;

    return {
      name: deck.name,
      totalCards,
      studiedCards,
      avgConfidence,
      progress: totalCards > 0 ? Math.round((studiedCards / totalCards) * 100) : 0
    };
  });

  container.innerHTML = deckStats.map(stat => `
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; border: 1px solid var(--border-color); border-radius: 10px; margin-bottom: 10px;">
      <div>
        <div style="font-weight: 600; color: var(--text-color);">${stat.name}</div>
        <div style="font-size: 0.9em; color: var(--text-color); opacity: 0.7;">${stat.studiedCards}/${stat.totalCards} cards studied</div>
      </div>
      <div style="text-align: right;">
        <div style="font-size: 1.1em; font-weight: 600; color: var(--primary-color);">${stat.avgConfidence}%</div>
        <div style="font-size: 0.8em; color: var(--text-color); opacity: 0.7;">confidence</div>
      </div>
    </div>
  `).join('');
}

function updateRecentActivity() {
  const container = document.getElementById('recent-activity-list');
  const studyHistory = JSON.parse(localStorage.getItem('chunky_study_history') || '[]');

  if (studyHistory.length === 0) {
    container.innerHTML = '<p style="text-align: center; color: var(--text-color); opacity: 0.7;">No recent activity. Start studying to track your progress!</p>';
    return;
  }

  container.innerHTML = studyHistory.slice(0, 5).map(session => {
    const date = new Date(session.date).toLocaleDateString();
    const time = new Date(session.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    const accuracy = session.cardsStudied > 0 ? Math.min(100, Math.round((session.correct / session.cardsStudied) * 100)) : 0;

    return `
      <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; border: 1px solid var(--border-color); border-radius: 10px; margin-bottom: 10px;">
        <div>
          <div style="font-weight: 600; color: var(--text-color);">${session.type} Study Session</div>
          <div style="font-size: 0.9em; color: var(--text-color); opacity: 0.7;">${date} at ${time}</div>
        </div>
        <div style="text-align: right;">
          <div style="font-size: 1.1em; font-weight: 600; color: var(--primary-color);">${accuracy}% Accuracy</div>
          <div style="font-size: 0.8em; color: var(--text-color); opacity: 0.7;">${session.correct}/${session.cardsStudied} correct</div>
        </div>
      </div>
    `;
  }).join('');
}

// Initialize analytics when tab is shown
document.querySelector('[data-tab="analytics"]').addEventListener('click', () => {
  setTimeout(updateAnalytics, 100);
});
</script>
</body>
</html>
