<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>How to Help a Friend or Family Member with Addiction: A Comprehensive Support Guide</title>
    <meta name="description" content="Learn how to support a friend or family member struggling with addiction. Practical advice for siblings, parents, children, and friends on providing effective help without enabling.">
    <meta name="keywords" content="helping friend with addiction, family member addiction support, how to help addicted relative, addiction intervention family, supporting loved one recovery">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #004824;
            --primary-light: #0a5e32;
            --primary-dark: #003018;
            --accent: #a8763e;
            --accent-dark: #8f6235;
            --accent-light: #c4924f;
            --light-green: #e6eed6;
            --emerald: #2ecc71;
            --sage: #95a5a6;
            --cream: #fdfcf8;
            --pearl: #f8f9fa;
            --shadow: #1a1a1a;
            --text-primary: #1a1a1a;
            --text-secondary: #4a5568;
            --text-light: #718096;
            --white: #ffffff;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            --backdrop-blur: blur(16px);
            --radius-xs: 6px;
            --radius-sm: 12px;
            --radius-md: 20px;
            --radius-lg: 32px;
            --radius-xl: 48px;
            --shadow-xs: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
            --shadow-sm: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
            --shadow-md: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
            --shadow-lg: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
            --shadow-xl: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);
            --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            --gradient-accent: linear-gradient(135deg, var(--accent) 0%, var(--accent-dark) 50%, var(--accent-light) 100%);
            --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
            --gradient-hero: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 50%, var(--primary-light) 100%);
            --gradient-subtle: linear-gradient(135deg, var(--light-green) 0%, var(--pearl) 100%);
            --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            --transition-spring: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: clamp(1rem, 1.2vw, 1.125rem);
            line-height: 1.75;
            color: var(--text-primary);
            font-weight: 400;
            background:
                    radial-gradient(circle at 25% 25%, rgba(0, 72, 36, 0.03) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(168, 118, 62, 0.04) 0%, transparent 50%),
                    radial-gradient(circle at 50% 0%, rgba(230, 238, 214, 0.02) 0%, transparent 50%),
                    var(--gradient-subtle);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        a {
            color: var(--primary);
            text-decoration: none;
            transition: var(--transition-smooth);
            position: relative;
        }

        a:hover {
            color: var(--accent);
        }

        .container {
            width: min(1400px, 92%);
            margin-inline: auto;
            position: relative;
        }

        /* Hero Section */
        .hero {
            background: var(--gradient-hero);
            color: var(--white);
            text-align: center;
            padding: clamp(3rem, 8vw, 8rem) clamp(2rem, 5vw, 4rem);
            max-width: 1600px;
            margin: clamp(2rem, 5vw, 4rem) auto;
            border-radius: var(--radius-xl);
            font-family: 'Plus Jakarta Sans', sans-serif;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
            backdrop-filter: var(--backdrop-blur);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .hero::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background:
                    radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%),
                    linear-gradient(45deg, transparent 30%, rgba(168, 118, 62, 0.1) 50%, transparent 70%);
            animation: heroFloat 12s ease-in-out infinite;
            z-index: 1;
        }

        .hero::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                    radial-gradient(circle at 70% 30%, rgba(230, 238, 214, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 30% 70%, rgba(168, 118, 62, 0.08) 0%, transparent 50%);
            animation: heroFloat 15s ease-in-out infinite reverse;
            z-index: 1;
        }

        @keyframes heroFloat {
            0%, 100% {
                transform: translate(-50%, -50%) rotate(0deg) scale(1);
                opacity: 0.8;
            }
            33% {
                transform: translate(-45%, -55%) rotate(120deg) scale(1.1);
                opacity: 1;
            }
            66% {
                transform: translate(-55%, -45%) rotate(240deg) scale(0.9);
                opacity: 0.9;
            }
        }

        .hero-content {
            position: relative;
            z-index: 10;
        }

        .hero h1 {
            font-weight: 800;
            margin-bottom: 1rem;
            color: var(--white);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: titleGlow 3s ease-in-out infinite alternate;
            font-size: clamp(2rem, 5vw, 3.5rem);
        }

        @keyframes titleGlow {
            0% {
                filter: drop-shadow(0 0 10px rgba(168, 118, 62, 0.3));
            }
            100% {
                filter: drop-shadow(0 0 20px rgba(168, 118, 62, 0.6));
            }
        }

        .hero-underline {
            width: 80px;
            height: 4px;
            background: var(--gradient-accent);
            margin: 0 auto 2rem;
            border-radius: var(--radius-xs);
            box-shadow: 0 4px 15px rgba(168, 118, 62, 0.4);
            animation: underlinePulse 2s ease-in-out infinite alternate;
        }

        @keyframes underlinePulse {
            0% {
                transform: scaleX(1);
                box-shadow: 0 4px 15px rgba(168, 118, 62, 0.4);
            }
            100% {
                transform: scaleX(1.2);
                box-shadow: 0 6px 25px rgba(168, 118, 62, 0.7);
            }
        }

        .hero p {
            font-size: clamp(1.1rem, 2vw, 1.4rem);
            max-width: 800px;
            margin: 0 auto 3rem;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.95);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            font-weight: 400;
        }

        .hero a {
            color: var(--accent-light);
            font-weight: 600;
            text-decoration: underline;
        }

        .hero a:hover {
            color: var(--white);
        }

        /* Navigation Pills */
        .nav-section {
            margin: clamp(3rem, 6vw, 6rem) 0;
        }

        .nav-pills {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            justify-content: center;
            margin-bottom: 4rem;
            padding: 3rem;
            background: var(--glass-bg);
            backdrop-filter: var(--backdrop-blur);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-lg);
            box-shadow: var(--glass-shadow);
            position: relative;
            overflow: hidden;
        }

        .nav-pill {
            padding: 1rem 2rem;
            background: rgba(255, 255, 255, 0.9);
            color: var(--primary);
            text-decoration: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            font-size: 0.95rem;
            transition: var(--transition-bounce);
            border: 2px solid transparent;
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }

        .nav-pill:hover, .nav-pill.active {
            background: var(--primary);
            color: var(--white);
            transform: translateY(-4px) scale(1.05);
            box-shadow: var(--shadow-lg);
            border-color: var(--accent);
        }

        /* Content Sections */
        .content-section {
            background: var(--glass-bg);
            backdrop-filter: var(--backdrop-blur);
            border: 1px solid var(--glass-border);
            margin: clamp(3rem, 6vw, 6rem) 0;
            padding: clamp(3rem, 6vw, 6rem) clamp(2rem, 4vw, 4rem);
            border-radius: var(--radius-xl);
            box-shadow: var(--glass-shadow);
            position: relative;
            overflow: hidden;
        }

        .section-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 800;
            color: var(--primary);
            text-align: center;
            margin-bottom: 1rem;
            font-family: 'Plus Jakarta Sans', sans-serif;
            position: relative;
        }

        .section-underline {
            width: 80px;
            height: 4px;
            background: var(--gradient-accent);
            margin: 0 auto 3rem;
            border-radius: var(--radius-xs);
            box-shadow: 0 4px 15px rgba(212, 165, 116, 0.4);
        }

        .section-subtitle {
            font-size: clamp(1.1rem, 2vw, 1.3rem);
            color: var(--text-secondary);
            text-align: center;
            max-width: 700px;
            margin: 0 auto 3rem;
            line-height: 1.6;
        }

        /* Card Styles */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .info-card {
            background: var(--glass-bg);
            backdrop-filter: var(--backdrop-blur);
            border-radius: var(--radius-lg);
            padding: 3rem;
            box-shadow: var(--glass-shadow);
            border: 1px solid var(--glass-border);
            transition: var(--transition-spring);
            position: relative;
            overflow: hidden;
        }

        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--gradient-accent);
            box-shadow: 0 4px 15px rgba(212, 165, 116, 0.4);
        }

        .info-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: var(--shadow-xl);
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 1.5rem;
            display: block;
            text-align: center;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 1.5rem;
            font-family: 'Plus Jakarta Sans', sans-serif;
            text-align: center;
        }

        /* Alert Boxes */
        .alert {
            padding: 2rem;
            border-radius: var(--radius-lg);
            margin: 2rem 0;
            border-left: 5px solid;
            backdrop-filter: var(--backdrop-blur);
            position: relative;
        }

        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            border-color: var(--primary);
            color: var(--primary-dark);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-color: var(--gold);
            color: #92400e;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border-color: var(--emerald);
            color: #065f46;
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #991b1b;
        }

        /* Lists */
        .styled-list {
            list-style: none;
            padding-left: 0;
        }

        .styled-list li {
            padding: 0.75rem 0;
            padding-left: 2rem;
            position: relative;
            line-height: 1.6;
        }

        .styled-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--emerald);
            font-weight: bold;
            font-size: 1.2rem;
        }

        /* Emergency Contact Box */
        .emergency-box {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c2c7 50%, #f1aeb5 100%);
            color: #721c24;
            padding: 3rem;
            border-radius: var(--radius-xl);
            text-align: center;
            margin: 3rem 0;
            box-shadow: var(--shadow-lg);
            border: 2px solid #f5c2c7;
            position: relative;
            overflow: hidden;
        }

        .emergency-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 6px;
            background: linear-gradient(90deg, #dc3545, #e74c3c, #dc3545);
            animation: emergencyPulse 2s ease-in-out infinite;
        }

        @keyframes emergencyPulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        .emergency-box h3 {
            font-size: 2rem;
            margin-bottom: 1.5rem;
            font-weight: 800;
            color: #721c24;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .emergency-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .emergency-item {
            background: rgba(255, 255, 255, 0.8);
            padding: 1.5rem;
            border-radius: var(--radius-lg);
            border: 1px solid rgba(220, 53, 69, 0.2);
            transition: var(--transition-smooth);
        }

        .emergency-item:hover {
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .emergency-item h4 {
            color: #721c24;
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .emergency-box a {
            color: #721c24;
            font-weight: bold;
            text-decoration: none;
            font-size: 1.3rem;
            border-bottom: 2px solid transparent;
            transition: var(--transition-smooth);
        }

        .emergency-box a:hover {
            color: #dc3545;
            border-bottom-color: #dc3545;
        }

        /* CTA Section */
        .cta-section {
            background: var(--gradient-hero);
            color: var(--white);
            padding: clamp(4rem, 8vw, 8rem) clamp(3rem, 6vw, 5rem);
            border-radius: var(--radius-xl);
            text-align: center;
            box-shadow: var(--shadow-xl);
            margin: clamp(4rem, 8vw, 8rem) 0;
            position: relative;
            overflow: hidden;
            backdrop-filter: var(--backdrop-blur);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .cta-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background:
                    radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
                    linear-gradient(45deg, transparent 40%, rgba(168, 118, 62, 0.15) 50%, transparent 60%);
            animation: ctaPulse 8s ease-in-out infinite;
            z-index: 1;
        }

        .cta-section::after {
            content: '';
            position: absolute;
            top: 20%;
            right: 15%;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(168, 118, 62, 0.12) 0%, transparent 70%);
            border-radius: 50%;
            animation: ctaFloat 10s ease-in-out infinite reverse;
            z-index: 1;
        }

        @keyframes ctaPulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1) rotate(0deg);
                opacity: 0.4;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
                opacity: 0.8;
            }
        }

        @keyframes ctaFloat {
            0%, 100% {
                transform: translateY(0) scale(1);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-30px) scale(1.2);
                opacity: 1;
            }
        }

        .cta-content {
            position: relative;
            z-index: 10;
        }

        .cta-section h2 {
            color: var(--white);
            font-size: clamp(2.5rem, 5vw, 4rem);
            margin-bottom: 2rem;
            font-weight: 800;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
        }

        .cta-section p {
            max-width: 850px;
            margin: 0 auto 4rem;
            font-size: 1.4rem;
            opacity: 0.95;
            line-height: 1.8;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
            color: rgba(255, 255, 255, 0.95);
        }

        .cta-section p a {
            color: var(--accent-light);
            font-weight: 600;
            text-decoration: underline;
        }

        .cta-section p a:hover {
            color: var(--white);
        }

        .cta-buttons {
            display: flex;
            justify-content: center;
            gap: 2.5rem;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            font-weight: 700;
            padding: 1.6rem 3.5rem;
            border-radius: 50px;
            transition: var(--transition-bounce);
            cursor: pointer;
            border: none;
            font-family: inherit;
            font-size: 1.15rem;
            white-space: nowrap;
            position: relative;
            box-shadow: var(--shadow-lg);
            transform: translateY(0) scale(1);
            overflow: hidden;
            letter-spacing: 0.5px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: var(--transition-smooth);
        }

        .btn:hover::before {
            width: 400px;
            height: 400px;
        }

        .btn--primary {
            background: var(--gradient-accent);
            color: var(--white);
            border: 3px solid rgba(255, 255, 255, 0.2);
        }

        .btn--primary:hover {
            transform: translateY(-8px) scale(1.08);
            box-shadow: var(--shadow-xl);
            color: var(--white);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .btn--secondary {
            background: rgba(255, 255, 255, 0.95);
            color: var(--primary);
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .btn--secondary:hover {
            transform: translateY(-8px) scale(1.08);
            box-shadow: var(--shadow-xl);
            color: var(--primary);
            background: rgba(255, 255, 255, 1);
        }

        .btn:active {
            transform: translateY(-4px) scale(1.04);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-pills {
                justify-content: flex-start;
                overflow-x: auto;
                padding-bottom: 1rem;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }

            .content-section {
                padding: 2rem 1rem;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
                gap: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Supporting Your Loved One Through Addiction</h1>
                <div class="hero-underline"></div>
                <p>A comprehensive guide for family members and friends who want to help someone they care about overcome addiction while protecting their own well-being</p>
            </div>
        </div>
    </section>

    <!-- Navigation -->
    <nav class="nav-section">
        <div class="container">
            <div class="nav-pills">
                <a href="#understanding" class="nav-pill">Understanding Addiction</a>
                <a href="#recognizing-signs" class="nav-pill">Warning Signs</a>
                <a href="#how-to-help" class="nav-pill">How to Help</a>
                <a href="#communication" class="nav-pill">Communication Tips</a>
                <a href="#boundaries" class="nav-pill">Setting Boundaries</a>
                <a href="#intervention" class="nav-pill">Intervention Guide</a>
                <a href="#self-care" class="nav-pill">Self-Care</a>
                <a href="#resources" class="nav-pill">Resources</a>
            </div>
        </div>
    </nav>

    <!-- Emergency Help Section -->
    <section id="immediate-help" class="container">
        <div class="emergency-box">
            <h3>🚨 Need Immediate Help?</h3>
            <p>If your loved one is in crisis or you need immediate support, these resources are available 24/7:</p>

            <div class="emergency-content">
                <div class="emergency-item">
                    <h4>🆘 Crisis Support</h4>
                    <p><strong>Crisis Hotline:</strong><br>
                    <a href="tel:988">988</a><br>
                    <small>Suicide & Crisis Lifeline</small></p>
                </div>

                <div class="emergency-item">
                    <h4>👨‍👩‍👧‍👦 Family Support</h4>
                    <p><strong>Al-Anon Family Groups:</strong><br>
                    <a href="tel:**************">**************</a><br>
                    <small>Support for families affected by alcoholism</small></p>
                </div>

                <div class="emergency-item">
                    <h4>🤝 Addiction Family Support</h4>
                    <p><strong>Nar-Anon Family Groups:</strong><br>
                    <a href="tel:**************">**************</a><br>
                    <small>Support for families affected by drug addiction</small></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Understanding Addiction Section -->
    <section id="understanding" class="content-section">
        <div class="container">
            <h2 class="section-title">Understanding Addiction from a Family Perspective</h2>
            <div class="section-underline"></div>
            <p class="section-subtitle">Knowledge is power when it comes to supporting someone with addiction. Understanding the nature of addiction helps you respond with compassion rather than judgment.</p>

            <div class="card-grid">
                <div class="info-card">
                    <span class="card-icon">🧠</span>
                    <h3 class="card-title">It's a Brain Disease</h3>
                    <p>Addiction fundamentally changes brain structure and function, affecting areas responsible for decision-making, impulse control, and judgment. This isn't about weakness or moral failure—it's about brain chemistry.</p>
                </div>

                <div class="info-card">
                    <span class="card-icon">🔄</span>
                    <h3 class="card-title">It's Chronic but Treatable</h3>
                    <p>Like diabetes or heart disease, addiction is a chronic condition that requires ongoing management. Recovery is absolutely possible with proper treatment, support, and commitment.</p>
                </div>

                <div class="info-card">
                    <span class="card-icon">👥</span>
                    <h3 class="card-title">It Affects Everyone</h3>
                    <p>Addiction impacts the entire family system. Parents, siblings, children, and close friends all experience stress, confusion, and emotional pain when someone they love struggles with addiction.</p>
                </div>

                <div class="info-card">
                    <span class="card-icon">💪</span>
                    <h3 class="card-title">Recovery is Possible</h3>
                    <p>With proper support, treatment, and commitment, people can and do recover from addiction. Your role as a supportive family member or friend can be a crucial part of their recovery journey.</p>
                </div>
            </div>

            <div class="alert alert-info">
                <h4><strong>Remember the 3 C's:</strong></h4>
                <ul class="styled-list">
                    <li><strong>You didn't CAUSE it</strong> - Addiction has complex roots including genetics, trauma, and brain chemistry</li>
                    <li><strong>You can't CONTROL it</strong> - Only the person with addiction can choose recovery</li>
                    <li><strong>You can't CURE it</strong> - Professional treatment and ongoing support are typically necessary</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- Recognizing Signs Section -->
    <section id="recognizing-signs" class="content-section">
        <div class="container">
            <h2 class="section-title">Recognizing the Warning Signs</h2>
            <div class="section-underline"></div>
            <p class="section-subtitle">Early recognition can make a significant difference. As someone close to them, you may notice changes before they do.</p>

            <div class="card-grid">
                <div class="info-card">
                    <span class="card-icon">👁️</span>
                    <h3 class="card-title">Behavioral Changes</h3>
                    <ul class="styled-list">
                        <li>Withdrawing from family activities and social events</li>
                        <li>Lying about whereabouts or activities</li>
                        <li>Neglecting work, school, or family responsibilities</li>
                        <li>Sudden changes in friend groups</li>
                        <li>Secretive phone calls or text messages</li>
                        <li>Unexplained absences or disappearances</li>
                    </ul>
                </div>

                <div class="info-card">
                    <span class="card-icon">💰</span>
                    <h3 class="card-title">Financial Red Flags</h3>
                    <ul class="styled-list">
                        <li>Frequent requests for money with vague explanations</li>
                        <li>Missing money, valuables, or prescription medications</li>
                        <li>Unexplained financial problems or debt</li>
                        <li>Selling personal belongings</li>
                        <li>Borrowing money from multiple sources</li>
                    </ul>
                </div>

                <div class="info-card">
                    <span class="card-icon">🏥</span>
                    <h3 class="card-title">Physical & Emotional Signs</h3>
                    <ul class="styled-list">
                        <li>Changes in appearance or personal hygiene</li>
                        <li>Unexplained weight loss or gain</li>
                        <li>Bloodshot eyes, dilated pupils, or tremors</li>
                        <li>Extreme mood swings or personality changes</li>
                        <li>Increased anxiety, depression, or irritability</li>
                        <li>Sleep pattern disruptions</li>
                    </ul>
                </div>

                <div class="info-card">
                    <span class="card-icon">⚠️</span>
                    <h3 class="card-title">Relationship Warning Signs</h3>
                    <ul class="styled-list">
                        <li>Becoming defensive when questioned about behavior</li>
                        <li>Blaming others for their problems or circumstances</li>
                        <li>Making promises they repeatedly break</li>
                        <li>Manipulating family members to get what they want</li>
                        <li>Creating drama or crises to distract from their addiction</li>
                        <li>Isolating from family members who express concern</li>
                    </ul>
                </div>
            </div>

            <div class="alert alert-warning">
                <h4><strong>Trust Your Instincts</strong></h4>
                <p>If something feels "off" about your loved one's behavior, don't ignore it. While these signs don't automatically mean addiction, they warrant attention and possibly professional evaluation.</p>
            </div>
        </div>
    </section>

    <!-- How to Help Section -->
    <section id="how-to-help" class="content-section">
        <div class="container">
            <h2 class="section-title">How to Help Without Enabling</h2>
            <div class="section-underline"></div>
            <p class="section-subtitle">The line between helping and enabling can be confusing. Learn how to provide support that encourages recovery rather than perpetuating addiction.</p>

            <div class="card-grid">
                <div class="info-card">
                    <span class="card-icon">✅</span>
                    <h3 class="card-title">Helpful Actions</h3>
                    <ul class="styled-list">
                        <li>Research treatment options and have information ready</li>
                        <li>Offer to drive them to appointments or meetings</li>
                        <li>Learn about addiction and recovery processes</li>
                        <li>Attend family therapy sessions when invited</li>
                        <li>Celebrate recovery milestones and achievements</li>
                        <li>Maintain hope and express your love consistently</li>
                        <li>Connect with other families in similar situations</li>
                    </ul>
                </div>

                <div class="info-card">
                    <span class="card-icon">❌</span>
                    <h3 class="card-title">Enabling Behaviors to Avoid</h3>
                    <ul class="styled-list">
                        <li>Giving money without knowing how it will be used</li>
                        <li>Making excuses for their behavior to others</li>
                        <li>Covering up consequences of their addiction</li>
                        <li>Lying to protect them from natural consequences</li>
                        <li>Doing things they should be doing for themselves</li>
                        <li>Ignoring the problem and hoping it goes away</li>
                        <li>Threatening consequences you won't follow through on</li>
                    </ul>
                </div>
            </div>

            <div class="alert alert-success">
                <h4><strong>The Goal is Recovery, Not Comfort</strong></h4>
                <p>Sometimes the most loving thing you can do is allow your loved one to experience the natural consequences of their choices. This can be incredibly difficult, but it's often necessary for them to recognize they need help.</p>
            </div>
        </div>
    </section>

    <!-- Communication Section -->
    <section id="communication" class="content-section">
        <div class="container">
            <h2 class="section-title">Effective Communication Strategies</h2>
            <div class="section-underline"></div>
            <p class="section-subtitle">How you communicate can significantly impact your relationship and their willingness to seek help.</p>

            <div class="card-grid">
                <div class="info-card">
                    <span class="card-icon">💬</span>
                    <h3 class="card-title">Communication Do's</h3>
                    <ul class="styled-list">
                        <li>Use "I" statements: "I feel worried when..." instead of "You always..."</li>
                        <li>Choose the right time - when they're sober and calm</li>
                        <li>Listen without judgment or immediate advice</li>
                        <li>Express love while addressing the behavior</li>
                        <li>Be specific about concerning behaviors you've observed</li>
                        <li>Stay calm and avoid emotional outbursts</li>
                        <li>Focus on how their addiction affects you and the family</li>
                    </ul>
                </div>

                <div class="info-card">
                    <span class="card-icon">🚫</span>
                    <h3 class="card-title">Communication Don'ts</h3>
                    <ul class="styled-list">
                        <li>Don't lecture, preach, or give long speeches</li>
                        <li>Don't argue with them when they're under the influence</li>
                        <li>Don't use labels like "addict" or "junkie"</li>
                        <li>Don't make threats you're not prepared to follow through on</li>
                        <li>Don't bring up past mistakes during current discussions</li>
                        <li>Don't try to reason with them when they're intoxicated</li>
                        <li>Don't take their words personally when they're using</li>
                    </ul>
                </div>
            </div>

            <div class="alert alert-info">
                <h4><strong>Sample Conversation Starter:</strong></h4>
                <p>"I love you and I'm concerned about some changes I've noticed lately. I've seen [specific behavior], and I'm worried about your health and well-being. I want to support you in getting help if you're ready. Can we talk about this?"</p>
            </div>
        </div>
    </section>

    <!-- Boundaries Section -->
    <section id="boundaries" class="content-section">
        <div class="container">
            <h2 class="section-title">Setting and Maintaining Healthy Boundaries</h2>
            <div class="section-underline"></div>
            <p class="section-subtitle">Boundaries protect both you and your loved one. They're not punishments—they're necessary limits that promote healing.</p>

            <div class="card-grid">
                <div class="info-card">
                    <span class="card-icon">🛡️</span>
                    <h3 class="card-title">Financial Boundaries</h3>
                    <ul class="styled-list">
                        <li>Don't give cash or access to credit cards</li>
                        <li>Pay bills directly instead of giving money</li>
                        <li>Remove them from joint accounts if necessary</li>
                        <li>Don't pay legal fees related to addiction</li>
                        <li>Secure valuables and important documents</li>
                        <li>Consider changing locks if they've stolen from you</li>
                    </ul>
                </div>

                <div class="info-card">
                    <span class="card-icon">🏠</span>
                    <h3 class="card-title">Home & Safety Boundaries</h3>
                    <ul class="styled-list">
                        <li>No drug or alcohol use in your home</li>
                        <li>No bringing drug-using friends to your home</li>
                        <li>They must be sober to visit or stay</li>
                        <li>No verbal or physical abuse will be tolerated</li>
                        <li>Children's safety comes first always</li>
                        <li>Set specific consequences for boundary violations</li>
                    </ul>
                </div>

                <div class="info-card">
                    <span class="card-icon">❤️</span>
                    <h3 class="card-title">Emotional Boundaries</h3>
                    <ul class="styled-list">
                        <li>You won't listen to manipulation or guilt trips</li>
                        <li>You won't be responsible for their emotions</li>
                        <li>You won't keep their addiction secret from other family</li>
                        <li>You won't cancel your plans to deal with their crises</li>
                        <li>You won't accept verbal abuse or disrespect</li>
                        <li>You have the right to your own feelings and needs</li>
                    </ul>
                </div>

                <div class="info-card">
                    <span class="card-icon">⏰</span>
                    <h3 class="card-title">Time & Energy Boundaries</h3>
                    <ul class="styled-list">
                        <li>Set specific times when you're available to talk</li>
                        <li>Don't drop everything for every crisis they create</li>
                        <li>Limit how much time you spend discussing their addiction</li>
                        <li>You have the right to say "no" to unreasonable requests</li>
                        <li>Don't sacrifice your own commitments for their problems</li>
                        <li>Take breaks from being their primary support person</li>
                    </ul>
                </div>
            </div>

            <div class="alert alert-warning">
                <h4><strong>Boundaries Require Consistency</strong></h4>
                <p>Setting boundaries is only effective if you consistently enforce them. Be prepared to follow through on consequences, even when it's difficult. Inconsistent boundaries often make the situation worse.</p>
            </div>
        </div>
    </section>

    <!-- Self-Care Section -->
    <section id="self-care" class="content-section">
        <div class="container">
            <h2 class="section-title">Taking Care of Yourself</h2>
            <div class="section-underline"></div>
            <p class="section-subtitle">You can't pour from an empty cup. Taking care of your own physical, emotional, and mental health is essential for everyone involved.</p>

            <div class="card-grid">
                <div class="info-card">
                    <span class="card-icon">🧘</span>
                    <h3 class="card-title">Emotional Self-Care</h3>
                    <ul class="styled-list">
                        <li>Join a support group like Al-Anon or Nar-Anon</li>
                        <li>Consider individual therapy or counseling</li>
                        <li>Practice stress-reduction techniques (meditation, yoga)</li>
                        <li>Keep a journal to process your emotions</li>
                        <li>Allow yourself to feel angry, sad, or frustrated</li>
                        <li>Don't isolate yourself from friends and activities</li>
                    </ul>
                </div>

                <div class="info-card">
                    <span class="card-icon">💪</span>
                    <h3 class="card-title">Physical Self-Care</h3>
                    <ul class="styled-list">
                        <li>Maintain regular exercise and physical activity</li>
                        <li>Eat nutritious meals and stay hydrated</li>
                        <li>Get adequate sleep (7-9 hours per night)</li>
                        <li>Limit alcohol and avoid using substances to cope</li>
                        <li>Schedule regular medical check-ups</li>
                        <li>Take breaks and practice relaxation</li>
                    </ul>
                </div>

                <div class="info-card">
                    <span class="card-icon">🌱</span>
                    <h3 class="card-title">Practical Self-Care</h3>
                    <ul class="styled-list">
                        <li>Maintain your own interests and hobbies</li>
                        <li>Set aside time for activities you enjoy</li>
                        <li>Keep up with work and other responsibilities</li>
                        <li>Nurture relationships with supportive people</li>
                        <li>Learn about addiction and recovery</li>
                        <li>Create a safety plan for crisis situations</li>
                    </ul>
                </div>

                <div class="info-card">
                    <span class="card-icon">🛡️</span>
                    <h3 class="card-title">Protective Self-Care</h3>
                    <ul class="styled-list">
                        <li>Set clear boundaries about what you will and won't do</li>
                        <li>Don't take responsibility for their recovery</li>
                        <li>Protect your financial security and assets</li>
                        <li>Know when to step back and let professionals help</li>
                        <li>Have a support network you can rely on</li>
                        <li>Remember that you can't save someone who doesn't want to be saved</li>
                    </ul>
                </div>
            </div>

            <div class="alert alert-success">
                <h4><strong>Self-Care Isn't Selfish</strong></h4>
                <p>Taking care of yourself isn't abandoning your loved one—it's ensuring you have the strength and clarity to provide appropriate support over the long term. You matter too.</p>
            </div>
        </div>
    </section>

    <!-- Resources Section -->
    <section id="resources" class="content-section">
        <div class="container">
            <h2 class="section-title">Support Resources for Families</h2>
            <div class="section-underline"></div>
            <p class="section-subtitle">You don't have to navigate this journey alone. These resources provide support, education, and guidance for families affected by addiction.</p>

            <div class="card-grid">
                <div class="info-card">
                    <span class="card-icon">👥</span>
                    <h3 class="card-title">Family Support Groups</h3>
                    <p><strong>Al-Anon Family Groups</strong><br>
                    For families affected by alcoholism<br>
                    Phone: <a href="tel:**************">**************</a><br>
                    Website: <a href="https://al-anon.org" target="_blank">al-anon.org</a></p>

                    <p><strong>Nar-Anon Family Groups</strong><br>
                    For families affected by drug addiction<br>
                    Phone: <a href="tel:**************">**************</a><br>
                    Website: <a href="https://nar-anon.org" target="_blank">nar-anon.org</a></p>
                </div>

                <div class="info-card">
                    <span class="card-icon">📞</span>
                    <h3 class="card-title">Crisis & Information Lines</h3>
                    <p><strong>SAMHSA National Helpline</strong><br>
                    24/7 treatment referral service<br>
                    Phone: <a href="tel:**************">1-800-662-HELP (4357)</a></p>

                    <p><strong>Crisis Text Line</strong><br>
                    Text HOME to <a href="sms:741741">741741</a></p>

                    <p><strong>National Suicide Prevention Lifeline</strong><br>
                    Phone: <a href="tel:988">988</a></p>
                </div>

                <div class="info-card">
                    <span class="card-icon">🏥</span>
                    <h3 class="card-title">Professional Treatment</h3>
                    <p><strong>Williamsville Wellness</strong><br>
                    Comprehensive addiction treatment with family support services<br>
                    Phone: <a href="tel:8046550094">(*************</a></p>

                    <p>We offer family therapy, education programs, and ongoing support to help families heal together during the recovery process.</p>
                </div>

                <div class="info-card">
                    <span class="card-icon">📚</span>
                    <h3 class="card-title">Educational Resources</h3>
                    <p><strong>Partnership to End Addiction</strong><br>
                    Comprehensive family resources and guides<br>
                    Website: <a href="https://drugfree.org" target="_blank">drugfree.org</a></p>

                    <p><strong>SMART Recovery Family & Friends</strong><br>
                    Tools and support for family members<br>
                    Website: <a href="https://smartrecovery.org/family" target="_blank">smartrecovery.org/family</a></p>
                </div>
            </div>

            <div class="alert alert-info">
                <h4><strong>Additional Online Resources</strong></h4>
                <ul class="styled-list">
                    <li><strong>National Institute on Drug Abuse:</strong> drugabuse.gov - Research and information</li>
                    <li><strong>Substance Abuse and Mental Health Services Administration:</strong> samhsa.gov - Government resources</li>
                    <li><strong>Families Anonymous:</strong> familiesanonymous.org - Support for families of addicted individuals</li>
                    <li><strong>Learn About SAM:</strong> learnaboutsam.org - Science-based addiction information</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <div class="container">
        <div class="cta-section">
            <div class="cta-content">
                <h2>Get Professional Support Today</h2>
                <p>
                    Supporting a family member or friend through addiction recovery is challenging, but you don't have to navigate this journey alone. Professional guidance can make all the difference for both you and your loved one. Our family support services help everyone heal together during the recovery process with expert guidance and compassionate care.
                </p>
                <div class="cta-buttons">
                    <a href="tel:8046550094" class="btn btn--primary">(*************</a>
                    <a href="#immediate-help" class="btn btn--secondary">Get Immediate Help</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Active navigation pill highlighting
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('section[id]');
            const navPills = document.querySelectorAll('.nav-pill');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                if (scrollY >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            navPills.forEach(pill => {
                pill.classList.remove('active');
                if (pill.getAttribute('href') === `#${current}`) {
                    pill.classList.add('active');
                }
            });
        });

        // Add scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all content sections
        document.querySelectorAll('.content-section, .info-card').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    </script>
</body>
</html>
